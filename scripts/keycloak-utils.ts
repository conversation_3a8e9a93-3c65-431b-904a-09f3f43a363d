import axios, { AxiosResponse } from 'axios';

const KEYCLOAK_URL = 'http://localhost:8080';
const REALM = 'master'; // Realm padrão do Keycloak, igual ao docker-compose.yml
const ADMIN_USER = 'admin';
const ADMIN_PASS = 'admin';
const CLIENT_ID = 'backend-dev-client';
const CLIENT_SECRET = 'myclientsecret';
const TEST_USER = 'testuser';
const TEST_PASS = 'testpass';

// Roles do schema.prisma
const ROLES = [
  'ADMIN',
  'USER',
  'FINANCE_ADMIN',
  'FINANCE_USER',
  'DOCUMENT_ARCHIVER',
  'DOCUMENT_UPLOADER',
  'DOCUMENT_VIEWER',
];

interface TokenResponse {
  access_token: string;
}

async function getAdminToken(): Promise<string> {
  const params = new URLSearchParams();
  params.append('grant_type', 'password');
  params.append('client_id', 'admin-cli');
  params.append('username', ADMIN_USER);
  params.append('password', ADMIN_PASS);
  const res: AxiosResponse<TokenResponse> = await axios.post(
    `${KEYCLOAK_URL}/realms/master/protocol/openid-connect/token`,
    params,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
  );
  return res.data.access_token;
}

async function ensureRealmExists(
  realmName: string,
  token: string,
): Promise<void> {
  const headers = { Authorization: `Bearer ${token}` };
  try {
    await axios.get(`${KEYCLOAK_URL}/admin/realms/${realmName}`, { headers });
    console.log(`Realm '${realmName}' já existe.`);
    return;
  } catch (err) {
    if (
      axios.isAxiosError(err) &&
      err.response &&
      err.response.status === 404
    ) {
      await axios.post(
        `${KEYCLOAK_URL}/admin/realms`,
        {
          realm: realmName,
          enabled: true,
        },
        { headers },
      );
      console.log(`Realm '${realmName}' criado.`);
    } else {
      throw err;
    }
  }
}

async function ensureRoleExists(
  roleName: string,
  token: string,
): Promise<void> {
  const headers = { Authorization: `Bearer ${token}` };
  try {
    await axios.get(`${KEYCLOAK_URL}/admin/realms/${REALM}/roles/${roleName}`, {
      headers,
    });
    console.log(`Role '${roleName}' já existe.`);
    return;
  } catch (err) {
    if (
      axios.isAxiosError(err) &&
      err.response &&
      err.response.status === 404
    ) {
      await axios.post(
        `${KEYCLOAK_URL}/admin/realms/${REALM}/roles`,
        {
          name: roleName,
          description: `Role ${roleName} do domínio`,
        },
        { headers },
      );
      console.log(`Role '${roleName}' criada.`);
    } else {
      throw err;
    }
  }
}

async function ensureAllDomainRoles(token: string): Promise<void> {
  for (const role of ROLES) {
    await ensureRoleExists(role, token);
  }
}

async function validateClientAndUser(
  realm: string,
  clientId: string,
  clientSecret: string,
  username: string,
  password: string,
): Promise<boolean> {
  const params = new URLSearchParams();
  params.append('grant_type', 'password');
  params.append('client_id', clientId);
  params.append('client_secret', clientSecret);
  params.append('username', username);
  params.append('password', password);

  try {
    const res: AxiosResponse<TokenResponse> = await axios.post(
      `${KEYCLOAK_URL}/realms/${realm}/protocol/openid-connect/token`,
      params,
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
    );
    console.log('Validação do client e usuário OK:', res.data);
    return true;
  } catch (err) {
    if (axios.isAxiosError(err) && err.response) {
      console.error('Erro ao validar client/usuário:', err.response.data);
    } else if (err instanceof Error) {
      console.error('Erro ao validar client/usuário:', err.message);
    } else {
      console.error('Erro ao validar client/usuário:', String(err));
    }
    throw new Error(
      'Falha na validação do client ou usuário no Keycloak. Veja o erro acima.',
    );
  }
}

export async function ensureKeycloakSetup(): Promise<void> {
  const token = await getAdminToken();
  const headers = { Authorization: `Bearer ${token}` };

  // 1. Criar realm de teste se não existir
  await ensureRealmExists(REALM, token);

  // 1.1. Criar todas as roles do domínio
  await ensureAllDomainRoles(token);

  // 2. Criar client se não existir
  const clientsRes: AxiosResponse<{ id: string }[]> = await axios.get(
    `${KEYCLOAK_URL}/admin/realms/${REALM}/clients?clientId=${CLIENT_ID}`,
    { headers },
  );
  let clientId: string;
  if (clientsRes.data.length === 0) {
    await axios.post(
      `${KEYCLOAK_URL}/admin/realms/${REALM}/clients`,
      {
        clientId: CLIENT_ID,
        enabled: true,
        secret: CLIENT_SECRET,
        directAccessGrantsEnabled: true,
        serviceAccountsEnabled: true,
        publicClient: false,
        protocol: 'openid-connect',
      },
      { headers },
    );
    // Buscar o id do client criado
    const created: AxiosResponse<{ id: string }[]> = await axios.get(
      `${KEYCLOAK_URL}/admin/realms/${REALM}/clients?clientId=${CLIENT_ID}`,
      { headers },
    );
    clientId = created.data[0].id;
  } else {
    clientId = clientsRes.data[0].id;
    // Atualizar secret se necessário
    await axios.put(
      `${KEYCLOAK_URL}/admin/realms/${REALM}/clients/${clientId}`,
      {
        ...clientsRes.data[0],
        secret: CLIENT_SECRET,
        directAccessGrantsEnabled: true,
        serviceAccountsEnabled: true,
        publicClient: false,
        protocol: 'openid-connect',
      },
      { headers },
    );
  }

  // 3. Criar usuário se não existir
  const usersRes: AxiosResponse<{ id: string }[]> = await axios.get(
    `${KEYCLOAK_URL}/admin/realms/${REALM}/users?username=${TEST_USER}`,
    { headers },
  );
  let userId: string;
  if (usersRes.data.length === 0) {
    await axios.post(
      `${KEYCLOAK_URL}/admin/realms/${REALM}/users`,
      {
        username: TEST_USER,
        enabled: true,
        emailVerified: true,
        email: `${TEST_USER}@test.com`,
      },
      { headers },
    );
    // Buscar o id do usuário criado
    const created: AxiosResponse<{ id: string }[]> = await axios.get(
      `${KEYCLOAK_URL}/admin/realms/${REALM}/users?username=${TEST_USER}`,
      { headers },
    );
    userId = created.data[0].id;
  } else {
    userId = usersRes.data[0].id;
    // Atualizar o campo email se necessário
    await axios.put(
      `${KEYCLOAK_URL}/admin/realms/${REALM}/users/${userId}`,
      {
        email: `${TEST_USER}@test.com`,
        emailVerified: true,
      },
      { headers },
    );
  }

  // 4. Definir senha do usuário
  await axios.put(
    `${KEYCLOAK_URL}/admin/realms/${REALM}/users/${userId}/reset-password`,
    {
      type: 'password',
      value: TEST_PASS,
      temporary: false,
    },
    { headers },
  );

  // 5. Validar client e usuário
  await validateClientAndUser(
    REALM,
    CLIENT_ID,
    CLIENT_SECRET,
    TEST_USER,
    TEST_PASS,
  );
}

if (require.main === module) {
  ensureKeycloakSetup().catch((err: unknown) => {
    if (err instanceof Error) {
      console.error('Erro ao configurar Keycloak:', err.message);
    } else {
      console.error('Erro ao configurar Keycloak:', String(err));
    }
    process.exit(1);
  });
}
