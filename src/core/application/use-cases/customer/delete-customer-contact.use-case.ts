import { CustomerContactRepositoryPort } from "@/core/ports/repositories/customer-contact-repository.port";
import { Injectable, Inject } from '@nestjs/common';

@Injectable()
export class DeleteCustomerContactUseCase {
  constructor(
    @Inject('CustomerContactRepository')
    private readonly customerContactRepository: CustomerContactRepositoryPort,
  ) {}

  async execute(id: string): Promise<void> {
    await this.customerContactRepository.deleteById(id)
  }
}