import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';
import { SectorRepositoryPort } from '@/core/ports/repositories/sector-repository.port';
import { Sector } from '@/core/domain/entities/sector.entity';

export type UpdateSectorInput = Partial<{
  code: string;
  description: string;
}> & {
  updatedBy: string;
};

@Injectable()
export class UpdateSectorUseCase {
  constructor(
    @Inject(SECTOR_REPOSITORY)
    private readonly sectorRepository: SectorRepositoryPort,
  ) {}

  async execute(uuid: string, updateData: UpdateSectorInput): Promise<Sector> {
    const existingSector = await this.sectorRepository.findByUuid(uuid);

    if (!existingSector) {
      throw new NotFoundException('Setor não encontrado.');
    }

    const updatedSector = Sector.create(
      existingSector.id,
      existingSector.uuid,
      updateData.code ?? existingSector.code,
      updateData.description ?? existingSector.description,
      existingSector.createdBy,
      updateData.updatedBy ?? existingSector.updatedBy,
      existingSector.createdAt,
      new Date(),
    );

    await this.sectorRepository.update(updatedSector);

    return updatedSector;
  }
}
