import { Injectable, Inject } from '@nestjs/common';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';
import {
  SectorRepositoryPort,
  ListSectorsCriteria,
  ListSectorsResult,
} from '@/core/ports/repositories/sector-repository.port';

export interface ListSectorsInput {
  limit: number;
  offset: number;
  code?: string;
  description?: string;
}

@Injectable()
export class ListSectorsUseCase {
  constructor(
    @Inject(SECTOR_REPOSITORY)
    private readonly sectorRepository: SectorRepositoryPort,
  ) {}

  async execute(input: ListSectorsInput): Promise<ListSectorsResult> {
    const criteria: ListSectorsCriteria = {
      limit: input.limit,
      offset: input.offset,
      code: input.code,
      description: input.description,
    };

    return this.sectorRepository.list(criteria);
  }
}
