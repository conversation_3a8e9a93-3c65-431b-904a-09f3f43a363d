import { Test, TestingModule } from '@nestjs/testing';
import { CreateSupplierContactUseCase } from './create-supplier-contact.use-case';
import { SupplierContactRepositoryPort } from '../../../ports/repositories/supplier-contact-repository.port';
import { SupplierContact } from '../../../domain/supplier/entities/supplier-contact.entity';

describe('CreateSupplierContactUseCase', () => {
  let useCase: CreateSupplierContactUseCase;
  let repository: jest.Mocked<SupplierContactRepositoryPort>;

  const mockContactData = {
    contact: '<EMAIL>',
    type: 'email',
    area: 'Finance',
    responsible: '<PERSON>',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    deletedAt: null,
  };

  const supplierId = 'supplier-uuid';
  const mockContact = new SupplierContact({
    id: 'contact-id',
    supplierId,
    ...mockContactData,
  });

  beforeEach(async () => {
    const mockRepository = {
      create: jest.fn(),
      findBySupplierId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateSupplierContactUseCase,
        {
          provide: 'SupplierContactRepository',
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateSupplierContactUseCase>(CreateSupplierContactUseCase);
    repository = module.get('SupplierContactRepository');
  });

  it('should create a new supplier contact successfully', async () => {
    repository.create.mockResolvedValue(mockContact);
    const result = await useCase.execute(supplierId, mockContactData);
    expect(repository.create).toHaveBeenCalledWith({ ...mockContactData, supplierId });
    expect(result).toEqual(mockContact);
  });

  it('should throw if repository.create throws', async () => {
    repository.create.mockRejectedValue(new Error('DB error'));
    await expect(useCase.execute(supplierId, mockContactData)).rejects.toThrow('DB error');
  });
}); 