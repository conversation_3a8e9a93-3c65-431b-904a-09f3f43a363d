import { Injectable, Inject } from '@nestjs/common';
import { SupplierContact } from '../../../domain/supplier/entities/supplier-contact.entity';
import { SupplierContactRepositoryPort } from '../../../ports/repositories/supplier-contact-repository.port';

@Injectable()
export class CreateSupplierContactUseCase {
  constructor(
    @Inject('SupplierContactRepository')
    private readonly supplierContactRepository: SupplierContactRepositoryPort,
  ) { }

  async execute(supplierId: string, contactData: Omit<SupplierContact, 'id' | 'supplierId'>): Promise<SupplierContact> {
    const contact = await this.supplierContactRepository.create({
      ...contactData,
      supplierId,
    });
    return contact;
  }
} 