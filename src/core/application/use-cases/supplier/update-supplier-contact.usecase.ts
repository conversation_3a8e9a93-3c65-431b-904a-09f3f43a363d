import { Injectable, Inject } from '@nestjs/common';
import { SupplierContact } from '../../../domain/supplier/entities/supplier-contact.entity';
import { SupplierContactRepositoryPort } from '../../../ports/repositories/supplier-contact-repository.port';

@Injectable()
export class UpdateSupplierContactUseCase {
  constructor(
    @Inject('SupplierContactRepository')
    private readonly supplierContactRepository: SupplierContactRepositoryPort,
  ) { }

  async execute(uuid: string, data: Partial<Omit<SupplierContact, 'id' | 'supplierId' | 'updatedAt' | 'createdAt' | 'deletedAt'>>): Promise<SupplierContact> {
    const contact = await this.supplierContactRepository.update(uuid, data);
    return contact;
  }
} 