import { Test, TestingModule } from '@nestjs/testing';
import { UpdateSupplierUseCase } from './update-supplier.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '../../../domain/supplier/enums/supplier-classification.enum';

describe('UpdateSupplierUseCase', () => {
  let useCase: UpdateSupplierUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    'test-id',
    'Test Supplier',
    '**************',
    'Test Trade',
    new Address('Test Street', null, null, null, 'Test City', '12345-678', 'SP'),
    '<EMAIL>',
    SupplierClassification.GENERAL,
    SupplierType.BANK,
    SupplierStatus.ACTIVE,
    'user-id',
    'user-id',
    new Date(),
    new Date(),
    'user-id',
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateSupplierUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateSupplierUseCase>(UpdateSupplierUseCase);
    repository =
      module.get<jest.Mocked<SupplierRepositoryPort>>(SUPPLIER_REPOSITORY);
  });

  describe('execute', () => {
    it('should update supplier when user is authorized', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const update = jest.spyOn(repository, 'update');

      // Act
      const result = await useCase.execute(
        'test-id',
        { tradeName: 'New Trade Name', type: SupplierType.BANK },
        'user-id',
      );

      // Assert
      expect(result.tradeName).toBe('New Trade Name');
      expect(update).toHaveBeenCalledTimes(1);
    });

    it('should throw NotFoundException when supplier does not exist', async () => {
      // Arrange
      repository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(
        useCase.execute('non-existent-id', { type: SupplierType.BANK }, 'user-id'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should update all supplier fields successfully', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        name: 'New Name',
        cnpj: '**************',
        tradeName: 'New Trade Name',
        address: {
          street: 'New Street',
          city: 'New City',
          zipCode: '98765-432',
          state: 'RJ',
        },
        email: '<EMAIL>',
        status: SupplierStatus.INACTIVE,
        type: SupplierType.BANK,
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.name).toBe(updateData.name);
      expect(result.document).toBe(updateData.cnpj);
      expect(result.tradeName).toBe(updateData.tradeName);
      expect(result.address.street).toBe(updateData.address.street);
      expect(result.address.city).toBe(updateData.address.city);
      expect(result.address.zipCode).toBe(updateData.address.zipCode);
      expect(result.address.state).toBe(updateData.address.state);
      expect(result.email).toBe(updateData.email);
      expect(result.status).toBe(updateData.status);
    });

    it('should throw ConflictException when CNPJ already exists', async () => {
      // Arrange
      const existingSupplier = new Supplier(
        'other-id',
        'Other Supplier',
        '**************',
        'Other Trade',
        new Address('Other Street', null, null, null, 'Other City', '54321-876', 'RJ'),
        '<EMAIL>',
        SupplierClassification.GENERAL,
        SupplierType.BANK,
        SupplierStatus.ACTIVE,
        'user-id',
        'user-id',
        new Date(),
        new Date(),
        'user-id',
      );

      repository.findById.mockResolvedValue(mockSupplier);
      repository.findByDocument.mockResolvedValue(existingSupplier);

      // Act & Assert
      await expect(
        useCase.execute('test-id', { cnpj: '**************', type: SupplierType.BANK }, 'user-id'),
      ).rejects.toThrow(ConflictException);
    });

    it('should update only address when only address is provided', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        address: {
          street: 'New Street',
          city: 'New City',
          zipCode: '98765-432',
          state: 'RJ',
        },
        type: SupplierType.BANK,
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.address.street).toBe(updateData.address.street);
      expect(result.address.city).toBe(updateData.address.city);
      expect(result.address.zipCode).toBe(updateData.address.zipCode);
      expect(result.address.state).toBe(updateData.address.state);
      expect(result.name).toBe(mockSupplier.name);
      expect(result.document).toBe(mockSupplier.document);
      expect(result.tradeName).toBe(mockSupplier.tradeName);
    });

    it('should update only contact when only contact is provided', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        email: '<EMAIL>',
        type: SupplierType.BANK,
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.email).toBe(updateData.email);
      expect(result.name).toBe(mockSupplier.name);
      expect(result.document).toBe(mockSupplier.document);
      expect(result.tradeName).toBe(mockSupplier.tradeName);
    });

    it('should update only status when only status is provided', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        status: SupplierStatus.INACTIVE,
        type: SupplierType.BANK,
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.status).toBe(updateData.status);
      expect(result.name).toBe(mockSupplier.name);
      expect(result.document).toBe(mockSupplier.document);
      expect(result.tradeName).toBe(mockSupplier.tradeName);
    });
  });
});
