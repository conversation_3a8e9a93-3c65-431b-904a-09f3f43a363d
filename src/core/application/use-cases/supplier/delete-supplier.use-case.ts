import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';

export interface DeleteSupplierInput {
  uuid: string;
}

@Injectable()
export class DeleteSupplierUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
  ) {}

  async execute(input: DeleteSupplierInput): Promise<void> {
    const existingSupplier = await this.supplierRepository.findById(input.uuid);

    if (!existingSupplier) {
      throw new NotFoundException('Fornecedor não encontrado');
    }

    await this.supplierRepository.delete(input.uuid);
  }
}
