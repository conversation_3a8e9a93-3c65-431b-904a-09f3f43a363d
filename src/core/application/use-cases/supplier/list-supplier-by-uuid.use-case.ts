import { Injectable, Inject } from '@nestjs/common';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { NotFoundException } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';

@Injectable()
export class ListSupplierByUuidUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
  ) {}

  async execute(uuid: string): Promise<Supplier> {
    const existingSupplier = await this.supplierRepository.findById(uuid);

    if (!existingSupplier) {
      throw new NotFoundException('Fornecedor não encontrado');
    }

    return existingSupplier;
  }
}
