import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { UnauthorizedError } from '../../../domain/errors/unauthorized.error';

@Injectable()
export class UpdateSupplierStatusUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
  ) {}

  async execute(
    id: string,
    status: SupplierStatus,
    userId: string,
  ): Promise<Supplier> {
    // Buscar o fornecedor existente
    const existingSupplier = await this.supplierRepository.findById(id);
    if (!existingSupplier) {
      throw new NotFoundException('Supplier not found');
    }

    // Verificar se o usuário é o criador do fornecedor
    if (existingSupplier.createdBy !== userId) {
      throw new UnauthorizedError(
        'Only the creator can update supplier status',
      );
    }
    // Criar uma nova instância do fornecedor com o status atualizado
    const updatedSupplier = new Supplier(
      existingSupplier.id,
      existingSupplier.name,
      existingSupplier.document,
      existingSupplier.tradeName,
      existingSupplier.address,
      existingSupplier.email,
      existingSupplier.classification,
      existingSupplier.type,
      status,
      existingSupplier.userId,
      existingSupplier.createdBy,
      existingSupplier.createdAt,
      new Date(),
      userId,
      existingSupplier.stateRegistration,
      existingSupplier.municipalRegistration,
      existingSupplier.taxRegime,
      existingSupplier.companySize,
    );

    // Persistir as alterações
    return this.supplierRepository.update(updatedSupplier);
  }
}
