import { Test, TestingModule } from '@nestjs/testing';
import { ListSupplierContactsUseCase } from './list-supplier-contacts.use-case';
import { SupplierContactRepositoryPort } from '../../../ports/repositories/supplier-contact-repository.port';
import { SupplierContact } from '../../../domain/supplier/entities/supplier-contact.entity';


describe('ListSupplierContactsUseCase', () => {
  let useCase: ListSupplierContactsUseCase;
  let repository: jest.Mocked<SupplierContactRepositoryPort>;

  const mockSupplierContacts: SupplierContact[] = [
    new SupplierContact({
      id: 'contact-1',
      supplierId: 'supplier-1',
      contact: '<EMAIL>',
      type: 'EMAIL',
      area: 'Finance',
      responsible: '<PERSON>',
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      deletedAt: null,
    }),
    new SupplierContact({
      id: 'contact-2',
      supplierId: 'supplier-1',
      contact: '<EMAIL>',
      type: 'PHONE',
      area: 'Support',
      responsible: '<PERSON>e',
      createdAt: new Date('2024-01-02T00:00:00Z'),
      updatedAt: new Date('2024-01-02T00:00:00Z'),
      deletedAt: null,
    }),
  ];

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierContactRepositoryPort> = {
      create: jest.fn(),
      findBySupplierId: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListSupplierContactsUseCase,
        {
          provide: 'SupplierContactRepository',
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListSupplierContactsUseCase>(ListSupplierContactsUseCase);
    repository = module.get('SupplierContactRepository');
  });

  describe('execute', () => {
    it('should return a list of supplier contacts for a given supplierId', async () => {
      repository.findBySupplierId.mockResolvedValue(mockSupplierContacts);

      const result = await useCase.execute('supplier-1');

      expect(repository.findBySupplierId).toHaveBeenCalledWith('supplier-1');
      expect(result).toEqual(mockSupplierContacts);
    });

    it('should return an empty array if no contacts are found', async () => {
      repository.findBySupplierId.mockResolvedValue([]);

      const result = await useCase.execute('supplier-2');

      expect(repository.findBySupplierId).toHaveBeenCalledWith('supplier-2');
      expect(result).toEqual([]);
    });
  });
}); 