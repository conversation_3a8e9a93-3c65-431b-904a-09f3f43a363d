import { Test, TestingModule } from '@nestjs/testing';
import { GetSupplierByUserIdUsecase } from './get-supplier-by-user-id.use-case';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '../../../domain/supplier/enums/supplier-classification.enum';
import { NotFoundException } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';

describe('GetSupplierByUserIdUsecase', () => {
  let useCase: GetSupplierByUserIdUsecase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const userId = 'user-uuid';
  const mockSupplier = new Supplier(
    'supplier-id',
    'Supplier Name',
    '12345678901234',
    'Trade Name',
    new Address('Street', null, null, null, 'City', '12345-678', 'ST'),
    '<EMAIL>',
    SupplierClassification.CORE,
    SupplierType.GAME,
    SupplierStatus.ACTIVE,
    userId,
    'created-by',
    new Date('2024-01-01T00:00:00Z'),
    new Date('2024-01-01T00:00:00Z'),
    'updated-by',
  );

  beforeEach(async () => {
    const mockRepository = {
      findByUserId: jest.fn(),
      create: jest.fn(),
      findByDocument: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetSupplierByUserIdUsecase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    })
      .overrideProvider(SUPPLIER_REPOSITORY)
      .useValue(mockRepository)
      .compile();

    useCase = module.get<GetSupplierByUserIdUsecase>(GetSupplierByUserIdUsecase);
    repository = module.get(SUPPLIER_REPOSITORY);
  });

  it('should return a supplier by userId', async () => {
    repository.findByUserId.mockResolvedValue(mockSupplier);
    const result = await useCase.execute(userId);
    expect(repository.findByUserId).toHaveBeenCalledWith(userId);
    expect(result).toEqual(mockSupplier);
  });

  it('should throw NotFoundException if supplier does not exist', async () => {
    repository.findByUserId.mockResolvedValue(null);
    await expect(useCase.execute(userId)).rejects.toThrow(NotFoundException);
  });
}); 