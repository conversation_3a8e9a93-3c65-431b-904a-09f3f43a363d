import { Test, TestingModule } from '@nestjs/testing';
import { UpdateSupplierStatusUseCase } from './update-supplier-status.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '../../../domain/supplier/enums/supplier-classification.enum';
import { NotFoundException } from '@nestjs/common';
import { UnauthorizedError } from '../../../domain/errors/unauthorized.error';

describe('UpdateSupplierStatusUseCase', () => {
  let useCase: UpdateSupplierStatusUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    'supplier-id',
    'Supplier Name',
    '12345678901234',
    'Trade Name',
    new Address('Street', '123', 'Apt 1', 'Centro', 'City', '12345-678', 'ST'),
    '<EMAIL>',
    SupplierClassification.CORE,
    SupplierType.GAME,
    SupplierStatus.PENDING,
    'user-id',
    'user-id',
    new Date('2024-01-01T00:00:00Z'),
    new Date('2024-01-01T00:00:00Z'),
    'user-id',
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      create: jest.fn(),
      findByUserId: jest.fn(),
      findByDocument: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateSupplierStatusUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateSupplierStatusUseCase>(UpdateSupplierStatusUseCase);
    repository = module.get(SUPPLIER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    const userId = 'user-id';
    const otherUserId = 'other-user-id';
    const supplierId = 'supplier-id';
    const newStatus = SupplierStatus.ACTIVE;

    it('should update supplier status successfully if user is creator', async () => {
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation(async (supplier) => supplier);

      const result = await useCase.execute(supplierId, newStatus, userId);

      expect(repository.findById).toHaveBeenCalledWith(supplierId);
      expect(repository.update).toHaveBeenCalled();
      expect(result.status).toBe(newStatus);
      expect(result.updatedBy).toBe(userId);
    });

    it('should throw NotFoundException if supplier does not exist', async () => {
      repository.findById.mockResolvedValue(null);
      await expect(
        useCase.execute(supplierId, newStatus, userId)
      ).rejects.toThrow(NotFoundException);
      expect(repository.findById).toHaveBeenCalledWith(supplierId);
      expect(repository.update).not.toHaveBeenCalled();
    });

    it('should throw UnauthorizedError if user is not the creator', async () => {
      const supplierWithOtherCreator = new Supplier(
        mockSupplier.id,
        mockSupplier.name,
        mockSupplier.document,
        mockSupplier.tradeName,
        mockSupplier.address,
        mockSupplier.email,
        mockSupplier.classification,
        mockSupplier.type,
        mockSupplier.status,
        mockSupplier.userId,
        otherUserId, // createdBy diferente
        mockSupplier.createdAt,
        mockSupplier.updatedAt,
        mockSupplier.updatedBy,
      );
      repository.findById.mockResolvedValue(supplierWithOtherCreator);
      await expect(
        useCase.execute(supplierId, newStatus, userId)
      ).rejects.toThrow(UnauthorizedError);
      expect(repository.findById).toHaveBeenCalledWith(supplierId);
      expect(repository.update).not.toHaveBeenCalled();
    });
  });
}); 