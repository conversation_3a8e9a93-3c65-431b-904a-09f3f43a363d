import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { ListEmployeeByUuidUseCase } from './list-employee-by-uuid.use-case';
import {
  EMPLOYEE_REPOSITORY,
  EmployeeRepositoryPort,
} from '@/core/ports/repositories/employee-repository.port';
import { Employee } from '@/core/domain/entities/employee.entity';
import { EmployeeStatus } from '@prisma/client';

describe('ListEmployeeByUuidUseCase', () => {
  let useCase: ListEmployeeByUuidUseCase;
  let repository: jest.Mocked<EmployeeRepositoryPort>;

  const mockEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-************',
    '<PERSON>',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [],
    EmployeeStatus.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<EmployeeRepositoryPort> = {
      findById: jest.fn(),
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByEmail: jest.fn(),
      findPersonalDocument: jest.fn(),
      uploadPersonalDocument: jest.fn(),
      deletePersonalDocument: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListEmployeeByUuidUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListEmployeeByUuidUseCase>(ListEmployeeByUuidUseCase);
    repository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  describe('execute', () => {
    it('should return employee when found by UUID', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      findByUuid.mockResolvedValue(mockEmployee);

      const result = await useCase.execute('test-id');

      expect(result).toBeDefined();
      expect(result).toEqual(mockEmployee);
      expect(findByUuid).toHaveBeenCalledWith('test-id');
    });

    it('should throw NotFoundException when payables type does not exist', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      findByUuid.mockResolvedValue(null);

      await expect(useCase.execute('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
      expect(findByUuid).toHaveBeenCalledWith('non-existent-id');
    });
  });
});
