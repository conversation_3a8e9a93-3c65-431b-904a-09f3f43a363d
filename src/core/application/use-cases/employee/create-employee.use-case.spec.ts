import { Test, TestingModule } from '@nestjs/testing';
import { CreateEmployeeUseCase } from './create-employee.use-case';
import {
  EmployeeRepositoryPort,
  EMPLOYEE_REPOSITORY,
} from '@/core/ports/repositories/employee-repository.port';
import { ConflictException } from '@nestjs/common';
import { Employee } from '@/core/domain/entities/employee.entity';
import { CreateEmployeeDto } from '@/modules/finance/employee/dto/create-employee.dto';
import {
  ContractType,
  Seniority,
  Shift,
} from '@/core/domain/enums/employee.enum';
import { EmployeeStatus } from '@prisma/client';
import { UsersService } from '@/modules/users/users.service';
import { AuthService } from '@/modules/auth/auth.service';
import { User } from '@/core/domain/user.entity';
import { Role } from '@/core/domain/role.enum';

describe('CreateEmployeeUseCase', () => {
  let useCase: CreateEmployeeUseCase;
  let repository: jest.Mocked<EmployeeRepositoryPort>;
  let usersService: jest.Mocked<UsersService>;
  let authService: jest.Mocked<AuthService>;

  const mockWorkSchedule = {
    weekDays: [1, 2, 3, 4, 5],
    startingHour: '08:00',
    endingHour: '17:00',
  };

  const mockVacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const mockEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-426614174000',
    'John Doe',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [
      {
        name: 'Jane Doe',
        kinship: 'Spouse',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
    EmployeeStatus.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
    mockWorkSchedule,
    Shift.MORNING,
    5000.0,
    500.0,
    300.0,
    'Unimed',
    ContractType.PJ,
    Seniority.SENIOR,
    '***********',
    new Date('1990-01-01'),
    '08:00-17:00',
    true,
    [mockVacation],
    'user-id-123', // userId
  );

  const employeeWithoutOptionals = Employee.create(
    2,
    'uuid-456',
    'John Doe',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [
      {
        name: 'Jane Doe',
        kinship: 'Spouse',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
    EmployeeStatus.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    'user-id-456', // userId
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<EmployeeRepositoryPort> = {
      create: jest.fn(),
      findByEmail: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findByUuid: jest.fn(),
      findAll: jest.fn(),
      findWithPagination: jest.fn(),
      findPersonalDocument: jest.fn(),
      uploadPersonalDocument: jest.fn(),
      deletePersonalDocument: jest.fn(),
    };

    usersService = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      findAll: jest.fn(),
      findById: jest.fn(),
      findByKeycloakId: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    } as any;

    authService = {
      forgotPassword: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateEmployeeUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
        {
          provide: UsersService,
          useValue: usersService,
        },
        {
          provide: AuthService,
          useValue: authService,
        },
      ],
    }).compile();

    useCase = module.get<CreateEmployeeUseCase>(CreateEmployeeUseCase);
    repository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    const createEmployeeDto: Omit<
      CreateEmployeeDto,
      'createdBy' | 'updatedBy'
    > = {
      name: 'John Doe',
      email: '<EMAIL>',
      position: 'Software Engineer',
      department: 'Engineering',
      hireDate: new Date().toISOString(),
      address: {
        street: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        zipCode: '12345',
        number: '123',
        neighborhood: 'Downtown',
        complement: 'Apt 1',
      },
      personalDocuments: [
        {
          type: 'CPF',
          number: '12345678900',
        },
      ],
      dependents: [
        {
          name: 'Jane Doe',
          kinship: 'Spouse',
          isTaxDependent: true,
          hasHealthPlan: true,
        },
      ],
      status: EmployeeStatus.ACTIVE,
      workSchedule: mockWorkSchedule,
      shift: Shift.MORNING,
      grossSalary: 5000.0,
      mealAllowance: 500.0,
      transportAllowance: 300.0,
      healthPlan: 'Unimed',
      contractType: ContractType.PJ,
      seniority: Seniority.SENIOR,
      phone: '***********',
      birthDate: new Date('1990-01-01').toISOString(),
      workHours: '08:00-17:00',
      overtimeBank: true,
      vacations: [mockVacation],
    };

    it('should create a new employee successfully', async () => {
      usersService.findByEmail.mockRejectedValue(new Error('não encontrado'));
      const mockUser = new User(
        'user-id-123',
        createEmployeeDto.email,
        createEmployeeDto.name,
        'sut@t6@LuhKX29*C',
        Role.EMPLOYEE,
      );
      usersService.create.mockResolvedValue(mockUser);
      repository.create.mockResolvedValue(mockEmployee);
      authService.forgotPassword.mockResolvedValue();

      const result = await useCase.execute(
        createEmployeeDto as CreateEmployeeDto,
      );

      expect(result).toEqual(mockEmployee);
      expect(usersService.findByEmail).toHaveBeenCalledWith(createEmployeeDto.email);
      expect(usersService.create).toHaveBeenCalledWith({
        name: createEmployeeDto.name,
        email: createEmployeeDto.email,
        password: 'sut@t6@LuhKX29*C',
        role: Role.EMPLOYEE,
      });
      expect(authService.forgotPassword).toHaveBeenCalledWith({ email: createEmployeeDto.email });
      expect(result.workSchedule).toEqual(mockWorkSchedule);
      expect(result.shift).toBe(Shift.MORNING);
      expect(result.grossSalary).toBe(5000.0);
      expect(result.mealAllowance).toBe(500.0);
      expect(result.transportAllowance).toBe(300.0);
      expect(result.healthPlan).toBe('Unimed');
      expect(result.contractType).toBe(ContractType.PJ);
      expect(result.seniority).toBe(Seniority.SENIOR);
      expect(result.phone).toBe('***********');
      expect(result.birthDate).toEqual(new Date('1990-01-01'));
      expect(result.workHours).toBe('08:00-17:00');
      expect(result.overtimeBank).toBe(true);
      expect(result.vacations).toEqual([mockVacation]);
    });

    it('should throw ConflictException if email already exists', async () => {
      usersService.findByEmail.mockResolvedValue({} as any);

      await expect(
        useCase.execute(createEmployeeDto as CreateEmployeeDto),
      ).rejects.toThrow(ConflictException);
    });

    it('should create employee with optional fields', async () => {
      usersService.findByEmail.mockRejectedValue(new Error('não encontrado'));
      const mockUser = new User(
        'user-id-456',
        createEmployeeDto.email,
        createEmployeeDto.name,
        'sut@t6@LuhKX29*C',
        Role.EMPLOYEE,
      );
      usersService.create.mockResolvedValue(mockUser);
      repository.create.mockResolvedValue(employeeWithoutOptionals);
      authService.forgotPassword.mockResolvedValue();

      const dtoWithoutOptionals = {
        ...createEmployeeDto,
        workSchedule: undefined,
        shift: undefined,
        grossSalary: undefined,
        mealAllowance: undefined,
        transportAllowance: undefined,
        healthPlan: undefined,
        contractType: undefined,
        seniority: undefined,
        phone: undefined,
        birthDate: undefined,
        workHours: undefined,
        overtimeBank: undefined,
        vacations: undefined,
      };

      const result = await useCase.execute(dtoWithoutOptionals);

      expect(result.workSchedule).toBeUndefined();
      expect(result.shift).toBeUndefined();
      expect(result.grossSalary).toBeUndefined();
      expect(result.mealAllowance).toBeUndefined();
      expect(result.transportAllowance).toBeUndefined();
      expect(result.healthPlan).toBeUndefined();
      expect(result.contractType).toBeUndefined();
      expect(result.seniority).toBeUndefined();
      expect(result.phone).toBeUndefined();
      expect(result.birthDate).toBeUndefined();
      expect(result.workHours).toBeUndefined();
      expect(result.overtimeBank).toBeUndefined();
      expect(result.vacations).toBeUndefined();
      expect(usersService.findByEmail).toHaveBeenCalledWith(dtoWithoutOptionals.email);
      expect(usersService.create).toHaveBeenCalledWith({
        name: dtoWithoutOptionals.name,
        email: dtoWithoutOptionals.email,
        password: 'sut@t6@LuhKX29*C',
        role: Role.EMPLOYEE,
      });
      expect(authService.forgotPassword).toHaveBeenCalledWith({ email: dtoWithoutOptionals.email });
    });
  });
});
