import { EMPLOYEE_REPOSITORY, EmployeeRepositoryPort } from "@/core/ports/repositories/employee-repository.port";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { Inject, Injectable, NotFoundException } from "@nestjs/common";

@Injectable()
export class DeleteEmployeePersonalDocumentUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(uuid: string, number: string): Promise<void> {
    const personalDocument = await this.employeeRepository.findPersonalDocument(uuid, number);

    if (!personalDocument) throw new NotFoundException('Personal document not found');

    if (personalDocument.filePath) {
     await this.storageProvider.delete(personalDocument.filePath);
  }

    return await this.employeeRepository.deletePersonalDocument(uuid, number);
  }
}