import { Test, TestingModule } from '@nestjs/testing';
import { ListEmployeeUseCase } from './list-employee.use-case';
import {
  EMPLOYEE_REPOSITORY,
  EmployeeRepositoryPort,
} from '@/core/ports/repositories/employee-repository.port';
import { Employee } from '@/core/domain/entities/employee.entity';
import { EmployeeStatus } from '@prisma/client';

describe('ListEmployeeUseCase', () => {
  let useCase: ListEmployeeUseCase;
  let mockEmployeeRepository: jest.Mocked<EmployeeRepositoryPort>;

  const mockEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-426614174000',
    '<PERSON>',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [],
    EmployeeStatus.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<EmployeeRepositoryPort> = {
      findWithPagination: jest.fn(),
      findById: jest.fn(),
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findByEmail: jest.fn(),
      findPersonalDocument: jest.fn(),
      uploadPersonalDocument: jest.fn(),
      deletePersonalDocument: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListEmployeeUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListEmployeeUseCase>(ListEmployeeUseCase);
    mockEmployeeRepository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should return paginated employees without filters', async () => {
      const mockResult = {
        items: [mockEmployee],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockEmployeeRepository,
        'findWithPagination',
      );

      mockEmployeeRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      });
    });

    it('should return paginated employees with name filter', async () => {
      const mockResult = {
        items: [mockEmployee],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockEmployeeRepository,
        'findWithPagination',
      );

      mockEmployeeRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        name: 'John Doe',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        name: 'John Doe',
      });
    });

    it('should return paginated employees with email filter', async () => {
      const mockResult = {
        items: [mockEmployee],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockEmployeeRepository,
        'findWithPagination',
      );

      mockEmployeeRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        email: '<EMAIL>',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        email: '<EMAIL>',
      });
    });

    it('should return paginated suppliers with both filters', async () => {
      const mockResult = {
        items: [mockEmployee],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockEmployeeRepository,
        'findWithPagination',
      );

      mockEmployeeRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        name: 'John Doe',
        email: '<EMAIL>',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        name: 'John Doe',
        email: '<EMAIL>',
      });
    });
  });
});
