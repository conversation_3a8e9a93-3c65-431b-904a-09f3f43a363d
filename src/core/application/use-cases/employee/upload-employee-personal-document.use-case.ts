import { EMPLOYEE_REPOSITORY, EmployeeRepositoryPort } from "@/core/ports/repositories/employee-repository.port";
import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { PersonalDocument } from "@/core/domain/entities/employee.entity";

export interface PersonalDocumentWithUrl extends PersonalDocument {
  downloadUrl: string;
}

@Injectable()
export class UploadEmployeePersonalDocumentUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(uuid: string, number: string, file: Express.Multer.File): Promise<PersonalDocumentWithUrl> {
    if (!file) {
      throw new BadRequestException('Arquivo não encontrado.');
    }
    const versionId = 1;
    const key = `employees/${uuid}/${number}/${file.originalname}/v${versionId}/${file.originalname}`;
    await this.storageProvider.upload(file.buffer, file.mimetype, key);
    const downloadUrl = await this.storageProvider.getDownloadUrl(key, file.originalname, 3600);
    const updatedPersonalDocument = await this.employeeRepository.uploadPersonalDocument(uuid, number, key, file.originalname);
    return {
      ...updatedPersonalDocument,
      downloadUrl,
    };
  }
}