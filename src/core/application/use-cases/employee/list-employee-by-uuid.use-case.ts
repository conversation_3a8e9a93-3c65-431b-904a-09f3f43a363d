import { Injectable, Inject } from '@nestjs/common';
import { NotFoundException } from '@nestjs/common';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import { Employee } from '@/core/domain/entities/employee.entity';

@Injectable()
export class ListEmployeeByUuidUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
  ) {}

  async execute(uuid: string): Promise<Employee> {
    const existingEmployee = await this.employeeRepository.findByUuid(uuid);

    if (!existingEmployee) {
      throw new NotFoundException('Colaborador não encontrado');
    }

    return existingEmployee;
  }
}
