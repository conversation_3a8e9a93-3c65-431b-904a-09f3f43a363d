import { Test, TestingModule } from '@nestjs/testing';
import { UpdateEmployeeUseCase } from './update-employee.use-case';
import {
  EmployeeRepositoryPort,
  EMPLOYEE_REPOSITORY,
} from '@/core/ports/repositories/employee-repository.port';
import { NotFoundException } from '@nestjs/common';
import { Employee } from '@/core/domain/entities/employee.entity';
import {
  ContractType,
  Seniority,
  Shift,
} from '@/core/domain/enums/employee.enum';
import { EmployeeStatus } from '@prisma/client';
import { UpdateEmployeeDto } from '@/modules/finance/employee/dto/update-employee.dto';

describe('UpdateEmployeeUseCase', () => {
  let useCase: UpdateEmployeeUseCase;
  let repository: jest.Mocked<EmployeeRepositoryPort>;

  const mockWorkSchedule = {
    weekDays: [1, 2, 3, 4, 5],
    startingHour: '08:00',
    endingHour: '17:00',
  };

  const mockVacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const existingEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-426614174000',
    'John Doe',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [
      {
        name: 'Jane Doe',
        kinship: 'Spouse',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
    EmployeeStatus.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
    mockWorkSchedule,
    Shift.MORNING,
    5000.0,
    500.0,
    300.0,
    'Unimed',
    ContractType.PJ,
    Seniority.SENIOR,
    '11999999999',
    new Date('1990-01-01'),
    '08:00-17:00',
    true,
    [mockVacation],
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<EmployeeRepositoryPort> = {
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByEmail: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findById: jest.fn(),
      findPersonalDocument: jest.fn(),
      uploadPersonalDocument: jest.fn(),
      deletePersonalDocument: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateEmployeeUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateEmployeeUseCase>(UpdateEmployeeUseCase);
    repository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  describe('execute', () => {
    const userId = 'user-id-from-session';
    it('should update employee when user is authorized', async () => {
      repository.findByUuid.mockResolvedValue(existingEmployee);
      repository.update.mockImplementation((employee) =>
        Promise.resolve(employee),
      );

      const updateDto: UpdateEmployeeDto = {
        name: 'New Name',
        email: '<EMAIL>',
        address: {
          street: '456 Elm St',
        },
        personalDocuments: [
          {
            type: 'RG',
            number: '987654321',
          },
        ],
        dependents: [
          {
            name: 'John Doe Jr.',
            kinship: 'Son',
          },
        ],
        status: EmployeeStatus.INACTIVE,
        shift: Shift.NIGHT,
        contractType: ContractType.PJ,
        seniority: Seniority.JUNIOR,
      };

      const result = await useCase.execute('test-id', updateDto, userId);

      expect(result.name).toBe('New Name');
      expect(result.email).toBe('<EMAIL>');
      expect(result.address.street).toBe('456 Elm St');
      expect(result.address.city).toBe('Anytown');
      expect(result.personalDocuments).toHaveLength(1);
      expect(result.personalDocuments[0].type).toBe('RG');
      expect(result.dependents).toHaveLength(1);
      expect(result.dependents[0].name).toBe('John Doe Jr.');
      expect(result.status).toBe(EmployeeStatus.INACTIVE);
      expect(result.shift).toBe(Shift.NIGHT);
      expect(result.contractType).toBe(ContractType.PJ);
      expect(result.seniority).toBe(Seniority.JUNIOR);
      expect(result.updatedBy).toBe(userId);
    });

    it('should update only provided fields', async () => {
      repository.findByUuid.mockResolvedValue(existingEmployee);
      repository.update.mockImplementation((employee) =>
        Promise.resolve(employee),
      );

      const partialUpdateDto: Partial<UpdateEmployeeDto> = {
        name: 'Only Name Updated',
      };

      const result = await useCase.execute(
        'test-id',
        partialUpdateDto as UpdateEmployeeDto,
        userId,
      );

      expect(result.name).toBe('Only Name Updated');
      expect(result.email).toBe(existingEmployee.email);
      expect(result.seniority).toBe(existingEmployee.seniority);
      expect(result.updatedBy).toBe(userId);
    });

    it('should throw NotFoundException when employee does not exist', async () => {
      repository.findByUuid.mockResolvedValue(null);

      const updateDto: Partial<UpdateEmployeeDto> = {
        name: 'New Name',
      };

      await expect(
        useCase.execute('non-existent-id', updateDto as UpdateEmployeeDto, userId),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
