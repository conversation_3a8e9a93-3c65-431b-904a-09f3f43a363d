import { ARCHIVE_REPOSITORY, ArchiveRepositoryPort } from "@/core/ports/repositories/archive-repository.port";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { v4 as uuidv4 } from 'uuid';
import { Archive } from "@prisma/client";

@Injectable()
export class CreateEmployeeArchiveUseCase {
  constructor(
    @Inject(ARCHIVE_REPOSITORY)
    private readonly archiveRepository: ArchiveRepositoryPort,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(employeeUuid: string, file: Express.Multer.File, userId: string): Promise<Archive> {
    if (!file) {
      throw new BadRequestException('Arquivo não encontrado.');
    }

    const uuid = uuidv4();
    const versionId = 1;
    const key = `archives/${employeeUuid}/${uuid}/v${versionId}/${file.originalname}`;

    await this.storageProvider.upload(file.buffer, file.mimetype, key);

    return this.archiveRepository.create({ employeeUuid, filePath: key, uploadedBy: userId, fileName: file.originalname, createdAt: new Date(), updatedAt: new Date(), deletedAt: null });
  }
}