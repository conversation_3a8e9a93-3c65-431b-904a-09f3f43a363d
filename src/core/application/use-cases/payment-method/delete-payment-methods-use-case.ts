import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';

export interface DeletePaymentMethodInput {
  uuid: string;
}

@Injectable()
export class DeletePaymentMethodUseCase {
  constructor(
    @Inject('PaymentMethodRepository')
    private readonly paymentMethodRepository: PaymentMethodRepository,
  ) {}

  async execute(input: DeletePaymentMethodInput): Promise<void> {
    const existingPaymentMethod = await this.paymentMethodRepository.findByUuid(
      input.uuid,
    );

    if (!existingPaymentMethod) {
      throw new NotFoundException('Método de pagamento não encontrado');
    }

    await this.paymentMethodRepository.delete(input.uuid);
  }
}
