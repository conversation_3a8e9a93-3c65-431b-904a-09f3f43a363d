import { Inject, Injectable } from '@nestjs/common';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { PaymentMethod } from '../../../domain/payment-method';

export interface ListPaymentMethodInput {
  limit: number;
  offset: number;
  label?: string;
  id?: string;
}

export interface PaymentMethodOutput {
  id: number;
  uuid: string;
  label: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export interface ListPaymentMethodOutput {
  paymentMethods: PaymentMethodOutput[];
}

@Injectable()
export class ListPaymentMethodUseCase {
  constructor(
    @Inject('PaymentMethodRepository')
    private readonly paymentMethodRepository: PaymentMethodRepository,
  ) {}

  async execute(
    input: ListPaymentMethodInput,
  ): Promise<{ items: PaymentMethod[]; total: number }> {
    const paymentMethods =
      await this.paymentMethodRepository.findWithPagination(input);

    return paymentMethods;
  }
}
