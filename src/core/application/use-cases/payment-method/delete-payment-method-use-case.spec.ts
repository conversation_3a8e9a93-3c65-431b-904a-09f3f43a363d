import { NotFoundException } from '@nestjs/common';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { DeletePaymentMethodUseCase } from './delete-payment-methods-use-case';
import { PaymentMethod } from '../../../domain/payment-method';

describe('DeletePaymentMethodUseCase', () => {
  let useCase: DeletePaymentMethodUseCase;
  let paymentMethodRepository: jest.Mocked<PaymentMethodRepository>;

  beforeEach(() => {
    const mockRepository: jest.Mocked<PaymentMethodRepository> = {
      findWithPagination: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByLabel: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findByUuid: jest.fn(),
    };
    paymentMethodRepository = mockRepository;

    useCase = new DeletePaymentMethodUseCase(paymentMethodRepository);
  });
  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });
  describe('execute', () => {
    it('should delete a payment method', async () => {
      const mockUuid = '550e8400-e29b-41d4-a716-************';
      const mockPaymentMethod = new PaymentMethod(
        1,
        mockUuid,
        'Test Payment Method',
        'Test Description',
        'Test User',
        new Date(),
        new Date(),
        'Test User',
      );
      const findByUuidMock = jest.spyOn(paymentMethodRepository, 'findByUuid');

      findByUuidMock.mockResolvedValue(mockPaymentMethod);

      const deleteMock = jest.spyOn(paymentMethodRepository, 'delete');

      deleteMock.mockResolvedValue();

      await useCase.execute({ uuid: mockUuid });

      expect(findByUuidMock).toHaveBeenCalledWith(mockUuid);
      expect(deleteMock).toHaveBeenCalledWith(mockUuid);
    });

    it('should throw NotFoundException if payment method does not exist', async () => {
      const mockUuid = '550e8400-e29b-41d4-a716-************';

      jest.spyOn(paymentMethodRepository, 'findByUuid').mockResolvedValue(null);

      await expect(useCase.execute({ uuid: mockUuid })).rejects.toThrowError(
        new NotFoundException('Método de pagamento não encontrado'),
      );
    });
  });
  it('should throw NotFoundException if payment method does not exist', async () => {
    const mockUuid = '550e8400-e29b-41d4-a716-************';

    jest.spyOn(paymentMethodRepository, 'findByUuid').mockResolvedValue(null);

    await expect(useCase.execute({ uuid: mockUuid })).rejects.toThrow(
      new NotFoundException('Método de pagamento não encontrado'),
    );
  });
});
