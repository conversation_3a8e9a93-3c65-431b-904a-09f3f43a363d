import { Injectable, Inject } from '@nestjs/common';
import {
  PAYABLES_TYPE_REPOSITORY,
  PayablesTypeRepositoryPort,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';

@Injectable()
export class ListPayablesTypeUseCase {
  constructor(
    @Inject(PAYABLES_TYPE_REPOSITORY)
    private readonly payableTypeRepository: PayablesTypeRepositoryPort,
  ) {}

  async execute(params: {
    limit: number;
    offset: number;
    code?: string;
    description?: string;
  }): Promise<{ items: PayablesType[]; total: number }> {
    return this.payableTypeRepository.findWithPagination(params);
  }
}
