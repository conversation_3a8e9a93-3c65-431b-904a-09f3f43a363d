import { Test, TestingModule } from '@nestjs/testing';
import { ListPayablesTypeByUuidUseCase } from './list-payable-type-by-uuid.use-case';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '../../../ports/repositories/payables-type-repository.port';
import { PayablesType } from '../../../domain/payables-type/entities/payables-type.entity';
import { NotFoundException } from '@nestjs/common';

describe('ListPayablesTypeByUuidUseCase', () => {
  let useCase: ListPayablesTypeByUuidUseCase;
  let repository: jest.Mocked<PayablesTypeRepositoryPort>;

  const mockPayablesType = new PayablesType(
    'test-id',
    'test-code',
    'test-description',
    'test-created-by',
    'test-updated-by',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<PayablesTypeRepositoryPort> = {
      findById: jest.fn(),
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByCode: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListPayablesTypeByUuidUseCase,
        {
          provide: PAYABLES_TYPE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListPayablesTypeByUuidUseCase>(
      ListPayablesTypeByUuidUseCase,
    );
    repository = module.get<jest.Mocked<PayablesTypeRepositoryPort>>(
      PAYABLES_TYPE_REPOSITORY,
    );
  });

  describe('execute', () => {
    it('should return payables type when found by UUID', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      findByUuid.mockResolvedValue(mockPayablesType);

      const result = await useCase.execute('test-id');

      expect(result).toBeDefined();
      expect(result).toEqual(mockPayablesType);
      expect(findByUuid).toHaveBeenCalledWith('test-id');
    });

    it('should throw NotFoundException when payables type does not exist', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      findByUuid.mockResolvedValue(null);

      await expect(useCase.execute('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
      expect(findByUuid).toHaveBeenCalledWith('non-existent-id');
    });
  });
});
