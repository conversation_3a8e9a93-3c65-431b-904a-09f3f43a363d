import { Test, TestingModule } from '@nestjs/testing';
import { ListPayablesTypeUseCase } from './list-payables-type.use-case';
import {
  PAYABLES_TYPE_REPOSITORY,
  PayablesTypeRepositoryPort,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';

describe('ListPayablesTypeUseCase', () => {
  let useCase: ListPayablesTypeUseCase;
  let mockPayablesTypeRepository: jest.Mocked<PayablesTypeRepositoryPort>;

  const mockPayablesType = new PayablesType(
    '123e4567-e89b-12d3-a456-************',
    'OFFICE',
    'Despesas de Escritório',
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<PayablesTypeRepositoryPort> = {
      findWithPagination: jest.fn(),
      findById: jest.fn(),
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByCode: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListPayablesTypeUseCase,
        {
          provide: PAYABLES_TYPE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListPayablesTypeUseCase>(ListPayablesTypeUseCase);
    mockPayablesTypeRepository = module.get<
      jest.Mocked<PayablesTypeRepositoryPort>
    >(PAYABLES_TYPE_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should return paginated suppliers without filters', async () => {
      const mockResult = {
        items: [mockPayablesType],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockPayablesTypeRepository,
        'findWithPagination',
      );

      mockPayablesTypeRepository.findWithPagination.mockResolvedValue(
        mockResult,
      );

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      });
    });

    it('should return paginated suppliers with CNPJ filter', async () => {
      const mockResult = {
        items: [mockPayablesType],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockPayablesTypeRepository,
        'findWithPagination',
      );

      mockPayablesTypeRepository.findWithPagination.mockResolvedValue(
        mockResult,
      );

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        code: 'OFFICE',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        code: 'OFFICE',
      });
    });

    it('should return paginated suppliers with name filter', async () => {
      const mockResult = {
        items: [mockPayablesType],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockPayablesTypeRepository,
        'findWithPagination',
      );

      mockPayablesTypeRepository.findWithPagination.mockResolvedValue(
        mockResult,
      );

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        description: 'Despesas de Escritório',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        description: 'Despesas de Escritório',
      });
    });

    it('should return paginated suppliers with both filters', async () => {
      const mockResult = {
        items: [mockPayablesType],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockPayablesTypeRepository,
        'findWithPagination',
      );

      mockPayablesTypeRepository.findWithPagination.mockResolvedValue(
        mockResult,
      );

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        code: 'OFFICE',
        description: 'Despesas de Escritório',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        code: 'OFFICE',
        description: 'Despesas de Escritório',
      });
    });
  });
});
