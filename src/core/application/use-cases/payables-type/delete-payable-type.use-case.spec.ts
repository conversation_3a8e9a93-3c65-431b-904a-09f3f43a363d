import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { DeletePayableTypeUseCase } from './delete-payable-type.use-case';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';

describe('DeletePayableTypeUseCase', () => {
  let useCase: DeletePayableTypeUseCase;
  let repository: jest.Mocked<PayablesTypeRepositoryPort>;

  const mockPayableType = new PayablesType(
    'test-id',
    'test-code',
    'test-description',
    'test-created-by',
    'test-updated-by',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<PayablesTypeRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByUuid: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByCode: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeletePayableTypeUseCase,
        {
          provide: PAYABLES_TYPE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<DeletePayableTypeUseCase>(DeletePayableTypeUseCase);
    repository = module.get<jest.Mocked<PayablesTypeRepositoryPort>>(
      PAYABLES_TYPE_REPOSITORY,
    );
  });

  describe('execute', () => {
    it('should delete payable type when found by UUID', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      const deleteFn = jest.spyOn(repository, 'delete');

      findByUuid.mockResolvedValue(mockPayableType);

      await useCase.execute({ uuid: 'test-id' });

      expect(findByUuid).toHaveBeenCalledWith('test-id');
      expect(deleteFn).toHaveBeenCalledWith('test-id');
    });

    it('should throw PayableTypeNotFoundError when payable type does not exist', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');

      findByUuid.mockResolvedValue(null);

      await expect(
        useCase.execute({ uuid: 'non-existent-id' }),
      ).rejects.toThrow(NotFoundException);
      expect(findByUuid).toHaveBeenCalledWith('non-existent-id');
    });
  });
});
