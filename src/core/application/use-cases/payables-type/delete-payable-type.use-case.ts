import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '@core/ports/repositories/payables-type-repository.port';

export interface DeletePayableTypeInput {
  uuid: string;
}

@Injectable()
export class DeletePayableTypeUseCase {
  constructor(
    @Inject(PAYABLES_TYPE_REPOSITORY)
    private readonly payableTypeRepository: PayablesTypeRepositoryPort,
  ) {}

  async execute(input: DeletePayableTypeInput): Promise<void> {
    const existingPayableType = await this.payableTypeRepository.findByUuid(
      input.uuid,
    );

    if (!existingPayableType) {
      throw new NotFoundException('Tipo de despesa não encontrado');
    }

    await this.payableTypeRepository.delete(input.uuid);
  }
}
