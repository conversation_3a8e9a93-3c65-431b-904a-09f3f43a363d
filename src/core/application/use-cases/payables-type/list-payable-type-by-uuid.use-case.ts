import { Injectable, Inject } from '@nestjs/common';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';
import { NotFoundException } from '@nestjs/common';

@Injectable()
export class ListPayablesTypeByUuidUseCase {
  constructor(
    @Inject(PAYABLES_TYPE_REPOSITORY)
    private readonly payablesTypeRepository: PayablesTypeRepositoryPort,
  ) {}

  async execute(uuid: string): Promise<PayablesType> {
    const existingPayablesType =
      await this.payablesTypeRepository.findByUuid(uuid);

    if (!existingPayablesType) {
      throw new NotFoundException('Tipo de pagamento não encontrado');
    }

    return existingPayablesType;
  }
}
