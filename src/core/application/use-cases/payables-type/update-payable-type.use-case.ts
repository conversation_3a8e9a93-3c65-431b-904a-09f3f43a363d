import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';

@Injectable()
export class UpdatePayablesTypeUseCase {
  constructor(
    @Inject(PAYABLES_TYPE_REPOSITORY)
    private readonly payableTypeRepository: PayablesTypeRepositoryPort,
  ) {}

  async execute(
    id: string,
    updateData: Partial<{
      code: string;
      description: string;
    }> & {
      updatedBy: string;
    },
  ): Promise<PayablesType> {
    const existingPayablesType =
      await this.payableTypeRepository.findByUuid(id);

    if (!existingPayablesType) {
      throw new NotFoundException('Tipo de pagamento não encontrado.');
    }

    const updatedPayablesType = new PayablesType(
      existingPayablesType.id,
      updateData.code ?? existingPayablesType.code,
      updateData.description ?? existingPayablesType.description,
      existingPayablesType.createdBy,
      updateData.updatedBy ?? existingPayablesType.updatedBy,
      existingPayablesType.createdAt,
      new Date(),
    );

    return this.payableTypeRepository.update(updatedPayablesType);
  }
}
