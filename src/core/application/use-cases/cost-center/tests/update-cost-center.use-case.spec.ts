import { Test, TestingModule } from '@nestjs/testing';
import { COST_CENTER_REPOSITORY } from '@core/ports/repositories/cost-center-repository.port';
import { CostCenter } from '../../../../domain/cost-center/entities/cost-center.entity';
import { v4 as uuidv4 } from 'uuid';
import { UpdateCostCenterUseCase } from '../update-cost-center.use-case';
import { UpdateCostCenterDto } from '@/modules/finance/cost-center/dto/update-cost-center.dto';

type MockCostCenterRepository = {
  update: jest.Mock;
  findByUuid: jest.Mock;
};

describe('UpdateCostCenterUseCase', () => {
  let useCase: UpdateCostCenterUseCase;
  let costCenterRepository: MockCostCenterRepository;

  beforeEach(async () => {
    const mockCostCenterRepository: MockCostCenterRepository = {
      update: jest.fn(),
      findByUuid: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateCostCenterUseCase,
        {
          provide: COST_CENTER_REPOSITORY,
          useValue: mockCostCenterRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateCostCenterUseCase>(UpdateCostCenterUseCase);
    costCenterRepository = module.get(COST_CENTER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should update a cost center successfully', async () => {
      // Arrange
      const dto: UpdateCostCenterDto = {
        description: 'Test Cost Center',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const expectedCostCenter = new CostCenter({
        id: '11111111-**************-************',
        description: dto.description || '',
        updatedBy: dto.updatedBy,
        updatedAt: new Date(),
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      });

      // Mock da função uuidv4 para retornar um valor fixo
      jest.mock('uuid', () => ({
        v4: jest.fn().mockReturnValue('11111111-**************-************'),
      }));
      costCenterRepository.findByUuid.mockResolvedValue(expectedCostCenter);
      costCenterRepository.update.mockResolvedValue(expectedCostCenter);

      // Act
      const result = await useCase.execute(
        '11111111-**************-************',
        dto,
      );

      // Assert
      expect(costCenterRepository.update).toHaveBeenCalled();
      expect(result).toBeInstanceOf(CostCenter);
      expect(result.description).toBe(dto.description);
      expect(result.updatedBy).toBe(dto.updatedBy);
    });

    it('should throw an error if repository throws', async () => {
      // Arrange
      const dto: UpdateCostCenterDto = {
        description: 'Test Cost Center',
        updatedBy: uuidv4(),
      };

      const error = new Error('Repository error');
      costCenterRepository.findByUuid.mockResolvedValue(
        new CostCenter({
          id: '11111111-**************-************',
          description: 'Old Description',
          updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
          updatedAt: new Date(),
          createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        }),
      );
      costCenterRepository.update.mockRejectedValue(error);

      // Act & Assert
      await expect(
        useCase.execute('11111111-**************-************', dto),
      ).rejects.toThrow(error);
    });
  });
});
