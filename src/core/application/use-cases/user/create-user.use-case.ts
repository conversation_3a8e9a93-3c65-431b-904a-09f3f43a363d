import { User } from '../../../domain/user.entity';
import { UserRepository } from '../../../ports/repositories/user-repository.interface';
import { v4 as uuidv4 } from 'uuid';
import { Role } from '../../../domain/role.enum';
import { PasswordHasher } from '../../../shared/password-hasher';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';
import { BadRequestException } from '@nestjs/common';

export interface CreateUserInput {
  name: string;
  email: string;
  password: string;
  role?: Role;
}

export class CreateUserUseCase {
  constructor(
    private userRepository: UserRepository,
    private keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {}

  async execute(input: CreateUserInput): Promise<User> {
    const { name, email, password, role = Role.USER } = input;

    // Verificar se já existe um usuário com o mesmo email
    const existingUser = await this.userRepository
      .findByEmail(email)
      .catch(() => null);

    if (existingUser) {
      throw new Error(`Usuário com email ${email} já existe`);
    }

    const hashedPassword = await PasswordHasher.hash(password);

    // Preparar dados para o Keycloak - similar ao teste e2e
    const nameParts = name.split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

    let keycloakUserId: string;
    const userRole = role || Role.USER;
    try {
      // Create user object for registration
      const keycloakUser = {
        username: email, // Username será sempre igual ao email
        email: email,
        firstName,
        lastName,
        password: password,
      };

      // Register user in Keycloak
      keycloakUserId =
        await this.keycloakIdentityProvider.registerUser(keycloakUser);

      // Assign user roles
      await this.keycloakIdentityProvider.assignUserRoles(keycloakUserId, [
        userRole,
      ]);
    } catch (keycloakError) {
      console.error('Erro ao registrar no Keycloak:', keycloakError);
      throw new BadRequestException(
        `Falha ao registrar usuário: ${
          keycloakError instanceof Error
            ? keycloakError.message
            : String(keycloakError)
        }`,
      );
    }
    console.log('keycloakUserId', keycloakUserId);
    // Criar a entidade de domínio
    const user = new User(uuidv4(), email, name, hashedPassword, role);

    const userCreated = await this.userRepository.create(user);

    await this.userRepository.updateUserKeycloakId(
      userCreated.id,
      keycloakUserId,
    );

    return userCreated;
  }
}
