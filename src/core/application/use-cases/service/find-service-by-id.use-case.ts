import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ServiceRepositoryPort } from '../../../ports/repositories/service-repository.port';
import { Service } from '../../../domain/service/entities/service.entity';

@Injectable()
export class FindServiceByIdUseCase {
  constructor(
    @Inject('SERVICE_REPOSITORY')
    private readonly serviceRepository: ServiceRepositoryPort,
  ) {}

  async execute(id: string): Promise<Service> {
    const service = await this.serviceRepository.findById(id);
    
    if (!service) {
      throw new NotFoundException(`Serviço com ID ${id} não encontrado`);
    }

    return service;
  }
} 