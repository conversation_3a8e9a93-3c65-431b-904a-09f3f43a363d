import { DomainEvent } from './domain-event';

/**
 * Classe abstrata AggregateRoot
 * Serve como base para todas as entidades que precisam publicar eventos de domínio
 */
export abstract class AggregateRoot {
  private readonly _domainEvents: DomainEvent[] = [];

  /**
   * Obtém os eventos de domínio registrados
   */
  public getDomainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  /**
   * Adiciona um evento de domínio à lista de eventos pendentes
   * @param event Evento de domínio a ser adicionado
   */
  protected addEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }

  /**
   * Remove todos os eventos pendentes
   * Deve ser chamado após a publicação dos eventos
   */
  public clearEvents(): void {
    this._domainEvents.length = 0;
  }
}
