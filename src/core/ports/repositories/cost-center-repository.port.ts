import { CostCenter } from '@/core/domain/cost-center/entities/cost-center.entity';

export const COST_CENTER_REPOSITORY = 'COST_CENTER_REPOSITORY';

export interface CostCenterRepositoryPort {
  create(costCenter: CostCenter): Promise<CostCenter>;
  findById(id: number): Promise<CostCenter | null>;
  findByUuid(uuid: string): Promise<CostCenter | null>;
  findAll(): Promise<CostCenter[]>;
  update(costCenter: CostCenter): Promise<CostCenter>;
  delete(id: string): Promise<void>;
  findWithPagination(params: {
    limit: number;
    offset: number;
    description?: string;
  }): Promise<{ items: CostCenter[]; total: number }>;
}
