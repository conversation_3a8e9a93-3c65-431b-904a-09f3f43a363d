import { CustomerContact } from "@/core/domain/customer/entities/customer-contact.entity";

export interface CustomerContactRepositoryPort {
    create(contact: Omit<CustomerContact, 'id'>);
    findByCustomerId(customerId: number);
    deleteById(id: string): Promise<void>;
    updateById(id: string, contact: Partial<Omit<CustomerContact, 'createdAt' | 'updatedAt' | 'id' | 'customerId'>>): Promise<CustomerContact>;
}