import { Employee, PersonalDocument } from '@/core/domain/entities/employee.entity';

export const EMPLOYEE_REPOSITORY = 'EMPLOYEE_REPOSITORY';

export interface ListEmployeeCriteria {
  limit: number;
  offset: number;
  name?: string;
  email?: string;
}

export interface ListEmployeeResult {
  items: Employee[];
  total: number;
}

export interface EmployeeRepositoryPort {
  findAll(): Promise<Employee[]>;
  findWithPagination(params: {
    limit: number;
    offset: number;
    name?: string;
    email?: string;
  }): Promise<{ items: Employee[]; total: number }>;
  findByEmail(email: string): Promise<Employee | null>;
  findByUuid(uuid: string): Promise<Employee | null>;
  findById(id: number): Promise<Employee | null>;
  create(employee: Employee): Promise<Employee>;
  update(employee: Employee): Promise<Employee>;
  findPersonalDocument(uuid: string, number: string): Promise<PersonalDocument | null>;
  uploadPersonalDocument(uuid: string, number: string, filePath: string, fileName: string): Promise<PersonalDocument>;
  deletePersonalDocument(uuid: string, number: string): Promise<void>;
  delete(uuid: string): Promise<void>;
}
