import { Company } from '../../domain/entities/company.entity';

export interface CompanyRepositoryPort {
  create(company: Company): Promise<Company>;
  findById(id: number): Promise<Company | null>;
  findByUuid(uuid: string): Promise<Company | null>;
  findByCnpj(cnpj: string): Promise<Company | null>;
  findAll(): Promise<Company[]>;
  update(company: Company): Promise<Company>;
  delete(id: number): Promise<void>;
}
