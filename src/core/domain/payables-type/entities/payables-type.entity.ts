export class PayablesType {
  private readonly _id: string;
  private readonly _code: string;
  private readonly _description: string;
  private readonly _createdBy: string;
  private readonly _createdAt: Date;
  private readonly _updatedBy: string;
  private readonly _updatedAt: Date;

  constructor(
    id: string,
    code: string,
    description: string,
    createdBy: string,
    updatedBy: string = createdBy,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
  ) {
    this._id = id;
    this._code = code;
    this._description = description;
    this._createdBy = createdBy;
    this._createdAt = createdAt;
    this._updatedAt = updatedAt;
    this._updatedBy = updatedBy;
    this.validate();
  }

  private validate(): void {
    if (!this._id) throw new Error('ID is required');
    if (!this._code) throw new Error('Code is required');
    if (!this._description) throw new Error('Description is required');
    if (!this._createdBy) throw new Error('CreatedBy is required');

    if (this._code.length > 50) {
      throw new Error('Code must be at most 50 characters');
    }

    if (this._description.length > 255) {
      throw new Error('Description must be at most 255 characters');
    }
  }

  get id(): string {
    return this._id;
  }

  get code(): string {
    return this._code;
  }

  get description(): string {
    return this._description;
  }

  get createdBy(): string {
    return this._createdBy;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  toJSON(): {
    id: string;
    code: string;
    description: string;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
  } {
    return {
      id: this._id,
      code: this._code,
      description: this._description,
      createdBy: this._createdBy,
      createdAt: this._createdAt.toISOString(),
      updatedBy: this._updatedBy,
      updatedAt: this._updatedAt.toISOString(),
    };
  }
}
