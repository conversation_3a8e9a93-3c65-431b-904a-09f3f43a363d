interface CostCenterProps {
  id: string;
  description: string;
  createdBy: string;
  updatedBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export class CostCenter {
  private readonly _id: string;
  private readonly _description: string;
  private readonly _createdBy: string;
  private readonly _createdAt: Date;
  private readonly _updatedBy: string;
  private readonly _updatedAt: Date;

  constructor(props: CostCenterProps) {
    this._id = props.id;
    this._description = props.description;
    this._createdBy = props.createdBy;
    this._createdAt = props.createdAt || new Date();
    this._updatedBy = props.updatedBy || props.createdBy;
    this._updatedAt = props.updatedAt || new Date();
    this.validate();
  }

  private validate(): void {
    if (!this._id) throw new Error('ID is required');
    if (!this._description) throw new Error('Description is required');
    if (!this._createdBy) throw new Error('CreatedBy is required');

    if (this._description.length > 255) {
      throw new Error('Description must be at most 255 characters');
    }
  }

  get id(): string {
    return this._id;
  }

  get description(): string {
    return this._description;
  }

  get createdBy(): string {
    return this._createdBy;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  toJSON(): {
    id: string;
    description: string;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
  } {
    return {
      id: this._id,
      description: this._description,
      createdBy: this._createdBy,
      createdAt: this._createdAt.toISOString(),
      updatedBy: this._updatedBy,
      updatedAt: this._updatedAt.toISOString(),
    };
  }
}
