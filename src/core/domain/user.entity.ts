import { AggregateRoot } from '../shared/aggregate-root';
import { Role } from './role.enum';
import { PasswordHasher } from '../shared/password-hasher';

export class User extends AggregateRoot {
  readonly id: string;
  readonly email: string;
  private _name: string;
  private _password: string;
  private _role: Role;
  readonly createdAt: Date;
  private _updatedAt: Date;
  private _keycloakId?: string;

  constructor(
    id: string,
    email: string,
    name: string,
    password: string,
    role: Role = Role.USER,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    keycloakId?: string,
  ) {
    super();
    this.id = id;
    this.email = email;
    this._name = name;
    this._password = password;
    this._role = role;
    this.createdAt = createdAt;
    this._updatedAt = updatedAt;
    this._keycloakId = keycloakId;
  }

  // Getters
  get name(): string {
    return this._name;
  }

  get password(): string {
    return this._password;
  }

  get role(): Role {
    return this._role;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get keycloakId(): string | undefined {
    return this._keycloakId;
  }

  // Business logic methods
  updateName(name: string): void {
    this._name = name;
    this._updatedAt = new Date();
  }

  async updatePassword(password: string): Promise<void> {
    this._password = await PasswordHasher.hash(password);
    this._updatedAt = new Date();
  }

  setKeycloakId(keycloakId: string): void {
    this._keycloakId = keycloakId;
    this._updatedAt = new Date();
  }

  promoteToAdmin(): void {
    this._role = Role.ADMIN;
    this._updatedAt = new Date();
  }

  demoteToUser(): void {
    this._role = Role.USER;
    this._updatedAt = new Date();
  }

  toJSON() {
    return {
      id: this.id,
      email: this.email,
      name: this._name,
      role: this._role,
      createdAt: this.createdAt,
      updatedAt: this._updatedAt,
      keycloakId: this._keycloakId,
    };
  }
}
