import { DomainEvent } from '../../../../core/domain/events/domain-event';

export class SupplierCreatedEvent extends DomainEvent {
  constructor(
    public readonly payload: {
      supplierId: string;
      name: string;
      cnpj: string;
      createdAt: Date;
    },
  ) {
    super('supplier.created');
  }

  getData(): object {
    return {
      supplierId: this.payload.supplierId,
      name: this.payload.name,
      cnpj: this.payload.cnpj,
      createdAt: this.payload.createdAt.toISOString(),
    };
  }
}
