import { Supplier } from './supplier.entity';
import { Address } from '../value-objects/address.value-object';
import { SupplierStatus } from '../enums/supplier-status.enum';
import { SupplierType } from '../enums/supplier-type.enum';
import { SupplierClassification, TaxRegime, CompanySize } from '@prisma/client';

describe('Supplier Entity', () => {
  const validAddress = new Address(
    'Rua Teste',
    '123',
    'Apto 45',
    'Centro',
    'Cidade Teste',
    '12345-678',
    'TS',
  );

  const validSupplierData = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test Supplier',
    document: '12345678901234',
    tradeName: 'Test Trade Name',
    address: validAddress,
    email: '<EMAIL>',
    classification: SupplierClassification.CORE,
    type: SupplierType.GAME,
    status: SupplierStatus.ACTIVE,
    userId: 'user-123',
    createdBy: 'test-user',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    updatedBy: 'test-user',
  };

  describe('constructor validation', () => {
    it('should create a valid supplier', () => {
      const supplier = new Supplier(
        validSupplierData.id,
        validSupplierData.name,
        validSupplierData.document,
        validSupplierData.tradeName,
        validSupplierData.address,
        validSupplierData.email,
        validSupplierData.classification,
        validSupplierData.type,
        validSupplierData.status,
        validSupplierData.userId,
        validSupplierData.createdBy,
        validSupplierData.createdAt,
        validSupplierData.updatedAt,
        validSupplierData.updatedBy,
      );

      expect(supplier).toBeDefined();
      expect(supplier.id).toBe(validSupplierData.id);
      expect(supplier.name).toBe(validSupplierData.name);
      expect(supplier.document).toBe(validSupplierData.document);
      expect(supplier.tradeName).toBe(validSupplierData.tradeName);
      expect(supplier.address).toBe(validSupplierData.address);
      expect(supplier.email).toBe(validSupplierData.email);
      expect(supplier.classification).toBe(validSupplierData.classification);
      expect(supplier.type).toBe(validSupplierData.type);
      expect(supplier.status).toBe(validSupplierData.status);
      expect(supplier.userId).toBe(validSupplierData.userId);
      expect(supplier.createdBy).toBe(validSupplierData.createdBy);
      expect(supplier.createdAt).toBe(validSupplierData.createdAt);
      expect(supplier.updatedAt).toBe(validSupplierData.updatedAt);
      expect(supplier.updatedBy).toBe(validSupplierData.updatedBy);
    });

    it('should create a valid supplier without trade name', () => {
      const supplier = new Supplier(
        validSupplierData.id,
        validSupplierData.name,
        validSupplierData.document,
        null,
        validSupplierData.address,
        validSupplierData.email,
        validSupplierData.classification,
        validSupplierData.type,
        validSupplierData.status,
        validSupplierData.userId,
        validSupplierData.createdBy,
        validSupplierData.createdAt,
        validSupplierData.updatedAt,
        validSupplierData.updatedBy,
      );

      expect(supplier).toBeDefined();
      expect(supplier.tradeName).toBeNull();
    });
  });

  describe('required fields validation', () => {
    it('should throw error when id is empty', () => {
      expect(() => {
        new Supplier(
          '',
          validSupplierData.name,
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('ID is required');
    });

    it('should throw error when name is empty', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          '',
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Name is required');
    });

    it('should throw error when document is empty', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          '',
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Document is required');
    });

    it('should throw error when address is not provided', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          validSupplierData.document,
          validSupplierData.tradeName,
          undefined as unknown as Address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Address is required');
    });

    it('should throw error when type is not provided', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          undefined as unknown as SupplierType,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Type is required');
    });

    it('should throw error when status is not provided', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          undefined as unknown as SupplierStatus,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Status is required');
    });

    it('should throw error when userId is empty', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          '',
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('User ID is required');
    });

    it('should throw error when createdBy is empty', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          '',
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('CreatedBy is required');
    });
  });

  describe('field validation rules', () => {
    it('should throw error when name is less than 3 characters', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          'ab',
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Name must be between 3 and 100 characters');
    });

    it('should throw error when name is more than 100 characters', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          'a'.repeat(101),
          validSupplierData.document,
          validSupplierData.tradeName,
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Name must be between 3 and 100 characters');
    });

    it('should throw error when document format is invalid', () => {
      const invalidDocuments = [
        '1234567890123', // 13 digits
        '123456789012345', // 15 digits
        '1234567890abc', // non-numeric (will be cleaned to 1234567890 which is 10 digits)
        '123456789', // 9 digits
      ];

      invalidDocuments.forEach((invalidDocument) => {
        expect(() => {
          new Supplier(
            validSupplierData.id,
            validSupplierData.name,
            invalidDocument,
            validSupplierData.tradeName,
            validSupplierData.address,
            validSupplierData.email,
            validSupplierData.classification,
            validSupplierData.type,
            validSupplierData.status,
            validSupplierData.userId,
            validSupplierData.createdBy,
            validSupplierData.createdAt,
            validSupplierData.updatedAt,
            validSupplierData.updatedBy,
          );
        }).toThrow(`Invalid Document format. Must be 14 or 11 digits, received: ${invalidDocument.replace(/\D/g, '')}`);
      });
    });

    it('should throw error when trade name is more than 100 characters', () => {
      expect(() => {
        new Supplier(
          validSupplierData.id,
          validSupplierData.name,
          validSupplierData.document,
          'a'.repeat(101),
          validSupplierData.address,
          validSupplierData.email,
          validSupplierData.classification,
          validSupplierData.type,
          validSupplierData.status,
          validSupplierData.userId,
          validSupplierData.createdBy,
          validSupplierData.createdAt,
          validSupplierData.updatedAt,
          validSupplierData.updatedBy,
        );
      }).toThrow('Trade name must be at most 100 characters');
    });
  });

  describe('business rules', () => {
    it('should update trade name', () => {
      const supplier = new Supplier(
        validSupplierData.id,
        validSupplierData.name,
        validSupplierData.document,
        validSupplierData.tradeName,
        validSupplierData.address,
        validSupplierData.email,
        validSupplierData.classification,
        validSupplierData.type,
        validSupplierData.status,
        validSupplierData.userId,
        validSupplierData.createdBy,
      );

      const newTradeName = 'New Trade Name';
      supplier.updateTradeName(newTradeName);

      expect(supplier.tradeName).toBe(newTradeName);
      expect(supplier.updatedAt).not.toBe(supplier.createdAt);
    });

    it('should update status', () => {
      const supplier = new Supplier(
        validSupplierData.id,
        validSupplierData.name,
        validSupplierData.document,
        validSupplierData.tradeName,
        validSupplierData.address,
        validSupplierData.email,
        validSupplierData.classification,
        validSupplierData.type,
        SupplierStatus.ACTIVE,
        validSupplierData.userId,
        validSupplierData.createdBy,
      );

      const newUpdatedBy = 'another-user';
      supplier.updateStatus(SupplierStatus.INACTIVE, newUpdatedBy);

      expect(supplier.status).toBe(SupplierStatus.INACTIVE);
      expect(supplier.updatedAt).not.toBe(supplier.createdAt);
      expect(supplier.updatedBy).toBe(newUpdatedBy);
    });
  });

  describe('toJSON', () => {
    it('should return supplier as JSON object', () => {
      const supplier = new Supplier(
        validSupplierData.id,
        validSupplierData.name,
        validSupplierData.document,
        validSupplierData.tradeName,
        validSupplierData.address,
        validSupplierData.email,
        validSupplierData.classification,
        validSupplierData.type,
        validSupplierData.status,
        validSupplierData.userId,
        validSupplierData.createdBy,
        validSupplierData.createdAt,
        validSupplierData.updatedAt,
        validSupplierData.updatedBy,
      );

      const json = supplier.toJSON();

      expect(json).toEqual({
        id: validSupplierData.id,
        name: validSupplierData.name,
        document: validSupplierData.document,
        tradeName: validSupplierData.tradeName,
        address: validSupplierData.address.toJSON(),
        classification: validSupplierData.classification,
        email: validSupplierData.email,
        type: validSupplierData.type,
        status: validSupplierData.status,
        userId: validSupplierData.userId,
        stateRegistration: undefined,
        municipalRegistration: undefined,
        taxRegime: undefined,
        companySize: undefined,
        createdAt: validSupplierData.createdAt.toISOString(),
        createdBy: validSupplierData.createdBy,
        updatedAt: validSupplierData.updatedAt.toISOString(),
        updatedBy: validSupplierData.updatedBy,
      });
    });
  });
});
