export class SupplierContact {
  id: string;
  supplierId: string;
  contact: string;
  type: string;
  area: string;
  responsible: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;

  constructor(props: Omit<SupplierContact, 'id'> & { id?: string }) {
    this.id = props.id ?? '';
    this.supplierId = props.supplierId;
    this.contact = props.contact;
    this.type = props.type;
    this.area = props.area;
    this.responsible = props.responsible;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
    this.deletedAt = props.deletedAt;
  }
} 