import { IdentityTokenRefreshedEvent } from './identity-token-refreshed.event';

describe('IdentityTokenRefreshedEvent', () => {
  const userId = 'user-123';
  const oldTokenId = 'token-456';
  const timestamp = new Date('2024-03-20T10:00:00Z');

  it('should create an event with required data', () => {
    const event = new IdentityTokenRefreshedEvent(userId, oldTokenId);

    expect(event.userId).toBe(userId);
    expect(event.oldTokenId).toBe(oldTokenId);
    expect(event.eventName).toBe('identity.token.refreshed');
    expect(event.eventId).toBeDefined();
    expect(event.occurredAt).toBeInstanceOf(Date);
  });

  it('should create an event with metadata', () => {
    const metadata = {
      correlationId: 'corr-789',
      userId: userId,
      occurredAt: timestamp,
      eventId: 'event-123',
    };

    const event = new IdentityTokenRefreshedEvent(userId, oldTokenId, metadata);

    expect(event.correlationId).toBe(metadata.correlationId);
    expect(event.userId).toBe(metadata.userId);
    expect(event.occurredAt).toBe(metadata.occurredAt);
    expect(event.eventId).toBe(metadata.eventId);
  });

  it('should serialize event data correctly', () => {
    const event = new IdentityTokenRefreshedEvent(userId, oldTokenId, {
      occurredAt: timestamp,
      eventId: 'f7dac1bd-4c13-4f54-a36a-59afc32061c4',
    });

    const serialized = event.serialize();

    expect(serialized).toEqual({
      eventId: 'f7dac1bd-4c13-4f54-a36a-59afc32061c4',
      eventName: 'identity.token.refreshed',
      occurredAt: timestamp.toISOString(),
      data: {
        userId: userId,
        oldTokenId: oldTokenId,
      },
    });
  });
});
