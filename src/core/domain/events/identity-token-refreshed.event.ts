import { DomainEvent } from './domain-event';

export class IdentityTokenRefreshedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly oldTokenId: string,
    metadata?: {
      correlationId?: string;
      userId?: string;
      occurredAt?: Date;
      eventId?: string;
    },
  ) {
    super('identity.token.refreshed', metadata);
  }

  getData(): object {
    return {
      userId: this.userId,
      oldTokenId: this.oldTokenId,
    };
  }

  serialize(): object {
    return {
      eventId: this.eventId,
      eventName: this.eventName,
      occurredAt: this.occurredAt.toISOString(),
      data: this.getData(),
    };
  }
}
