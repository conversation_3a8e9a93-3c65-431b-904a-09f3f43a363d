import { DomainEvent } from './domain-event';

export class IdentityAuthenticatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly tokenId?: string,
    metadata?: {
      correlationId?: string;
      userId?: string;
      occurredAt?: Date;
      eventId?: string;
    },
  ) {
    super('identity.authenticated', metadata);
  }

  getData(): object {
    return {
      userId: this.userId,
      tokenId: this.tokenId,
    };
  }

  serialize(): object {
    return {
      eventId: this.eventId,
      eventName: this.eventName,
      occurredAt: this.occurredAt.toISOString(),
      data: this.getData(),
    };
  }
}
