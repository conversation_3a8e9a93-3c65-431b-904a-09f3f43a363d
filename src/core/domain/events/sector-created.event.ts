import { DomainEvent } from '../base/domain-event';
import { Sector } from '../entities/sector.entity';

export class SectorCreatedEvent extends DomainEvent {
  constructor(private readonly sector: Sector) {
    super('sector.created');
  }

  getEventName(): string {
    return 'sector.created';
  }

  getData(): object {
    return {
      sectorId: this.sector.id,
      uuid: this.sector.uuid,
      code: this.sector.code,
      description: this.sector.description,
      createdBy: this.sector.createdBy,
      updatedBy: this.sector.updatedBy,
      createdAt: this.sector.createdAt.toISOString(),
      updatedAt: this.sector.updatedAt.toISOString(),
    };
  }
}
