/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { WorkSchedule } from '@/core/domain/entities/employee.entity';

export function calculateWorkJourney(workSchedule: WorkSchedule): string {
  if (
    !workSchedule.startingHour ||
    !workSchedule.endingHour ||
    !workSchedule.weekDays ||
    workSchedule.weekDays.length === 0
  ) {
    return '0 horas semanais';
  }

  const start = new Date(`2000-01-01T${workSchedule.startingHour}`);
  const end = new Date(`2000-01-01T${workSchedule.endingHour}`);
  const dailyHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);

  const totalHours = dailyHours * workSchedule.weekDays.length;

  return `${totalHours} horas semanais`;
}
