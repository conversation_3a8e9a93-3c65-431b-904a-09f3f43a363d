import { AggregateRoot } from '../../base/aggregate-root';
import { SectorCreatedEvent } from '../events/sector-created.event';

export class Sector extends AggregateRoot {
  private constructor(
    private readonly _id: number,
    private readonly _uuid: string,
    private readonly _code: string,
    private readonly _description: string,
    private readonly _createdBy: string,
    private readonly _updatedBy: string,
    private readonly _createdAt: Date,
    private readonly _updatedAt: Date,
  ) {
    super();
  }

  public static create(
    id: number,
    uuid: string,
    code: string,
    description: string,
    createdBy: string,
    updatedBy: string,
    createdAt: Date,
    updatedAt: Date,
  ): Sector {
    const sector = new Sector(
      id,
      uuid,
      code,
      description,
      createdBy,
      updatedBy,
      createdAt,
      updatedAt,
    );

    sector.addEvent(new SectorCreatedEvent(sector));

    return sector;
  }

  get id(): number {
    return this._id;
  }

  get uuid(): string {
    return this._uuid;
  }

  get code(): string {
    return this._code;
  }

  get description(): string {
    return this._description;
  }

  get createdBy(): string {
    return this._createdBy;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }
}
