import { EmployeeStatus } from '@prisma/client';
import { AggregateRoot } from '../../base/aggregate-root';
import { EmployeeCreatedEvent } from '../events/employee-created.event';
import { ContractType, Seniority, Shift } from '../enums/employee.enum';

export interface Address {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface PersonalDocument {
  type: string;
  number: string;
  issuingAgency?: string;
  issueDate?: string;
  filePath?: string;
  fileName?: string;
}

export interface Dependent {
  name: string;
  kinship: string;
  birthDate?: string;
  isTaxDependent?: boolean;
  hasHealthPlan?: boolean;
}

export interface WorkSchedule {
  weekDays: number[];
  startingHour: string;
  endingHour: string;
}

export interface Vacation {
  startDate: string;
  endDate: string;
}

export class Employee extends AggregateRoot {
  constructor(
    private readonly _id: number,
    private readonly _uuid: string,
    private readonly _name: string,
    private readonly _email: string,
    private readonly _position: string,
    private readonly _department: string,
    private readonly _hireDate: Date,
    private readonly _address: Address,
    private readonly _personalDocuments: PersonalDocument[],
    private readonly _dependents: Dependent[],
    private readonly _status: EmployeeStatus,
    private readonly _createdBy: string,
    private readonly _updatedBy: string,
    private readonly _createdAt: Date,
    private readonly _updatedAt: Date,
    private readonly _workSchedule?: WorkSchedule,
    private readonly _shift?: Shift,
    private readonly _grossSalary?: number,
    private readonly _mealAllowance?: number,
    private readonly _transportAllowance?: number,
    private readonly _healthPlan?: string,
    private readonly _contractType?: ContractType,
    private readonly _seniority?: Seniority,
    private readonly _phone?: string,
    private readonly _birthDate?: Date,
    private readonly _workHours?: string,
    private readonly _overtimeBank?: boolean,
    private readonly _vacations?: Vacation[],
    private readonly _userId?: string,
  ) {
    super();
  }

  public static create(
    id: number,
    uuid: string,
    name: string,
    email: string,
    position: string,
    department: string,
    hireDate: Date,
    address: Address,
    personalDocuments: PersonalDocument[],
    dependents: Dependent[],
    status: EmployeeStatus,
    createdBy: string,
    updatedBy: string,
    createdAt: Date,
    updatedAt: Date,
    workSchedule?: WorkSchedule,
    shift?: Shift,
    grossSalary?: number,
    mealAllowance?: number,
    transportAllowance?: number,
    healthPlan?: string,
    contractType?: ContractType,
    seniority?: Seniority,
    phone?: string,
    birthDate?: Date,
    workHours?: string,
    overtimeBank?: boolean,
    vacations?: Vacation[],
    userId?: string,
  ): Employee {
    const employee = new Employee(
      id,
      uuid,
      name,
      email,
      position,
      department,
      hireDate,
      address,
      personalDocuments,
      dependents,
      status,
      createdBy,
      updatedBy,
      createdAt,
      updatedAt,
      workSchedule,
      shift,
      grossSalary,
      mealAllowance,
      transportAllowance,
      healthPlan,
      contractType,
      seniority,
      phone,
      birthDate,
      workHours,
      overtimeBank,
      vacations,
      userId,
    );

    employee.addEvent(new EmployeeCreatedEvent(employee));

    return employee;
  }

  get id(): number {
    return this._id;
  }

  get uuid(): string {
    return this._uuid;
  }

  get name(): string {
    return this._name;
  }

  get email(): string {
    return this._email;
  }

  get position(): string {
    return this._position;
  }

  get department(): string {
    return this._department;
  }

  get hireDate(): Date {
    return this._hireDate;
  }

  get address(): Address {
    return this._address;
  }

  get personalDocuments(): PersonalDocument[] {
    return this._personalDocuments;
  }

  get dependents(): Dependent[] {
    return this._dependents;
  }

  get status(): EmployeeStatus {
    return this._status;
  }

  get createdBy(): string {
    return this._createdBy;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get workSchedule(): WorkSchedule | undefined {
    return this._workSchedule;
  }

  get shift(): Shift | undefined {
    return this._shift;
  }

  get grossSalary(): number | undefined {
    return this._grossSalary;
  }

  get mealAllowance(): number | undefined {
    return this._mealAllowance;
  }

  get transportAllowance(): number | undefined {
    return this._transportAllowance;
  }

  get healthPlan(): string | undefined {
    return this._healthPlan;
  }

  get contractType(): ContractType | undefined {
    return this._contractType;
  }

  get seniority(): Seniority | undefined {
    return this._seniority;
  }

  get phone(): string | undefined {
    return this._phone;
  }

  get birthDate(): Date | undefined {
    return this._birthDate;
  }

  get workHours(): string | undefined {
    return this._workHours;
  }

  get overtimeBank(): boolean | undefined {
    return this._overtimeBank;
  }

  get vacations(): Vacation[] | undefined {
    return this._vacations;
  }

  get userId(): string | undefined {
    return this._userId;
  }

  toJSON() {
    return {
      id: this._id,
      uuid: this._uuid,
      name: this._name,
      email: this._email,
      position: this._position,
      department: this._department,
      hireDate: this._hireDate.toISOString(),
      address: this._address,
      personalDocuments: this._personalDocuments,
      dependents: this._dependents,
      status: this._status,
      workSchedule: this._workSchedule,
      shift: this._shift,
      grossSalary: this._grossSalary,
      mealAllowance: this._mealAllowance,
      transportAllowance: this._transportAllowance,
      healthPlan: this._healthPlan,
      contractType: this._contractType,
      seniority: this._seniority,
      phone: this._phone,
      birthDate: this._birthDate?.toISOString(),
      workHours: this._workHours,
      overtimeBank: this._overtimeBank,
      vacations: this._vacations,
      createdBy: this._createdBy,
      updatedBy: this._updatedBy,
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
      userId: this._userId,
    };
  }
}
