import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { randomUUID } from 'crypto';

export function setupTestDatabase(): string {
  const schema = randomUUID();
  const databaseUrl = process.env.DATABASE_URL?.replace('public', schema);

  execSync(`set DATABASE_URL=${databaseUrl} && npx prisma db push`, {
    stdio: 'ignore',
  }); // aplica schema

  process.env.DATABASE_URL = databaseUrl;

  return schema;
}

export async function deleteTestDatabase(schema: string) {
  const prisma = new PrismaClient();
  await prisma.$executeRawUnsafe(`DROP SCHEMA IF EXISTS "${schema}" CASCADE`);
  await prisma.$disconnect();
}
