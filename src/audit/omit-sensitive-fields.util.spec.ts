/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { omitSensitiveFields } from './omit-sensitive-fields.util';

describe('omitSensitiveFields', () => {
  it('should return the same value for non-object inputs', () => {
    expect(omitSensitiveFields(null as any)).toBe(null);
    expect(omitSensitiveFields(undefined as any)).toBe(undefined);
    expect(omitSensitiveFields('string' as any)).toBe('string');
    expect(omitSensitiveFields(123 as any)).toBe(123);
    expect(omitSensitiveFields(true as any)).toBe(true);
  });

  it('should redact password field', () => {
    const input = { username: 'test', password: 'secret123' };
    const result = omitSensitiveFields(input);

    expect(result).toEqual({
      username: 'test',
      password: '[REDACTED]',
    });
  });

  it('should redact multiple sensitive fields', () => {
    const input = {
      username: 'test',
      password: 'secret123',
      token: 'jwt-token',
      cpf: '123.456.789-00',
      cnpj: '12.345.678/0001-90',
      normalField: 'visible',
    };

    const result = omitSensitiveFields(input);

    expect(result).toEqual({
      username: 'test',
      password: '[REDACTED]',
      token: '[REDACTED]',
      cpf: '[REDACTED]',
      cnpj: '[REDACTED]',
      normalField: 'visible',
    });
  });

  it('should redact credit card related fields', () => {
    const input = {
      creditCardNumber: '1234-5678-9012-3456',
      securityCode: '123',
      expirationDate: '12/25',
      cardHolderName: 'John Doe',
    };

    const result = omitSensitiveFields(input);

    expect(result).toEqual({
      creditCardNumber: '[REDACTED]',
      securityCode: '[REDACTED]',
      expirationDate: '[REDACTED]',
      cardHolderName: 'John Doe',
    });
  });

  it('should redact newPassowrd field (typo preserved)', () => {
    const input = {
      currentPassword: 'old123',
      newPassowrd: 'new123',
    };

    const result = omitSensitiveFields(input);

    expect(result).toEqual({
      currentPassword: 'old123',
      newPassowrd: '[REDACTED]',
    });
  });

  it('should not modify object without sensitive fields', () => {
    const input = {
      username: 'test',
      email: '<EMAIL>',
      role: 'admin',
    };

    const result = omitSensitiveFields(input);

    expect(result).toEqual(input);
  });

  it('should handle empty object', () => {
    const input = {};
    const result = omitSensitiveFields(input);

    expect(result).toEqual({});
  });

  it('should handle object with nested sensitive field names that should not be redacted', () => {
    const input = {
      userData: {
        password: 'should-not-be-redacted',
      },
      password: 'should-be-redacted',
    };

    const result = omitSensitiveFields(input);

    expect(result).toEqual({
      userData: {
        password: 'should-not-be-redacted',
      },
      password: '[REDACTED]',
    });
  });
});
