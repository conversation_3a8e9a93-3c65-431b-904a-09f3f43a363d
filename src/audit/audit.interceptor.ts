/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */

import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { AuditPublisherService } from './audit-publisher.service';
import { AuditEvent, AuditEventType } from './audit.interface';
import { omitSensitiveFields } from './omit-sensitive-fields.util';

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  constructor(private readonly auditPublisher: AuditPublisherService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    let user = request.user;
    if (!user) {
      user = {
        username:
          (request.body?.username as string) ||
          (request.body?.email as string) ||
          'unknown',
      };
    }

    const auditEvent: AuditEvent = {
      eventType: AuditEventType.AUDIT_LOG,
      timestamp: new Date().toISOString(),
      username: (user?.email as string) || (user?.username as string),
      method: request.method as string,
      endpoint: request.url as string,
      body: omitSensitiveFields(
        (request.body as Record<string, unknown>) || {},
      ),
      // Use 'x-forwarded-for' if available, otherwise fallback to 'ip'
      ip: request.headers['x-forwarded-for'] || request.ip,
      userAgent: request.get?.('user-agent') || '',
    };
    void this.auditPublisher.publish(auditEvent);
    return next.handle();
  }
}
