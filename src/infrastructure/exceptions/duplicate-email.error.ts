import { ConflictException } from '@nestjs/common';

export class DuplicateEmailError extends ConflictException {
  constructor(email: string, entity: string = 'recurso') {
    super({
      statusCode: 409,
      error: 'Conflict',
      message: `Já existe um ${entity} cadastrado com o email ${email}. Por favor, verifique os dados e tente novamente.`,
      field: 'email',
      value: email,
      entity: entity,
    });
  }
}
