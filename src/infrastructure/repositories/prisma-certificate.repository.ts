import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { ICertificateRepository } from '@/core/ports/repositories/certificate.repository.port';
import { Certificate } from '@/modules/customers/domain/entities/certificate.entity';
import { CertificateMapper } from '@/modules/customers/infrastructure/mappers/certificate.mapper';

@Injectable()
export class PrismaCertificateRepository implements ICertificateRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(certificate: Certificate): Promise<Certificate> {
    const prismaCertificate = CertificateMapper.toPersistence(certificate);
    const created = await this.prisma.certificate.create({
      data: prismaCertificate,
    });
    return CertificateMapper.toDomain(created);
  }

  async update(certificate: Certificate): Promise<Certificate> {
    const prismaCertificate = CertificateMapper.toPersistence(certificate);
    const updated = await this.prisma.certificate.update({
      where: { id: certificate.id },
      data: prismaCertificate,
    });
    return CertificateMapper.toDomain(updated);
  }

  async findById(id: string): Promise<Certificate | null> {
    const certificate = await this.prisma.certificate.findUnique({
      where: { id },
    });
    return certificate ? CertificateMapper.toDomain(certificate) : null;
  }

  async findByCustomerId(customerId: number): Promise<Certificate[]> {
    const certificates = await this.prisma.certificate.findMany({
      where: { customerId },
    });
    return certificates.map(CertificateMapper.toDomain);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.certificate.delete({
      where: { id },
    });
  }
} 