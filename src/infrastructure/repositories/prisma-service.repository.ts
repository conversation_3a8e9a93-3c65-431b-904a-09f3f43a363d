import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ServiceRepositoryPort } from '../../core/ports/repositories/service-repository.port';
import { Service } from '../../core/domain/service/entities/service.entity';
import { EntityType } from '../../core/domain/service/enums/entity-type.enum';
import { 
  EntityType as PrismaEntityType,
  Prisma
} from '@prisma/client';

@Injectable()
export class PrismaServiceRepository implements ServiceRepositoryPort {
  constructor(private readonly prisma: PrismaService) {}

  async create(service: Service): Promise<Service> {
    const created = await this.prisma.service.create({
      data: {
        id: service.id,
        entityType: service.entityType as PrismaEntityType,
        entityUuid: service.entityUuid,
        type: service.type,
        rate: service.rate,
        description: service.description,
        createdBy: service.createdBy,
        updatedBy: service.updatedBy,
        createdAt: service.createdAt,
        updatedAt: service.updatedAt,
        deletedAt: service.deletedAt,
      },
    });

    return this.toDomainEntity(created);
  }

  async findById(id: string): Promise<Service | null> {
    const service = await this.prisma.service.findUnique({
      where: { 
        id,
        deletedAt: null 
      },
    });

    if (!service) return null;
    return this.toDomainEntity(service);
  }

  async findByEntityUuid(entityUuid: string, entityType: EntityType): Promise<Service[]> {
    const services = await this.prisma.service.findMany({
      where: {
        entityUuid,
        entityType: entityType as PrismaEntityType,
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });

    return services.map(service => this.toDomainEntity(service));
  }

  async findAll(): Promise<Service[]> {
    const services = await this.prisma.service.findMany({
      where: { deletedAt: null },
      orderBy: { createdAt: 'desc' },
    });

    return services.map(service => this.toDomainEntity(service));
  }

  async update(service: Service): Promise<Service> {
    const updated = await this.prisma.service.update({
      where: { id: service.id },
      data: {
        type: service.type,
        rate: service.rate,
        description: service.description,
        updatedBy: service.updatedBy,
        updatedAt: service.updatedAt,
        deletedAt: service.deletedAt,
      },
    });

    return this.toDomainEntity(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.service.update({
      where: { id },
      data: { 
        deletedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  async findWithPagination(params: {
    limit: number;
    offset: number;
    entityUuid?: string;
    entityType?: EntityType;
    type?: string;
  }): Promise<{ items: Service[]; total: number }> {
    const where: Prisma.ServiceWhereInput = {
      deletedAt: null,
    };

    if (params.entityUuid) {
      where.entityUuid = params.entityUuid;
    }

    if (params.entityType) {
      where.entityType = params.entityType as PrismaEntityType;
    }

    if (params.type) {
      where.type = {
        contains: params.type,
        mode: 'insensitive',
      };
    }

    const [services, total] = await this.prisma.$transaction([
      this.prisma.service.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.service.count({ where }),
    ]);

    return {
      items: services.map(service => this.toDomainEntity(service)),
      total,
    };
  }

  private toDomainEntity(prismaService: any): Service {
    return new Service(
      prismaService.id,
      prismaService.entityType as EntityType,
      prismaService.entityUuid,
      prismaService.type,
      prismaService.rate,
      prismaService.description,
      prismaService.createdBy,
      prismaService.createdAt,
      prismaService.updatedAt,
      prismaService.updatedBy,
      prismaService.deletedAt,
    );
  }
} 