import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SupplierRepositoryPort } from '../../core/ports/repositories/supplier-repository.port';
import { Supplier } from '../../core/domain/supplier/entities/supplier.entity';
import { Address } from '../../core/domain/supplier/value-objects/address.value-object';
import { Contact } from '../../core/domain/supplier/value-objects/contact.value-object';
import { SupplierStatus } from '../../core/domain/supplier/enums/supplier-status.enum';
import {
  Prisma,
  SupplierStatus as PrismaSupplierStatus,
  SupplierType as PrismaSupplierType,
  SupplierClassification as PrismaSupplierClassification,
  TaxRegime,
  CompanySize,
} from '@prisma/client';
import { SupplierType } from '../../core/domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '@/core/domain/supplier/enums/supplier-classification.enum';

type PrismaSupplier = {
  id: string;
  name: string;
  document: string;
  tradeName: string | null;
  address: Prisma.JsonValue;
  email: string;
  userId: string;
  classification: PrismaSupplierClassification;
  type: PrismaSupplierType;
  status: PrismaSupplierStatus;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  updatedBy: string;
  stateRegistration: string | undefined;
  municipalRegistration: string | undefined;
  taxRegime: TaxRegime | undefined;
  companySize: CompanySize | undefined;
  deletedAt?: Date | null;
};

const toPrismaStatus = (status: SupplierStatus): PrismaSupplierStatus => {
  const statusMap: Record<SupplierStatus, PrismaSupplierStatus> = {
    [SupplierStatus.ACTIVE]: PrismaSupplierStatus.ACTIVE,
    [SupplierStatus.INACTIVE]: PrismaSupplierStatus.INACTIVE,
    [SupplierStatus.PENDING]: PrismaSupplierStatus.PENDING,
  };
  return statusMap[status];
};

const toDomainStatus = (status: PrismaSupplierStatus): SupplierStatus => {
  const statusMap: Record<PrismaSupplierStatus, SupplierStatus> = {
    [PrismaSupplierStatus.ACTIVE]: SupplierStatus.ACTIVE,
    [PrismaSupplierStatus.INACTIVE]: SupplierStatus.INACTIVE,
    [PrismaSupplierStatus.PENDING]: SupplierStatus.PENDING,
  };
  return statusMap[status];
};

const toPrismaType = (type: SupplierType): PrismaSupplierType => {
  const typeMap: Record<SupplierType, PrismaSupplierType> = {
    [SupplierType.BANK]: PrismaSupplierType.BANK,
    [SupplierType.GAME]: PrismaSupplierType.GAME,
    [SupplierType.SPORTSBOOK]: PrismaSupplierType.SPORTSBOOK,
    [SupplierType.KYC]: PrismaSupplierType.KYC,
    [SupplierType.OTHER]: PrismaSupplierType.OTHER,
  };
  return typeMap[type];
};

const toDomainType = (type: PrismaSupplierType): SupplierType => {
  const typeMap: Record<PrismaSupplierType, SupplierType> = {
    [PrismaSupplierType.BANK]: SupplierType.BANK,
    [PrismaSupplierType.GAME]: SupplierType.GAME,
    [PrismaSupplierType.SPORTSBOOK]: SupplierType.SPORTSBOOK,
    [PrismaSupplierType.KYC]: SupplierType.KYC,
    [PrismaSupplierType.OTHER]: SupplierType.OTHER,
  };
  return typeMap[type];
};

const toDomainClassification = (
  classification: PrismaSupplierClassification,
): SupplierClassification => {
  const classificationMap: Record<
    PrismaSupplierClassification,
    SupplierClassification
  > = {
    [PrismaSupplierClassification.CORE]: SupplierClassification.CORE,
    [PrismaSupplierClassification.GENERAL]: SupplierClassification.GENERAL,
  };
  return classificationMap[classification];
};

@Injectable()
export class PrismaSupplierRepository implements SupplierRepositoryPort {
  constructor(private readonly prisma: PrismaService) { }

  async findAll(): Promise<Supplier[]> {
    const suppliers = await this.prisma.supplier.findMany({
      where: { deletedAt: null },
    });
    return suppliers.map((supplier) => {
      const { ...rest } = supplier;
      return this.mapToDomain(rest as PrismaSupplier);
    });
  }

  async findById(id: string): Promise<Supplier | null> {
    const supplier = await this.prisma.supplier.findUnique({
      where: { id },
    });
    if (!supplier || supplier.deletedAt) return null;
    const { ...rest } = supplier;
    return this.mapToDomain(rest as PrismaSupplier);
  }

  async findByUserId(userId: string): Promise<Supplier | null> {
    const supplier = await this.prisma.supplier.findUnique({
      where: { userId },
    });
    if (!supplier || supplier.deletedAt) return null;
    const { ...rest } = supplier;
    return this.mapToDomain(rest as PrismaSupplier);
  }

  async findByDocument(document: string): Promise<Supplier | null> {
    const supplier = await this.prisma.supplier.findUnique({
      where: { document },
    });
    if (!supplier || supplier.deletedAt) return null;
    const { email, ...rest } = supplier;
    return this.mapToDomain({ ...rest, email } as PrismaSupplier);
  }

  async create(supplier: Supplier): Promise<Supplier> {
    if (!supplier.userId) {
      throw new Error('User ID is required');
    }

    const data = {
      id: supplier.id,
      name: supplier.name,
      document: supplier.document,
      tradeName: supplier.tradeName,
      email: supplier.email,
      classification: supplier.classification,
      address: supplier.address.toJSON(),
      type: toPrismaType(supplier.type),
      status: toPrismaStatus(supplier.status),
      userId: supplier.userId,
      createdBy: supplier.createdBy,
      updatedBy: supplier.updatedBy,
      stateRegistration: supplier.stateRegistration,
      municipalRegistration: supplier.municipalRegistration,
      taxRegime: supplier.taxRegime,
      companySize: supplier.companySize,
    };

    const createdSupplier = await this.prisma.supplier.create({ data });
    return this.mapToDomain(createdSupplier as PrismaSupplier);
  }

  async update(supplier: Supplier): Promise<Supplier> {
    if (!supplier.userId) {
      throw new Error('User ID is required');
    }

    const data = {
      name: supplier.name,
      document: supplier.document,
      tradeName: supplier.tradeName,
      email: supplier.email,
      classification: supplier.classification,
      type: toPrismaType(supplier.type),
      status: toPrismaStatus(supplier.status),
      updatedBy: supplier.updatedBy,
      address: supplier.address.toJSON(),
      userId: supplier.userId,
      stateRegistration: supplier.stateRegistration,
      municipalRegistration: supplier.municipalRegistration,
      taxRegime: supplier.taxRegime,
      companySize: supplier.companySize,
    };

    const updatedSupplier = await this.prisma.supplier.update({
      where: { id: supplier.id },
      data,
    });

    return this.mapToDomain(updatedSupplier as PrismaSupplier);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.supplier.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  async findWithPagination(params: {
    limit: number;
    offset: number;
    document?: string;
    name?: string;
    type?: SupplierType;
    status?: SupplierStatus;
  }): Promise<{ items: Supplier[]; total: number }> {
    const where: Prisma.SupplierWhereInput = { deletedAt: null };

    if (params.document) {
      where.document = params.document;
    }

    if (params.name) {
      where.name = {
        contains: params.name,
        mode: 'insensitive',
      };
    }

    if (params.type) {
      where.type = params.type;
    }

    if (params.status) {
      where.status = params.status;
    }

    const [items, total] = await Promise.all([
      this.prisma.supplier.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.supplier.count({ where }),
    ]);

    return {
      items: items.map((supplier) => {
        const { ...rest } = supplier;
        const email = (supplier as any).email ?? '';
        return this.mapToDomain({ ...rest, email } as PrismaSupplier);
      }),
      total,
    };
  }

  private mapToDomain(prismaSupplier: PrismaSupplier): Supplier {
    const addressData = prismaSupplier.address as {
      street: string;
      number?: string | null;
      complement?: string | null;
      neighborhood?: string | null;
      city: string;
      zipCode: string;
      state: string;
    };

    const address = new Address(
      addressData.street,
      addressData.number || null,
      addressData.complement || null,
      addressData.neighborhood || null,
      addressData.city,
      addressData.zipCode,
      addressData.state,
    );

    return new Supplier(
      prismaSupplier.id,
      prismaSupplier.name,
      prismaSupplier.document,
      prismaSupplier.tradeName,
      address,
      prismaSupplier.email,
      toDomainClassification(prismaSupplier.classification),
      toDomainType(prismaSupplier.type),
      toDomainStatus(prismaSupplier.status),
      prismaSupplier.userId,
      prismaSupplier.createdBy,
      prismaSupplier.createdAt,
      prismaSupplier.updatedAt,
      prismaSupplier.updatedBy,
      prismaSupplier.stateRegistration,
      prismaSupplier.municipalRegistration,
      prismaSupplier.taxRegime,
      prismaSupplier.companySize,
    );
  }
}
