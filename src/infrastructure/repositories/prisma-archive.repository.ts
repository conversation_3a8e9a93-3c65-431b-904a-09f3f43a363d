import { ArchiveRepositoryPort } from "@/core/ports/repositories/archive-repository.port";
import { Injectable } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { Archive } from "@prisma/client";

@Injectable()
export class PrismaArchiveRepository implements ArchiveRepositoryPort {
  constructor(private readonly prisma: PrismaService) { }
  async create(archive: Omit<Archive, 'id'>): Promise<Archive> {
    return this.prisma.archive.create({
      data: archive,
    });
  }

  async findByEmployeeUuid(employeeUuid: string): Promise<Archive[]> {
    return this.prisma.archive.findMany({
      where: {
        employeeUuid,
        deletedAt: null,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.archive.update({
      where: { id },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  async findOne(id: string): Promise<Archive | null> {
    return this.prisma.archive.findFirst({
      where: {
        id,
        deletedAt: null,
      },
    }) ?? null;
  }
}