import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { LoginDto } from '../../../modules/auth/dto/login.dto';
import { RegisterDto } from '../../../modules/auth/dto/register.dto';
import { RefreshTokenDto } from '../../../modules/auth/dto/refresh-token.dto';
import { LogoutDto } from '../../../modules/auth/dto/logout.dto';
import { TokenResponseDto } from '../../../modules/auth/dto/token-response.dto';
import { ValidateResponseDto } from '../../../modules/auth/dto/validate-response.dto';
import { UserProfileDto } from '../../../modules/auth/dto/user-profile.dto';

/**
 * Decorador para a documentação do endpoint de login
 */
export function ApiAuthLogin() {
  return applyDecorators(
    ApiOperation({ summary: 'Login with username and password' }),
    ApiBody({
      type: LoginDto,
      description: 'User credentials',
      examples: {
        example1: {
          value: {
            username: '<EMAIL>',
            password: 'testpass',
          },
          summary: 'Login with email and password',
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Login realizado com sucesso',
      type: TokenResponseDto,
    }),
    ApiResponse({ status: 401, description: 'Credenciais inválidas' }),
  );
}

/**
 * Decorador para a documentação do endpoint de registro de usuário
 */
export function ApiAuthRegister() {
  return applyDecorators(
    ApiOperation({ summary: 'Registra um novo usuário' }),
    ApiBody({
      type: RegisterDto,
      description: 'User registration data',
      examples: {
        employee: {
          value: {
            name: 'Novo Funcionário',
            email: '<EMAIL>',
            password: 'Senha123!',
            type: 'EMPLOYEE',
            cpf: '12345678901',
          },
          summary: 'Registro de funcionário (EMPLOYEE)',
        },
        customer: {
          value: {
            name: 'Novo Cliente',
            email: '<EMAIL>',
            password: 'Senha123!',
            type: 'CUSTOMER',
            cnpj: '12345678000195',
          },
          summary: 'Registro de cliente (CUSTOMER)',
        },
      },
    }),
    ApiResponse({ status: 201, description: 'Usuário registrado com sucesso' }),
    ApiResponse({
      status: 400,
      description: 'Dados inválidos ou usuário já existente',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de refresh token
 */
export function ApiAuthRefresh() {
  return applyDecorators(
    ApiOperation({ summary: 'Refresh token' }),
    ApiBody({
      type: RefreshTokenDto,
      description: 'Refresh token',
      examples: {
        example1: {
          value: {
            refresh_token:
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
          },
          summary: 'Refresh an access token',
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Token atualizado com sucesso',
      type: TokenResponseDto,
    }),
    ApiResponse({
      status: 401,
      description: 'Refresh token inválido ou expirado',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de validação de token
 */
export function ApiAuthValidate() {
  return applyDecorators(
    ApiOperation({
      summary: 'Valida se o token JWT é válido e retorna os dados do usuário',
    }),
    ApiResponse({
      status: 200,
      description: 'Token válido, usuário autenticado',
      type: ValidateResponseDto,
    }),
    ApiResponse({ status: 401, description: 'Token inválido ou expirado' }),
  );
}

/**
 * Decorador para a documentação do endpoint de perfil do usuário
 */
export function ApiAuthProfile() {
  return applyDecorators(
    ApiOperation({ summary: 'Get user profile' }),
    ApiResponse({
      status: 200,
      description: 'Perfil do usuário retornado com sucesso',
      type: UserProfileDto,
    }),
    ApiResponse({ status: 401, description: 'Token inválido ou expirado' }),
  );
}

/**
 * Decorador para a documentação do endpoint de logout
 */
export function ApiAuthLogout() {
  return applyDecorators(
    ApiOperation({ summary: 'Realiza o logout do usuário' }),
    ApiBody({
      type: LogoutDto,
      description: 'Logout data',
      examples: {
        example1: {
          value: {
            refresh_token:
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
          },
          summary: 'Logout with refresh token',
        },
      },
    }),
    ApiResponse({ status: 204, description: 'Logout realizado com sucesso' }),
    ApiResponse({ status: 400, description: 'Token de atualização inválido' }),
  );
}

/**
 * Decorador para a documentação do endpoint de recuperação de senha
 */
export const ApiAuthForgotPassword = () => {
  return applyDecorators(
    ApiOperation({ summary: 'Solicitar recuperação de senha' }),
    ApiResponse({
      status: 204,
      description: 'Email de recuperação enviado com sucesso',
    }),
    ApiResponse({
      status: 400,
      description: 'Requisição inválida',
      schema: {
        type: 'object',
        properties: {
          code: { type: 'string', example: 'BadRequest' },
          message: { type: 'string', example: 'Requisição inválida' },
          details: { type: 'array', items: { type: 'string' } },
        },
      },
    }),
    ApiResponse({
      status: 500,
      description: 'Erro interno do servidor',
      schema: {
        type: 'object',
        properties: {
          code: { type: 'string', example: 'InternalError' },
          message: { type: 'string', example: 'Erro interno do servidor' },
        },
      },
    }),
  );
};
