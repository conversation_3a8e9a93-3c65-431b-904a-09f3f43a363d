import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { CreatePayablesTypeDto } from '../../../modules/finance/dto/create-payables-type.dto';
import { UpdatePayablesTypeDto } from '../../../modules/finance/dto/update-payables-type.dto';

/**
 * Decorador para a documentação do endpoint de criação de tipo de despesa
 */
export function ApiCreatePayablesType() {
  return applyDecorators(
    ApiOperation({ summary: 'Create payable type' }),
    ApiBody({
      type: CreatePayablesTypeDto,
      description: 'Payable type data',
      examples: {
        example1: {
          value: {
            code: 'UTILITIES',
            description: 'Despesas com serviços públicos (água, luz, internet)',
            createdBy: '11111111-**************-************',
            updatedBy: '11111111-**************-************',
          },
          summary: 'Create a new payable type',
        },
      },
    }),
    ApiResponse({
      status: 201,
      description: 'O tipo de despesa foi criado com sucesso.',
    }),
    ApiResponse({
      status: 400,
      description:
        'Dados inválidos fornecidos. Verifique o formato dos campos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
    ApiResponse({
      status: 409,
      description:
        'Conflito. Já existe um tipo de despesa cadastrado com este código.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de busca de tipo de despesa por UUID
 */
export function ApiGetPayablesType() {
  return applyDecorators(
    ApiOperation({ summary: 'Get payable type by UUID' }),
    ApiParam({
      name: 'uuid',
      description: 'Payable type UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({
      status: 200,
      description: 'Tipo de despesa encontrado com sucesso',
    }),
    ApiResponse({ status: 400, description: 'UUID inválido' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 404,
      description: 'Tipo de despesa não encontrado.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de listagem de tipos de despesa
 */
export function ApiListPayablesTypes() {
  return applyDecorators(
    ApiOperation({ summary: 'List payable types' }),
    ApiResponse({
      status: 200,
      description: 'Lista de tipos de despesa retornada com sucesso',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de atualização de tipo de despesa
 */
export function ApiUpdatePayablesType() {
  return applyDecorators(
    ApiOperation({ summary: 'Update payable type' }),
    ApiParam({
      name: 'uuid',
      description: 'Payable type UUID',
      example: '11111111-**************-************',
    }),
    ApiBody({
      type: UpdatePayablesTypeDto,
      description: 'Payable type update data',
      examples: {
        example1: {
          value: {
            code: 'UTILITIES_UPDATED',
            description:
              'Despesas atualizadas com serviços públicos (água, luz, internet)',
          },
          summary: 'Update an existing payable type',
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Tipo de despesa atualizado com sucesso',
    }),
    ApiResponse({
      status: 400,
      description:
        'Dados inválidos fornecidos. Verifique o formato dos campos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
    ApiResponse({
      status: 404,
      description: 'Tipo de despesa não encontrado.',
    }),
    ApiResponse({
      status: 409,
      description:
        'Conflito. Já existe um tipo de despesa cadastrado com este código.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de exclusão de tipo de despesa
 */
export function ApiDeletePayablesType() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete payable type' }),
    ApiParam({
      name: 'uuid',
      description: 'Payable type UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({
      status: 204,
      description: 'Tipo de despesa deletado com sucesso',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
    ApiResponse({
      status: 404,
      description: 'Tipo de despesa não encontrado.',
    }),
  );
}
