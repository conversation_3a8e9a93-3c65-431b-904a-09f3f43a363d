import { applyDecorators, HttpStatus } from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiOperation,
  ApiNotFoundResponse,
  ApiNoContentResponse,
  ApiResponse,
} from '@nestjs/swagger';
import { CostCenterResponseDto } from '@modules/finance/cost-center/dto/cost-center-response.dto';

export function ApiCreateCostCenter() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new cost center' }),
    ApiCreatedResponse({
      description: 'The cost center has been successfully created.',
      type: CostCenterResponseDto,
    }),
    ApiBadRequestResponse({ description: 'Bad Request - Invalid input data.' }),
    ApiUnauthorizedResponse({
      description: 'Unauthorized - Invalid or missing token.',
    }),
    ApiForbiddenResponse({
      description: 'Forbidden - User does not have required role.',
    }),
  );
}

export function ApiDeleteCostCenter() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete a cost center' }),
    ApiNoContentResponse({
      description: 'The cost center has been successfully deleted.',
    }),
    ApiNotFoundResponse({ description: 'Cost center not found.' }),
  );
}

export function ApiUpdateCostCenter() {
  return applyDecorators(
    ApiOperation({ summary: 'Update a cost center' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Cost center updated successfully',
    }),
    ApiResponse({
      status: HttpStatus.BAD_REQUEST,
      description: 'Invalid payload',
    }),
    ApiResponse({
      status: HttpStatus.UNAUTHORIZED,
      description: 'Unauthorized',
    }),
    ApiResponse({
      status: HttpStatus.FORBIDDEN,
      description: 'Forbidden',
    }),
    ApiResponse({
      status: HttpStatus.NOT_FOUND,
      description: 'Cost center not found',
    }),
  );
}
