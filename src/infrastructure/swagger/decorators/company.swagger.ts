import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { CreateCompanyDto } from '../../../modules/company/dto/create-company.dto';
import { UpdateCompanyDto } from '../../../modules/company/dto/update-company.dto';

/**
 * Decorador para a documentação do endpoint de criação de empresa
 */
export function ApiCreateCompany() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new company' }),
    ApiBody({
      type: CreateCompanyDto,
      description: 'Company data',
      examples: {
        example1: {
          value: {
            name: 'Acme Corporation',
            cnpj: '12345678000195',
            address: {
              street: 'Av. Paulista, 1000',
              city: 'São Paulo',
              zipCode: '01310-100',
              state: 'SP',
            },
            phone: '(11) 98765-4321',
            email: '<EMAIL>',
            status: 'active',
          },
          summary: 'Create a new company',
        },
      },
    }),
    ApiResponse({ status: 201, description: 'Empresa criada com sucesso' }),
    ApiResponse({
      status: 400,
      description:
        'Dados inválidos fornecidos. Verifique o formato dos campos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
    ApiResponse({
      status: 409,
      description: 'Conflito. Já existe uma empresa cadastrada com este CNPJ.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de busca de empresa por UUID
 */
export function ApiGetCompany() {
  return applyDecorators(
    ApiOperation({ summary: 'Get company by UUID' }),
    ApiParam({
      name: 'uuid',
      description: 'Company UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({ status: 200, description: 'Empresa encontrada com sucesso' }),
    ApiResponse({ status: 400, description: 'UUID inválido' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({ status: 404, description: 'Empresa não encontrada.' }),
  );
}

/**
 * Decorador para a documentação do endpoint de listagem de empresas
 */
export function ApiListCompanies() {
  return applyDecorators(
    ApiOperation({ summary: 'List companies' }),
    ApiResponse({
      status: 200,
      description: 'Lista de empresas retornada com sucesso',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de atualização de empresa
 */
export function ApiUpdateCompany() {
  return applyDecorators(
    ApiOperation({ summary: 'Update a company' }),
    ApiParam({
      name: 'uuid',
      description: 'Company UUID',
      example: '11111111-**************-************',
    }),
    ApiBody({
      type: UpdateCompanyDto,
      description: 'Company update data',
      examples: {
        example1: {
          value: {
            name: 'Acme Corporation Updated',
            phone: '(11) 98888-7777',
            email: '<EMAIL>',
            status: 'inactive',
          },
          summary: 'Update an existing company',
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Empresa atualizada com sucesso' }),
    ApiResponse({
      status: 400,
      description:
        'Dados inválidos fornecidos. Verifique o formato dos campos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
    ApiResponse({ status: 404, description: 'Empresa não encontrada.' }),
    ApiResponse({
      status: 409,
      description: 'Conflito. Já existe uma empresa cadastrada com este CNPJ.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de exclusão de empresa
 */
export function ApiDeleteCompany() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete a company' }),
    ApiParam({
      name: 'uuid',
      description: 'Company UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({ status: 204, description: 'Empresa deletada com sucesso' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
    ApiResponse({ status: 404, description: 'Empresa não encontrada.' }),
  );
}
