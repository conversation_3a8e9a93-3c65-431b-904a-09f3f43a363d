import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import {
  CustomerListResponseDto,
  CustomerResponseDto,
} from '../../../modules/customers/infrastructure/dtos/customer-list.dto';

export function ApiCreateCustomer() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new customer' }),
    ApiResponse({
      status: HttpStatus.CREATED,
      description: 'Customer created successfully',
      type: CustomerResponseDto,
    }),
    ApiResponse({
      status: HttpStatus.BAD_REQUEST,
      description: 'Invalid input data',
    }),
    ApiResponse({
      status: HttpStatus.CONFLICT,
      description: 'Customer with the same document already exists',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de listagem de clientes
 */
export function ApiListCustomers() {
  return applyDecorators(
    ApiOperation({ summary: 'List customers with filtering and pagination' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Customers retrieved successfully',
      type: CustomerListResponseDto,
    }),
    ApiResponse({
      status: HttpStatus.BAD_REQUEST,
      description: 'Invalid pagination parameters',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de obtenção de cliente por UUID
 */
export function ApiGetCustomer() {
  return applyDecorators(
    ApiOperation({ summary: 'Get customer by UUID' }),
    ApiParam({
      name: 'uuid',
      description: 'Customer UUID',
      type: String,
      format: 'uuid',
      example: '123e4567-e89b-12d3-a456-************',
    }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Customer found successfully',
      type: CustomerResponseDto,
    }),
    ApiResponse({
      status: HttpStatus.BAD_REQUEST,
      description: 'Invalid UUID format',
    }),
    ApiResponse({
      status: HttpStatus.NOT_FOUND,
      description: 'Customer not found',
    }),
  );
}
