import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { CreatePaymentMethodDto } from '../../../modules/payment-method/dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from '../../../modules/payment-method/dto/update-payment-method.dto';

/**
 * Decorador para a documentação do endpoint de criação de método de pagamento
 */
export function ApiCreatePaymentMethod() {
  return applyDecorators(
    ApiOperation({ summary: 'Create payment method' }),
    ApiBody({
      type: CreatePaymentMethodDto,
      description: 'Payment method data',
      examples: {
        example1: {
          value: {
            label: 'Boleto Bancário',
            description:
              'Pagamento via boleto bancário com compensação em até 3 dias úteis',
          },
          summary: 'Create a new payment method',
        },
      },
    }),
    ApiResponse({
      status: 201,
      description: 'Forma de pagamento criada com sucesso',
    }),
    ApiResponse({
      status: 400,
      description:
        'Dados inválidos fornecidos. Verifique o formato dos campos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador financeiro.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de busca de método de pagamento por UUID
 */
export function ApiGetPaymentMethod() {
  return applyDecorators(
    ApiOperation({ summary: 'Get payment method by UUID' }),
    ApiParam({
      name: 'uuid',
      description: 'Payment method UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({
      status: 200,
      description: 'Método de pagamento encontrado com sucesso',
    }),
    ApiResponse({ status: 400, description: 'UUID inválido' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 404,
      description: 'Método de pagamento não encontrado.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de listagem de métodos de pagamento
 */
export function ApiListPaymentMethods() {
  return applyDecorators(
    ApiOperation({ summary: 'List payment methods' }),
    ApiResponse({
      status: 200,
      description: 'Lista de métodos de pagamento retornada com sucesso',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de atualização de método de pagamento
 */
export function ApiUpdatePaymentMethod() {
  return applyDecorators(
    ApiOperation({ summary: 'Update payment method' }),
    ApiParam({
      name: 'uuid',
      description: 'Payment method UUID',
      example: '11111111-**************-************',
    }),
    ApiBody({
      type: UpdatePaymentMethodDto,
      description: 'Payment method update data',
      examples: {
        example1: {
          value: {
            label: 'Boleto Bancário (Atualizado)',
            description:
              'Pagamento via boleto bancário com compensação em até 2 dias úteis',
          },
          summary: 'Update an existing payment method',
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Método de pagamento atualizado com sucesso',
    }),
    ApiResponse({
      status: 400,
      description:
        'Dados inválidos fornecidos. Verifique o formato dos campos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador financeiro.',
    }),
    ApiResponse({
      status: 404,
      description: 'Método de pagamento não encontrado.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de exclusão de método de pagamento
 */
export function ApiDeletePaymentMethod() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete payment method' }),
    ApiParam({
      name: 'uuid',
      description: 'Payment method UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({
      status: 204,
      description: 'Método de pagamento deletado com sucesso',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador financeiro.',
    }),
    ApiResponse({
      status: 404,
      description: 'Método de pagamento não encontrado.',
    }),
  );
}
