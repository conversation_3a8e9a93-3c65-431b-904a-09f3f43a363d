import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { CreateUserDto } from '../../../modules/users/dto/create-user.dto';
import { UpdateUserDto } from '../../../modules/users/dto/update-user.dto';

/**
 * Decorador para a documentação do endpoint de listagem de usuários
 */
export function ApiListUsers() {
  return applyDecorators(
    ApiOperation({ summary: 'Get all users' }),
    ApiResponse({
      status: 200,
      description: 'Lista de usuários retornada com sucesso',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de obtenção de usuário por ID
 */
export function ApiGetUser() {
  return applyDecorators(
    ApiParam({
      name: 'id',
      description: 'User ID',
      example: '12345',
    }),
    ApiOperation({ summary: 'Get user by ID' }),
    ApiResponse({ status: 200, description: 'Usuário encontrado com sucesso' }),
    ApiResponse({ status: 404, description: 'Usuário não encontrado' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de criação de usuário
 */
export function ApiCreateUser() {
  return applyDecorators(
    ApiOperation({ summary: 'Create new user' }),
    ApiBody({
      type: CreateUserDto,
      description: 'User data',
      examples: {
        example1: {
          value: {
            name: 'John Doe',
            email: '<EMAIL>',
            password: 'Secure123!',
            // role: 'ADMIN',
          },
          summary: 'Create a new user',
        },
      },
    }),
    ApiResponse({ status: 201, description: 'Usuário criado com sucesso' }),
    ApiResponse({ status: 400, description: 'Dados inválidos' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de atualização de usuário
 */
export function ApiUpdateUser() {
  return applyDecorators(
    ApiOperation({ summary: 'Update user' }),
    ApiParam({
      name: 'id',
      description: 'User ID',
      example: '12345',
    }),
    ApiBody({
      type: UpdateUserDto,
      description: 'User update data',
      examples: {
        example1: {
          value: {
            name: 'John Updated',
            // role: 'USER',
          },
          summary: 'Update an existing user',
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Usuário atualizado com sucesso' }),
    ApiResponse({ status: 404, description: 'Usuário não encontrado' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de exclusão de usuário
 */
export function ApiDeleteUser() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete user' }),
    ApiParam({
      name: 'id',
      description: 'User ID',
      example: '12345',
    }),
    ApiResponse({ status: 200, description: 'Usuário removido com sucesso' }),
    ApiResponse({ status: 404, description: 'Usuário não encontrado' }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado. Token de acesso inválido ou ausente.',
    }),
    ApiResponse({
      status: 403,
      description:
        'Acesso negado. Usuário não possui permissão de administrador.',
    }),
  );
}
