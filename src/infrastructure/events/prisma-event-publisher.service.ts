import { Injectable } from '@nestjs/common';
import { EventPublisherPort } from '../../core/ports/event-publisher.port';
import { DomainEvent } from '../../core/domain/base/domain-event';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class PrismaEventPublisherService implements EventPublisherPort {
  constructor(private readonly prisma: PrismaService) {}

  async publish(event: DomainEvent): Promise<void> {
    await this.prisma.domainEvent.create({
      data: {
        eventId: event.eventId,
        eventName: event.eventName,
        occurredAt: event.occurredAt,
        correlationId: event.correlationId,
        data: event.getData(),
      },
    });
  }

  async publishAll(events: DomainEvent[]): Promise<void> {
    await Promise.all(events.map((event) => this.publish(event)));
  }
}
