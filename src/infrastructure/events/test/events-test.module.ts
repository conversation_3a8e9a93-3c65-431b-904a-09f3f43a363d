import { Module } from '@nestjs/common';
import { EventEmitterService } from '../event-emitter.service';
import { DomainEventsModule } from '../domain-events.module';
import { EventPublisherService } from '../event-publisher.service';
import { ConfigModule } from '@nestjs/config';
import { AwsModule } from '../../aws/aws.module';
import { ModuleRef } from '@nestjs/core';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    AwsModule,
  ],
  providers: [
    EventEmitterService,
    EventPublisherService,
    {
      provide: ModuleRef,
      useValue: {
        resolve: jest.fn(),
      },
    },
    {
      provide: DomainEventsModule,
      useFactory: (moduleRef: ModuleRef, eventEmitter: EventEmitterService) => {
        return new DomainEventsModule(moduleRef, eventEmitter);
      },
      inject: [ModuleRef, EventEmitterService],
    },
  ],
  exports: [EventEmitterService, EventPublisherService, DomainEventsModule],
})
export class EventsTestModule {}
