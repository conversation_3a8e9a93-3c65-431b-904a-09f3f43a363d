import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DomainEvent } from 'src/core/domain/events/domain-event';
import { EventEmitterService } from './event-emitter.service';
import { SNSService } from '../aws/sns/sns.service';

/**
 * Serviço responsável por publicar eventos de domínio
 * Suporta publicação local (in-memory) e remota (SNS)
 */
@Injectable()
export class EventPublisherService {
  private readonly enableSns: boolean;
  private readonly environmentName: string;

  constructor(
    private readonly eventEmitter: EventEmitterService,
    private readonly snsService: SNSService,
    private readonly configService: ConfigService,
  ) {
    this.enableSns = this.configService.get('AWS_ENABLE_SNS') === 'true';
    this.environmentName = this.configService.get('NODE_ENV') || 'development';
  }

  /**
   * Publica um evento de domínio
   * @param event Evento de domínio a ser publicado
   */
  async publish(event: DomainEvent): Promise<void> {
    // Emite o evento localmente
    this.eventEmitter.emit(event.eventName, event, {
      correlationId: event.correlationId,
      userId: event.userId,
      eventId: event.eventId,
    });

    // Se SNS estiver habilitado, publica também no SNS
    if (this.enableSns) {
      await this.publishToSns(event);
    }
  }

  /**
   * Publica múltiplos eventos de domínio
   * @param events Lista de eventos a serem publicados
   */
  async publishAll(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      await this.publish(event);
    }
  }

  /**
   * Publica o evento no SNS
   * @param event Evento de domínio
   */
  private async publishToSns(event: DomainEvent): Promise<void> {
    try {
      const topicName = `${this.environmentName}-domain-events`;

      // Obter o topicArn com base no nome do tópico ou criar se não existir
      let topicArn: string;

      try {
        // Verificar se já existe um tópico com esse nome
        const topics = await this.snsService.listTopics();
        const existingTopic = topics.find(
          (topic) => topic.TopicArn && topic.TopicArn.endsWith(topicName),
        );

        if (existingTopic && existingTopic.TopicArn) {
          topicArn = existingTopic.TopicArn;
        } else {
          // Criar o tópico se não existir
          topicArn = await this.snsService.createTopic(topicName);
        }
      } catch (error) {
        console.error('Erro ao obter/criar tópico:', error);
        return;
      }

      // Publicar o evento no tópico
      const eventData = event.serialize() as Record<string, unknown>;
      await this.snsService.publish(topicArn, eventData, event.eventName);
    } catch (error) {
      console.error('Erro ao publicar evento no SNS:', error);
      // Não deve falhar a aplicação se o SNS falhar, apenas logar o erro
    }
  }
}
