import { Injectable } from '@nestjs/common';
import { Subject, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface EventData<T = unknown> {
  type: string;
  payload: T;
  metadata?: {
    timestamp: string;
    correlationId?: string;
    userId?: string;
    [key: string]: unknown;
  };
}

@Injectable()
export class EventEmitterService {
  private eventBus = new Subject<EventData<unknown>>();

  /**
   * Emite um evento para os assinantes
   * @param type Tipo do evento
   * @param payload Dados do evento
   * @param metadata Metadados adicionais (opcional)
   */
  emit<T>(type: string, payload: T, metadata?: Record<string, unknown>): void {
    const event: EventData<T> = {
      type,
      payload,
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata,
      },
    };

    console.log(`Evento emitido: ${type}`, { payload });
    this.eventBus.next(event as EventData<unknown>);
  }

  /**
   * Assina um tipo específico de evento
   * @param type Tipo do evento a ser assinado
   * @returns Observable que emite apenas os eventos do tipo especificado
   */
  on<T = unknown>(type: string): Observable<T> {
    return this.eventBus.asObservable().pipe(
      filter((event) => event.type === type),
      map((event) => event.payload as T),
    );
  }

  /**
   * Assina todos os eventos e filtra com base em uma função de predicado
   * @param predicate Função para filtrar eventos
   * @returns Observable que emite eventos que atendem ao predicado
   */
  onMany(
    predicate: (event: EventData<unknown>) => boolean,
  ): Observable<EventData<unknown>> {
    return this.eventBus.asObservable().pipe(filter(predicate));
  }

  /**
   * Assina todos os eventos
   * @returns Observable que emite todos os eventos
   */
  onAll(): Observable<EventData<unknown>> {
    return this.eventBus.asObservable();
  }
}
