import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as amqp from 'amqplib';

// Definindo tipos mais específicos para evitar problemas de tipagem
type RabbitMQConnection = amqp.Connection & {
  createChannel: () => Promise<amqp.Channel>;
  close: () => Promise<void>;
};

type RabbitMQChannel = amqp.Channel & {
  close: () => Promise<void>;
};

@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  private connection: RabbitMQConnection | null = null;
  private channel: RabbitMQChannel | null = null;
  private readonly maxRetries = 5;
  private readonly retryDelay = 5000; // 5 seconds

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    await this.connectWithRetry();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connectWithRetry(retryCount = 0): Promise<void> {
    try {
      await this.connect();
    } catch (error) {
      if (retryCount < this.maxRetries) {
        console.log(
          `Retrying RabbitMQ connection (${retryCount + 1}/${this.maxRetries})...`,
        );
        await new Promise((resolve) => setTimeout(resolve, this.retryDelay));
        return this.connectWithRetry(retryCount + 1);
      }
      throw error;
    }
  }

  private async connect() {
    try {
      const host = this.configService.get<string>('RABBITMQ_HOST');
      const port = this.configService.get<string>('RABBITMQ_PORT');
      const username = this.configService.get<string>('RABBITMQ_USERNAME');
      const password = this.configService.get<string>('RABBITMQ_PASSWORD');

      if (!host || !port || !username || !password) {
        throw new Error('Missing RabbitMQ configuration');
      }

      const url =
        process.env.NODE_ENV === 'production'
          ? `amqps://${username}:${password}@${host}:${port}`
          : `amqp://${username}:${password}@${host}:${port}`;

      console.log(`Attempting to connect to RabbitMQ at ${host}:${port}`);

      const conn = await amqp.connect(url);
      if (!conn) {
        throw new Error('Failed to establish connection with RabbitMQ');
      }

      this.connection = conn as unknown as RabbitMQConnection;
      if (this.connection) {
        const ch = await this.connection.createChannel();
        if (!ch) {
          throw new Error('Failed to create channel in RabbitMQ');
        }
        this.channel = ch as unknown as RabbitMQChannel;
        console.log('Successfully connected to RabbitMQ');
      }
    } catch (error: unknown) {
      this.logError('Error connecting to RabbitMQ', error);
      throw new Error('Failed to connect to RabbitMQ');
    }
  }

  private async disconnect() {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      console.log('Desconectado do RabbitMQ com sucesso');
    } catch (error: unknown) {
      this.logError('Erro ao desconectar do RabbitMQ', error);
    }
  }

  /**
   * Método auxiliar para logar erros de forma segura
   * @param message Mensagem de contexto do erro
   * @param error Objeto de erro
   */
  private logError(message: string, error: unknown): void {
    let errorDetails: string;

    if (error instanceof Error) {
      errorDetails = error.message;
      if (error.stack) {
        console.debug(error.stack);
      }
    } else if (error !== null && error !== undefined) {
      try {
        if (typeof error === 'object') {
          const safeObject = Object.entries(
            error as Record<string, unknown>,
          ).reduce<Record<string, unknown>>((acc, [key, value]) => {
            if (value instanceof Error) {
              acc[key] = {
                name: value.name,
                message: value.message,
                stack: value.stack,
              };
            } else {
              acc[key] = value;
            }
            return acc;
          }, {});

          const stringified = JSON.stringify(safeObject);
          errorDetails = stringified || 'objeto vazio';
        } else {
          errorDetails =
            typeof error === 'symbol'
              ? error.toString()
              : typeof error === 'string'
                ? error
                : typeof error === 'number' ||
                    typeof error === 'boolean' ||
                    typeof error === 'bigint'
                  ? String(error)
                  : 'valor não serializável';
        }
      } catch {
        // Se falhar na stringificação, tentar uma abordagem mais segura
        try {
          const errorValue = (
            typeof error === 'object'
              ? Object.prototype.toString.call(error)
              : typeof error === 'string'
                ? error
                : typeof error === 'number' ||
                    typeof error === 'boolean' ||
                    typeof error === 'bigint'
                  ? String(error)
                  : 'valor não serializável'
          ) as string;

          const safeError = {
            type: typeof error as string,
            value: errorValue,
          };
          errorDetails = JSON.stringify(safeError);
        } catch {
          errorDetails = 'erro não serializável';
        }
      }
    } else {
      errorDetails = 'erro desconhecido';
    }

    console.error(`${message}: ${errorDetails}`);
  }

  /**
   * Publica uma mensagem em uma exchange
   * @param exchange - Nome da exchange
   * @param routingKey - Chave de roteamento
   * @param message - Mensagem para publicar (será convertida para JSON)
   * @param options - Opções adicionais
   */
  async publish(
    exchange: string,
    routingKey: string,
    message: Record<string, unknown>,
    options?: amqp.Options.Publish,
  ) {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      // Garantir que a exchange existe
      await this.channel.assertExchange(exchange, 'topic', { durable: true });

      // Publicar a mensagem
      const buffer = Buffer.from(JSON.stringify(message));
      this.channel.publish(exchange, routingKey, buffer, {
        persistent: true,
        ...options,
      });

      console.log(
        `Mensagem publicada na exchange ${exchange} com routing key ${routingKey}`,
      );
    } catch (error: unknown) {
      this.logError('Erro ao publicar mensagem no RabbitMQ', error);
      throw error;
    }
  }

  /**
   * Consome mensagens de uma fila
   * @param queue - Nome da fila
   * @param callback - Função de callback para processar as mensagens
   * @param options - Opções adicionais
   */
  async consume(
    queue: string,
    callback: (message: Record<string, unknown>) => Promise<void> | void,
    options?: amqp.Options.Consume,
  ) {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      // Garantir que a fila existe
      await this.channel.assertQueue(queue, { durable: true });

      // Consumir mensagens
      const consumeCallback = (msg: amqp.ConsumeMessage | null) => {
        if (msg && this.channel) {
          void (async () => {
            try {
              const content = JSON.parse(msg.content.toString()) as Record<
                string,
                unknown
              >;
              await Promise.resolve(callback(content));
              this.channel?.ack(msg);
            } catch (error: unknown) {
              this.logError(
                `Erro ao processar mensagem da fila ${queue}`,
                error,
              );
              // Rejeitar a mensagem com requeue
              this.channel?.nack(msg, false, true);
            }
          })();
        }
      };

      await this.channel.consume(queue, consumeCallback, { ...options });

      console.log(`Consumidor registrado para a fila ${queue}`);
    } catch (error: unknown) {
      this.logError('Erro ao consumir mensagens do RabbitMQ', error);
      throw error;
    }
  }

  /**
   * Vincula uma fila a uma exchange com uma routing key
   * @param queue - Nome da fila
   * @param exchange - Nome da exchange
   * @param pattern - Padrão da routing key
   */
  async bindQueue(queue: string, exchange: string, pattern: string) {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      // Garantir que a exchange e a fila existem
      await this.channel.assertExchange(exchange, 'topic', { durable: true });
      await this.channel.assertQueue(queue, { durable: true });

      // Vincular a fila à exchange
      await this.channel.bindQueue(queue, exchange, pattern);

      console.log(
        `Fila ${queue} vinculada à exchange ${exchange} com pattern ${pattern}`,
      );
    } catch (error: unknown) {
      this.logError('Erro ao vincular fila à exchange', error);
      throw error;
    }
  }
}
