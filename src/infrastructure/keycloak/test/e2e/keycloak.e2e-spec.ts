import { KeycloakService } from '../../keycloak.service';
import { KeycloakAdminUtils } from '../../keycloak.admin.utils';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../utils/request.utils.service';
import * as dotenv from 'dotenv';
import { KeycloakIdentityProviderService } from '../../keycloak-identity-provider.service';

dotenv.config({ path: '.env.test' });

describe('Keycloak E2E (real)', () => {
  let configService: ConfigService;
  let requestUtilsService: RequestUtilsService;
  let keycloakAdminUtils: KeycloakAdminUtils;
  let keycloakService: KeycloakService;
  let keycloakIdentityProviderService: KeycloakIdentityProviderService;
  let createdUserId: string;
  const testUser = {
    username: `e2euser_${Date.now()}`,
    email: `e2euser_${Date.now()}@test.com`,
    password: 'e2ePassword123',
    firstName: 'E2E',
    lastName: 'Test',
  };

  beforeAll(async () => {
    configService = new ConfigService();
    requestUtilsService = new RequestUtilsService(configService);
    keycloakAdminUtils = new KeycloakAdminUtils(
      configService,
      requestUtilsService,
    );
    await keycloakAdminUtils.createAllDomainRoles();
    keycloakService = new KeycloakService(
      {} as Record<string, unknown>,
      keycloakAdminUtils,
      configService,
      requestUtilsService,
    );
    keycloakIdentityProviderService = new KeycloakIdentityProviderService(
      keycloakService,
      keycloakAdminUtils,
    );
  });

  it('should create a real user in Keycloak', async () => {
    const adminToken = await keycloakAdminUtils.getAdminToken();
    createdUserId = await keycloakAdminUtils.createUser(adminToken, {
      username: testUser.username,
      email: testUser.email,
      firstName: testUser.firstName,
      lastName: testUser.lastName,
      enabled: true,
      credentials: [
        {
          type: 'password',
          value: testUser.password,
          temporary: false,
        },
      ],
    });
    expect(createdUserId).toBeDefined();
  });

  it('should authenticate the created user', async () => {
    const token = await keycloakIdentityProviderService.authenticate(
      testUser.username,
      testUser.password,
    );
    expect(token.access_token).toBeDefined();
  });

  it('should assign a role to the user', async () => {
    const adminToken = await keycloakAdminUtils.getAdminToken();
    await keycloakAdminUtils.assignRole(adminToken, createdUserId, 'user');
    // Não lança erro = sucesso
    expect(true).toBe(true);
  });

  afterAll(async () => {
    if (createdUserId) {
      const adminToken = await keycloakAdminUtils.getAdminToken();
      await keycloakAdminUtils.deleteUser(adminToken, createdUserId);
    }
  });
});
