import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KeycloakService } from './keycloak.service';
import { KeycloakAdminUtils } from './keycloak.admin.utils';
import { UtilsModule } from '../utils/utils-module';
import { KeycloakIdentityProviderService } from './keycloak-identity-provider.service';

@Module({
  imports: [ConfigModule, UtilsModule],
  providers: [
    {
      provide: 'KEYCLOAK_CONFIG',
      useFactory: (configService: ConfigService) => ({
        baseUrl: configService.get<string>('KEYCLOAK_BASE_URL'),
        realm: configService.get<string>('KEYCLOAK_REALM'),
        clientId: configService.get<string>('KEYCLOAK_CLIENT_ID'),
        clientSecret: configService.get<string>('KEYCLOAK_CLIENT_SECRET'),
        adminClientId: configService.get<string>('KEYCLOAK_ADMIN_CLIENT_ID'),
        adminClientSecret: configService.get<string>(
          'KEYCLOAK_ADMIN_CLIENT_SECRET',
        ),
        adminUsername: configService.get<string>('KEYCLOAK_ADMIN_USERNAME'),
        adminPassword: configService.get<string>('KEYCLOAK_ADMIN_PASSWORD'),
        maxRetries: configService.get<number>('MAX_RETRIES'),
        initialRetryDelayMs: configService.get<number>(
          'INITIAL_RETRY_DELAY_MS',
        ),
        errorThresholdPercentage: configService.get<number>(
          'KEYCLOAK_ERROR_THRESHOLD_PERCENTAGE',
        ),
        resetTimeout: configService.get<number>('KEYCLOAK_RESET_TIMEOUT'),
        rollingCountMaxErrors: configService.get<number>(
          'KEYCLOAK_ROLLING_COUNT_MAX_ERRORS',
        ),
      }),
      inject: [ConfigService],
    },
    KeycloakService,
    KeycloakAdminUtils,
    KeycloakIdentityProviderService,
    {
      provide: 'IdentityProviderService',
      useExisting: KeycloakIdentityProviderService,
    },
  ],
  exports: [
    KeycloakService,
    KeycloakAdminUtils,
    KeycloakIdentityProviderService,
    'IdentityProviderService',
  ],
})
export class KeycloakModule {}
