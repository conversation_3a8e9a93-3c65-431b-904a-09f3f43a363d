import {
  Injectable,
  UnauthorizedException,
  InternalServerErrorException,
  Inject,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { RequestUtilsService } from '../utils/request.utils.service';
import { KeycloakAdminUtils } from './keycloak.admin.utils';

interface TokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

interface UserInfo {
  sub: string;
  preferred_username: string;
  email: string;
  name?: string;
  roles?: string[];
}

@Injectable()
export class KeycloakService {
  private readonly logger = new Logger(KeycloakService.name);
  private readonly baseUrl: string;
  private readonly realm: string;
  private readonly clientId: string;
  private readonly clientSecret: string;

  constructor(
    @Inject('KEYCLOAK_CONFIG') private readonly config: Record<string, unknown>,
    private readonly keycloakAdminUtils: KeycloakAdminUtils,
    private readonly configService: ConfigService,
    private requestUtilsService: RequestUtilsService,
  ) {
    // Usar os valores do configService com verificações para evitar undefined
    const baseUrl = this.configService.get<string>('KEYCLOAK_BASE_URL');
    if (!baseUrl) {
      throw new Error('KEYCLOAK_BASE_URL não está definido no ambiente');
    }
    this.baseUrl = baseUrl;

    const realm = this.configService.get<string>('KEYCLOAK_REALM');
    if (!realm) {
      throw new Error('KEYCLOAK_REALM não está definido no ambiente');
    }
    this.realm = realm;

    const clientId = this.configService.get<string>('KEYCLOAK_CLIENT_ID');
    if (!clientId) {
      throw new Error('KEYCLOAK_CLIENT_ID não está definido no ambiente');
    }
    this.clientId = clientId;

    const clientSecret = this.configService.get<string>(
      'KEYCLOAK_CLIENT_SECRET',
    );
    if (!clientSecret) {
      throw new Error('KEYCLOAK_CLIENT_SECRET não está definido no ambiente');
    }
    this.clientSecret = clientSecret;
  }

  /**
   * Método para obter token do Keycloak via password grant
   * @param username Nome de usuário ou email
   * @param password Senha do usuário
   * @returns Token JWT e informações relacionadas
   */
  async token(
    username: string,
    password: string,
  ): Promise<{
    access_token: string;
    refresh_token?: string;
    expires_in: number;
  }> {
    try {
      // Preparar os dados para a requisição de token
      const params = new URLSearchParams();
      params.append('grant_type', 'password');
      params.append('client_id', this.clientId);
      params.append('username', username);
      params.append('password', password);

      // Adicionar client_secret se estiver disponível (necessário para admin-cli)
      if (this.clientSecret) {
        params.append('client_secret', this.clientSecret);
      }

      // Usar diretamente this.baseUrl e this.realm em vez de obter novamente do configService
      const tokenUrl = `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`;

      // Fazer a requisição para obter o token com retries
      const response =
        await this.requestUtilsService.executeWithRetry<TokenResponse>(
          () => {
            return axios.post(tokenUrl, params.toString(), {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            });
          },
          (_error) => {
            throw new UnauthorizedException('Usuário ou senha inválidos');
          },
        );

      // Retornar apenas as propriedades necessárias
      return {
        access_token: response.access_token,
        refresh_token: response.refresh_token,
        expires_in: response.expires_in,
      };
    } catch (error) {
      console.error('Erro ao obter token do Keycloak:', error);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao se comunicar com o servidor de autenticação',
      );
    }
  }

  /**
   * Obtém um token de acesso usando o fluxo client_credentials
   */
  async clientCredentialsToken(): Promise<{
    access_token: string;
    expires_in: number;
    token_type: string;
  }> {
    const params = new URLSearchParams();
    params.append('grant_type', 'client_credentials');
    params.append('client_id', this.clientId);

    if (this.clientSecret) {
      params.append('client_secret', this.clientSecret);
    } else {
      throw new Error(
        'Client secret é necessário para fluxo client_credentials',
      );
    }

    const response =
      await this.requestUtilsService.executeWithRetry<TokenResponse>(
        () =>
          axios.post(
            `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`,
            params.toString(),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Accept: 'application/json',
              },
            },
          ),
        (_error) => {
          throw new UnauthorizedException(
            'Falha na autenticação cliente-servidor',
          );
        },
      );

    return {
      access_token: response.access_token,
      expires_in: response.expires_in,
      token_type: response.token_type,
    };
  }

  async validateToken(token: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/userinfo`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (!response.ok) {
        this.logger.error(`Token validation failed: ${response.statusText}`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error validating token: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async getUserInfo(token: string): Promise<UserInfo> {
    try {
      const response = await fetch(
        `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/userinfo`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to get user info: ${response.statusText}`);
      }

      return (await response.json()) as UserInfo;
    } catch (error) {
      this.logger.error(
        `Error getting user info: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async getRoles(token: string): Promise<string[]> {
    try {
      const userInfo = await this.getUserInfo(token);
      return userInfo.roles || [];
    } catch (error) {
      this.logger.error(
        `Error getting roles: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async hasRole(token: string, role: string): Promise<boolean> {
    try {
      const roles = await this.getRoles(token);
      return roles.includes(role);
    } catch (error) {
      this.logger.error(
        `Error checking role: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async createUser(userData: Record<string, unknown>): Promise<string> {
    try {
      const adminToken = await this.keycloakAdminUtils.getAdminToken();
      return this.keycloakAdminUtils.createUser(adminToken, userData);
    } catch (error) {
      this.logger.error(
        `Error creating user: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async updateUser(
    userId: string,
    userData: Record<string, unknown>,
  ): Promise<void> {
    try {
      const adminToken = await this.keycloakAdminUtils.getAdminToken();
      await this.keycloakAdminUtils.updateUser(adminToken, userId, userData);
    } catch (error) {
      this.logger.error(
        `Error updating user: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<void> {
    try {
      const adminToken = await this.keycloakAdminUtils.getAdminToken();
      await this.keycloakAdminUtils.deleteUser(adminToken, userId);
    } catch (error) {
      this.logger.error(
        `Error deleting user: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async assignRole(userId: string, role: string): Promise<void> {
    try {
      const adminToken = await this.keycloakAdminUtils.getAdminToken();
      await this.keycloakAdminUtils.assignRole(adminToken, userId, role);
    } catch (error) {
      this.logger.error(
        `Error assigning role: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async removeRole(userId: string, role: string): Promise<void> {
    try {
      const adminToken = await this.keycloakAdminUtils.getAdminToken();
      await this.keycloakAdminUtils.removeRole(adminToken, userId, role);
    } catch (error) {
      this.logger.error(
        `Error removing role: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<{ access_token: string }> {
    try {
      const params = new URLSearchParams();
      params.append('grant_type', 'refresh_token');
      params.append('client_id', this.clientId);
      params.append('refresh_token', refreshToken);

      if (this.clientSecret) {
        params.append('client_secret', this.clientSecret);
      }

      const response =
        await this.requestUtilsService.executeWithRetry<TokenResponse>(
          () =>
            axios.post(
              `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`,
              params.toString(),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              },
            ),
          () => {
            throw new UnauthorizedException(
              'Token de atualização inválido ou expirado',
            );
          },
        );

      return { access_token: response.access_token };
    } catch {
      throw new UnauthorizedException(
        'Token de atualização inválido ou expirado',
      );
    }
  }

  async logout(refreshToken: string): Promise<void> {
    try {
      const params = new URLSearchParams();
      params.append('client_id', this.clientId);
      params.append('refresh_token', refreshToken);

      if (this.clientSecret) {
        params.append('client_secret', this.clientSecret);
      }

      await this.requestUtilsService.executeWithRetry(
        () =>
          axios.post(
            `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/logout`,
            params.toString(),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            },
          ),
        () => {
          throw new UnauthorizedException('Não foi possível realizar o logout');
        },
      );
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Não foi possível realizar o logout');
    }
  }

  /**
   * Resets a user's password using Keycloak's API
   * @param userId The Keycloak user ID
   * @param newPassword The new password to set
   */
  async resetPassword(userId: string, newPassword: string): Promise<void> {
    try {
      const adminToken = await this.keycloakAdminUtils.getAdminAuthHeaders();
      const resetPasswordUrl = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}/reset-password`;

      await this.requestUtilsService.executeWithRetry(
        () => {
          return axios.put(
            resetPasswordUrl,
            {
              type: 'password',
              value: newPassword,
              temporary: false,
            },
            {
              headers: {
                ...adminToken,
                'Content-Type': 'application/json',
              },
            },
          );
        },
        () => {
          throw new InternalServerErrorException(
            'Erro ao redefinir senha no servidor de autenticação',
          );
        },
      );
    } catch (error) {
      this.logger.error(
        `Error resetting password in Keycloak: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        'Erro ao redefinir senha no servidor de autenticação',
      );
    }
  }
}
