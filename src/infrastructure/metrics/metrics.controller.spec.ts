import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { MetricsModule } from './metrics.module';
import { MetricsService } from './metrics.service';
import { Server } from 'http';

describe('MetricsController (Integration)', () => {
  let app: INestApplication;
  let metricsService: MetricsService;

  beforeEach(async () => {
    // Clear metrics before each test
    MetricsService.clearMetrics();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [MetricsModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api/v1');
    metricsService = moduleFixture.get<MetricsService>(MetricsService);
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it('/api/v1/metrics (GET) should return prometheus metrics', async () => {
    // Simula algumas requisições para gerar métricas
    metricsService.recordRequest('GET', '/api/users', 200, 0.1);
    metricsService.recordRequest('POST', '/api/users', 201, 0.2);
    metricsService.recordRequest('GET', '/api/users/1', 404, 0.3);

    const response = await request(app.getHttpServer() as Server)
      .get('/api/v1/metrics')
      .expect(200);

    // Verifica se as métricas contêm os dados esperados
    expect(response.text).toContain('http_requests_total');
    expect(response.text).toContain('http_request_duration_seconds');
    expect(response.text).toContain('http_errors_total');

    // Verifica se as métricas específicas foram registradas
    expect(response.text).toContain(
      'method="GET",path="/api/users",status_code="200"',
    );
    expect(response.text).toContain(
      'method="POST",path="/api/users",status_code="201"',
    );
    expect(response.text).toContain(
      'method="GET",path="/api/users/1",status_code="404"',
    );
  });

  it('should handle concurrent requests correctly', async () => {
    // Simula múltiplas requisições concorrentes
    const requests = [
      metricsService.recordRequest('GET', '/api/concurrent', 200, 0.1),
      metricsService.recordRequest('GET', '/api/concurrent', 200, 0.2),
      metricsService.recordRequest('GET', '/api/concurrent', 200, 0.3),
    ];

    await Promise.all(requests);

    const response = await request(app.getHttpServer() as Server)
      .get('/api/v1/metrics')
      .expect(200);

    // Verifica se todas as requisições concorrentes foram registradas
    const matches = response.text.match(
      /method="GET",path="\/api\/concurrent",status_code="200"/g,
    );
    expect(matches).toBeDefined();
    expect(matches?.length).toBeGreaterThanOrEqual(3);
  });

  it('should record error metrics correctly', async () => {
    // Simula requisições com erro
    metricsService.recordRequest('GET', '/api/error', 500, 0.1);
    metricsService.recordRequest('POST', '/api/error', 400, 0.2);

    const response = await request(app.getHttpServer() as Server)
      .get('/api/v1/metrics')
      .expect(200);

    // Verifica se os erros foram registrados corretamente
    expect(response.text).toContain(
      'method="GET",path="/api/error",status_code="500"',
    );
    expect(response.text).toContain(
      'method="POST",path="/api/error",status_code="400"',
    );

    // Verifica se o contador de erros foi incrementado
    expect(response.text).toMatch(/http_errors_total{.*} [1-9]/);
  });
});
