import { Test, TestingModule } from '@nestjs/testing';
import { MetricsService } from './metrics.service';
import { Counter, Histogram } from 'prom-client';

describe('MetricsService', () => {
  let service: MetricsService;
  let httpRequestsTotal: Counter<string>;
  let httpRequestDuration: Histogram<string>;
  let httpErrorsTotal: Counter<string>;

  beforeEach(async () => {
    // Clear metrics before each test
    MetricsService.clearMetrics();

    httpRequestsTotal = {
      inc: jest.fn(),
    } as unknown as Counter<string>;
    httpRequestDuration = {
      observe: jest.fn(),
    } as unknown as Histogram<string>;
    httpErrorsTotal = {
      inc: jest.fn(),
    } as unknown as Counter<string>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MetricsService,
        {
          provide: 'PROM_METRIC_HTTP_REQUESTS_TOTAL',
          useValue: httpRequestsTotal,
        },
        {
          provide: 'PROM_METRIC_HTTP_REQUEST_DURATION_SECONDS',
          useValue: httpRequestDuration,
        },
        {
          provide: 'PROM_METRIC_HTTP_ERRORS_TOTAL',
          useValue: httpErrorsTotal,
        },
      ],
    }).compile();

    service = module.get<MetricsService>(MetricsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should record request metrics', async () => {
    const method = 'GET';
    const path = '/test';
    const status = 200;
    const duration = 0.1;

    service.recordRequest(method, path, status, duration);

    const metrics = await service.getMetrics();

    // Verify metrics contain expected values
    expect(metrics).toContain('http_requests_total');
    expect(metrics).toContain(`method="${method}"`);
    expect(metrics).toContain(`path="${path}"`);
    expect(metrics).toContain(`status_code="${status}"`);
    expect(metrics).toContain('http_request_duration_seconds');

    // Verify that no error metrics were recorded
    expect(metrics).not.toMatch(/http_errors_total{.*} [1-9]/);
  });

  it('should record error metrics for status >= 400', async () => {
    const method = 'GET';
    const path = '/test';
    const status = 404;
    const duration = 0.1;

    service.recordRequest(method, path, status, duration);

    const metrics = await service.getMetrics();

    // Verify metrics contain expected values
    expect(metrics).toContain('http_requests_total');
    expect(metrics).toContain('http_request_duration_seconds');
    expect(metrics).toMatch(/http_errors_total{.*} 1/);
    expect(metrics).toContain(`method="${method}"`);
    expect(metrics).toContain(`path="${path}"`);
    expect(metrics).toContain(`status_code="${status}"`);
  });
});
