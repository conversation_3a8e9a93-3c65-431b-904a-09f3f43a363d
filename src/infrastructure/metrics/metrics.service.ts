import { Injectable } from '@nestjs/common';
import { Counter, Histogram, register } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly httpRequestsTotal: Counter<string>;
  private readonly httpRequestDurationSeconds: Histogram<string>;
  private readonly httpErrorsTotal: Counter<string>;

  constructor() {
    // Clear any existing metrics (useful for testing)
    register.clear();

    this.httpRequestsTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'path', 'status_code'],
      registers: [register],
    });

    this.httpRequestDurationSeconds = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'path', 'status_code'],
      buckets: [0.1, 0.5, 1, 2, 5],
      registers: [register],
    });

    this.httpErrorsTotal = new Counter({
      name: 'http_errors_total',
      help: 'Total number of HTTP errors',
      labelNames: ['method', 'path', 'status_code'],
      registers: [register],
    });
  }

  recordRequest(
    method: string,
    path: string,
    statusCode: number,
    duration: number,
  ): void {
    this.httpRequestsTotal.inc({
      method,
      path,
      status_code: statusCode.toString(),
    });
    this.httpRequestDurationSeconds.observe(
      { method, path, status_code: statusCode.toString() },
      duration,
    );

    if (statusCode >= 400) {
      this.httpErrorsTotal.inc({
        method,
        path,
        status_code: statusCode.toString(),
      });
    }
  }

  async getMetrics(): Promise<string> {
    return register.metrics();
  }

  // Method to clear metrics (useful for testing)
  static clearMetrics(): void {
    register.clear();
  }
}
