import { DynamicModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import * as Joi from 'joi';

export function createConfig(): Promise<DynamicModule> {
  const envFile = process.env.NODE_ENV === 'test' ? '.env.test' : '.env';

  return ConfigModule.forRoot({
    isGlobal: true,
    envFilePath: envFile,
    validationSchema: Joi.object({
      NODE_ENV: Joi.string()
        .valid('development', 'production', 'test')
        .default('development'),
      APP_PORT: Joi.number().default(3000),
      DATABASE_URL: Joi.string().required(),
      JWT_SECRET: Joi.string().required(),
      JWT_EXPIRATION: Joi.string().required(),
    }),
  });
}
