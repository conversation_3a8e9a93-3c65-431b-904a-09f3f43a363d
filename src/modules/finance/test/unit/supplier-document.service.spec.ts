import { Test, TestingModule } from '@nestjs/testing';
import { SupplierDocumentService } from '../../services/supplier-document.service';
import { CreateDocumentUseCase } from '../../../documents/application/use-cases/create-document.use-case';
import { ListDocumentsUseCase } from '../../../documents/application/use-cases/list-documents.use-case';
import { DownloadDocumentUseCase } from '../../../documents/application/use-cases/download-document.use-case';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';
import { EntityType } from '../../../documents/domain/enums/entity-type.enum';
import { NotFoundException } from '@nestjs/common';

describe('SupplierDocumentService', () => {
  let service: SupplierDocumentService;
  let createDocumentUseCase: CreateDocumentUseCase;
  let listDocumentsUseCase: ListDocumentsUseCase;
  let downloadDocumentUseCase: DownloadDocumentUseCase;
  let prisma: PrismaService;
  let validateSupplierActivationUseCase: ValidateSupplierActivationUseCase;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierDocumentService,
        {
          provide: CreateDocumentUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListDocumentsUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DownloadDocumentUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: PrismaService,
          useValue: {
            supplier: {
              findUnique: jest.fn(),
            },
          },
        },
        {
          provide: ValidateSupplierActivationUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<SupplierDocumentService>(SupplierDocumentService);
    createDocumentUseCase = module.get<CreateDocumentUseCase>(CreateDocumentUseCase);
    listDocumentsUseCase = module.get<ListDocumentsUseCase>(ListDocumentsUseCase);
    downloadDocumentUseCase = module.get<DownloadDocumentUseCase>(DownloadDocumentUseCase);
    prisma = module.get<PrismaService>(PrismaService);
    validateSupplierActivationUseCase = module.get<ValidateSupplierActivationUseCase>(ValidateSupplierActivationUseCase);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadSupplierDocuments', () => {
    it('should upload documents and activate supplier successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const documentsMetadata = JSON.stringify([
        {
          responsible: 'John Doe',
          department: 'Legal',
          description: 'Contract',
          expirationDate: '2025-12-31',
        },
      ]);
      const mockDocument = { uuid: 'document-uuid' } as any;

      jest.spyOn(createDocumentUseCase, 'execute').mockResolvedValue(mockDocument);
      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockResolvedValue(true);

      const result = await service.uploadSupplierDocuments(
        supplierUuid,
        files,
        documentsMetadata,
        userId,
      );

      expect(result).toEqual([mockDocument]);
      expect(createDocumentUseCase.execute).toHaveBeenCalledTimes(1);
      expect(validateSupplierActivationUseCase.execute).toHaveBeenCalledWith(supplierUuid, userId);
    });

    it('should throw an error for invalid JSON metadata', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [] as Express.Multer.File[];
      const invalidJson = 'not a json';

      await expect(
        service.uploadSupplierDocuments(supplierUuid, files, invalidJson, userId),
      ).rejects.toThrow('O campo documentsMetadata não é uma string JSON válida.');
    });

    it('should throw an error if files and metadata count mismatch', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const documentsMetadata = JSON.stringify([]); // Mismatch

      await expect(
        service.uploadSupplierDocuments(
          supplierUuid,
          files,
          documentsMetadata,
          userId,
        ),
      ).rejects.toThrow(
        'O número de metadados de documento não corresponde ao número de arquivos enviados.',
      );
    });

    it('should still upload documents if supplier activation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const documentsMetadata = JSON.stringify([
        {
          responsible: 'John Doe',
          department: 'Legal',
          description: 'Contract',
          expirationDate: '2025-12-31',
        },
      ]);
      const mockDocument = { uuid: 'document-uuid' } as any;

      jest.spyOn(createDocumentUseCase, 'execute').mockResolvedValue(mockDocument);
      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockRejectedValue(new Error('Activation failed'));

      const result = await service.uploadSupplierDocuments(
        supplierUuid,
        files,
        documentsMetadata,
        userId,
      );

      expect(result).toEqual([mockDocument]);
    });
  });

  describe('listSupplierDocuments', () => {
    it('should return a list of documents with download URLs', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockDocuments = [
        {
          uuid: 'document-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0
      });
      jest.spyOn(downloadDocumentUseCase, 'execute').mockResolvedValue({
        url: 'http://example.com/download',
        fileName: 'document.pdf',
      });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result[0]).toHaveProperty('downloadUrl');
      expect(result[0].downloadUrl).toBe('http://example.com/download');
    });

    it('should return documents without download URLs if they have no versions', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockDocuments = [{ uuid: 'document-uuid', versions: [] }] as any[];

      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0,
      });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return document without download URL if URL generation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockDocuments = [
        {
          uuid: 'document-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0,
      });
      jest.spyOn(downloadDocumentUseCase, 'execute').mockImplementation(() => {
        throw new Error('URL generation failed');
      });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return an empty list if no documents are found', async () => {
      const supplierUuid = 'supplier-uuid';

      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: [],
        total: 0,
        limit: 10,
        offset: 0,
      });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result).toEqual([]);
    });
  });

  describe('getSupplierDocument', () => {
    it('should return a document if found', async () => {
      const supplierUuid = 'supplier-uuid';
      const documentUuid = 'document-uuid';
      const mockDocuments = [
        {
          uuid: documentUuid,
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0,
      });
      jest.spyOn(downloadDocumentUseCase, 'execute').mockResolvedValue({
        url: 'http://example.com/download',
        fileName: 'document.pdf',
      });

      const result = await service.getSupplierDocument(supplierUuid, documentUuid);

      expect(result).toHaveProperty('downloadUrl');
      expect(result?.downloadUrl).toBe('http://example.com/download');
    });

    it('should throw NotFoundException if supplier does not exist', async () => {
      const supplierUuid = 'non-existent-uuid';
      const documentUuid = 'document-uuid';

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue(null);

      await expect(
        service.getSupplierDocument(supplierUuid, documentUuid),
      ).rejects.toThrow(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    });

    it('should return null if document is not found', async () => {
      const supplierUuid = 'supplier-uuid';
      const documentUuid = 'non-existent-document';

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: [],
        total: 0,
        limit: 10,
        offset: 0,
      });

      const result = await service.getSupplierDocument(supplierUuid, documentUuid);

      expect(result).toBeNull();
    });

    it('should return document without download URL if URL generation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const documentUuid = 'document-uuid';
      const mockDocuments = [
        {
          uuid: documentUuid,
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(listDocumentsUseCase, 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0,
      });
      jest.spyOn(downloadDocumentUseCase, 'execute').mockImplementation(() => {
        throw new Error('URL generation failed');
      });

      const result = await service.getSupplierDocument(supplierUuid, documentUuid);

      expect(result).not.toHaveProperty('downloadUrl');
    });
  });
}); 