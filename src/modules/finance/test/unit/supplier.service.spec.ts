import { Test, TestingModule } from '@nestjs/testing';
import { SupplierService } from '../../services/supplier.service';
import { CreateSupplierUseCase } from '@/core/application/use-cases/supplier/create-supplier.use-case';
import { ListSupplierByUuidUseCase } from '@/core/application/use-cases/supplier/list-supplier-by-uuid.use-case';
import { ListSuppliersUseCase } from '@/core/application/use-cases/supplier/list-suppliers.use-case';
import { DeleteSupplierUseCase } from '@/core/application/use-cases/supplier/delete-supplier.use-case';
import { UpdateSupplierUseCase } from '@/core/application/use-cases/supplier/update-supplier.use-case';
import { CreateSupplierDto } from '../../dto/create-supplier.dto';
import { UpdateSupplierDto } from '../../dto/update-supplier.dto';
import { Supplier } from '@/core/domain/supplier/entities/supplier.entity';
import { SupplierStatus } from '@/core/domain/supplier/enums/supplier-status.enum';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';
import { GetSupplierByUserIdUsecase } from '@/core/application/use-cases/supplier/get-supplier-by-user-id.use-case';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { SupplierContractService } from '../../services/supplier-contract.service';
import { SupplierDocumentService } from '../../services/supplier-document.service';
import { SupplierContactService } from '../../services/supplier-contact.service';

describe('SupplierService', () => {
  let service: SupplierService;
  let createSupplierUseCase: CreateSupplierUseCase;
  let listSupplierByUuidUseCase: ListSupplierByUuidUseCase;
  let listSuppliersUseCase: ListSuppliersUseCase;
  let deleteSupplierUseCase: DeleteSupplierUseCase;
  let updateSupplierUseCase: UpdateSupplierUseCase;
  let prisma: PrismaService;

  // Mock supplier response data
  const mockSupplierData = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    cnpj: '**************',
    tradeName: 'Test Trade Name',
    address: {
      street: 'Test Street',
      city: 'Test City',
      zipCode: '********',
      state: 'TS',
      number: undefined,
      complement: undefined,
      neighborhood: undefined,
    },
    status: SupplierStatus.ACTIVE,
    type: SupplierType.BANK,
    email: '<EMAIL>',
    createdAt: '2023-01-01T00:00:00.000Z',
    createdBy: 'user-uuid',
    updatedAt: '2023-01-01T00:00:00.000Z',
    updatedBy: 'user-uuid',
  };

  // Mock the Supplier entity
  const mockSupplier = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    document: '**************',
    tradeName: 'Test Trade Name',
    address: {
      toJSON: () => ({
        street: 'Test Street',
        city: 'Test City',
        zipCode: '********',
        state: 'TS',
        number: undefined,
        complement: undefined,
        neighborhood: undefined,
      }),
    },
    status: SupplierStatus.ACTIVE,
    type: SupplierType.BANK,
    email: '<EMAIL>',
    createdAt: new Date('2023-01-01'),
    createdBy: 'user-uuid',
    updatedAt: new Date('2023-01-01'),
    updatedBy: 'user-uuid',
    toJSON: () => mockSupplierData,
  } as unknown as Supplier;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierService,
        {
          provide: CreateSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListSupplierByUuidUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListSuppliersUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: DeleteSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: UpdateSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: GetSupplierByUserIdUsecase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: PrismaService,
          useValue: {
            supplier: {
              findUnique: jest.fn(),
            },
          },
        },
        {
          provide: SupplierContractService,
          useValue: {},
        },
        {
          provide: SupplierDocumentService,
          useValue: {},
        },
        {
          provide: SupplierContactService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<SupplierService>(SupplierService);
    createSupplierUseCase = module.get<CreateSupplierUseCase>(
      CreateSupplierUseCase,
    );
    listSupplierByUuidUseCase = module.get<ListSupplierByUuidUseCase>(
      ListSupplierByUuidUseCase,
    );
    listSuppliersUseCase =
      module.get<ListSuppliersUseCase>(ListSuppliersUseCase);
    deleteSupplierUseCase = module.get<DeleteSupplierUseCase>(
      DeleteSupplierUseCase,
    );
    updateSupplierUseCase = module.get<UpdateSupplierUseCase>(
      UpdateSupplierUseCase,
    );
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSupplier', () => {
    it('should create a supplier and return it as DTO', async () => {
      const createDto: CreateSupplierDto = {
        name: 'Test Supplier',
        document: '**************',
        tradeName: 'Test Trade Name',
        address: {
          street: 'Test Street',
          city: 'Test City',
          zipCode: '********',
          state: 'TS',
        },
        email: '<EMAIL>',
        type: SupplierType.BANK,
      };
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(createSupplierUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      const result = await service.createSupplier(createDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(createDto, userId);
      expect(result).toEqual(mockSupplierData);
    });

    it('should create a supplier with GENERAL classification for OTHER type', async () => {
      const createDto: CreateSupplierDto = {
        name: 'Test Supplier',
        document: '**************',
        tradeName: 'Test Trade Name',
        address: {
          street: 'Test Street',
          city: 'Test City',
          zipCode: '********',
          state: 'TS',
        },
        email: '<EMAIL>',
        type: SupplierType.OTHER,
      };
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(createSupplierUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      await service.createSupplier(createDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(createDto, userId);
    });
  });

  describe('findByUserId', () => {
    it('should return a supplier by user id', async () => {
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(
          service['getSupplierByUserIdUseCase'],
          'execute',
        )
        .mockResolvedValue(mockSupplier);

      const result = await service.findByUserId(userId);

      expect(executeSpy).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockSupplierData);
    });

    it('should throw a not found exception if supplier is not found by user id', async () => {
      const userId = 'user-uuid';

      jest
        .spyOn(
          service['getSupplierByUserIdUseCase'],
          'execute',
        )
        .mockImplementation(() => {
          throw new Error('Supplier not found');
        });

      await expect(service.findByUserId(userId)).rejects.toThrow(
        'Supplier not found',
      );
    });
  });

  describe('getSupplierTypes', () => {
    it('should return a list of supplier types', () => {
      const result = service.getSupplierTypes();
      expect(result).toEqual(Object.values(SupplierType));
    });
  });

  describe('listSupplierByUuid', () => {
    it('should return a supplier by uuid', async () => {
      const uuid = 'supplier-uuid';

      const executeSpy = jest
        .spyOn(listSupplierByUuidUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      const result = await service.listSupplierByUuid(uuid);

      expect(executeSpy).toHaveBeenCalledWith(uuid);
      expect(result).toEqual(mockSupplierData);
    });
  });

  describe('listSuppliers', () => {
    it('should return a paginated list of suppliers', async () => {
      const params = { limit: 10, offset: 0, name: 'Test' };
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const executeSpy = jest
        .spyOn(listSuppliersUseCase, 'execute')
        .mockResolvedValue(mockResult);

      const result = await service.listSuppliers(params);

      expect(executeSpy).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        name: 'Test',
      });
      expect(result).toEqual({
        items: [mockSupplierData],
        total: 1,
        limit: 10,
        offset: 0,
      });
    });
  });

  describe('deleteSupplier', () => {
    it('should delete a supplier', async () => {
      const uuid = 'supplier-uuid';

      const executeSpy = jest
        .spyOn(deleteSupplierUseCase, 'execute')
        .mockResolvedValue(undefined);

      await service.deleteSupplier(uuid);

      expect(executeSpy).toHaveBeenCalledWith({ uuid });
    });
  });

  describe('updateSupplier', () => {
    it('should update a supplier and return it as DTO', async () => {
      const uuid = 'supplier-uuid';
      const updateDto: UpdateSupplierDto = {
        name: 'Updated Supplier',
        tradeName: 'Updated Trade Name',
        address: {
          street: 'Updated Street',
          city: 'Updated City',
          zipCode: '********',
          state: 'US',
        },
        email: '<EMAIL>',
        type: SupplierType.BANK,
      };
      const userId = 'user-uuid';

      // Updated supplier data
      const updatedSupplierData = {
        id: 'supplier-uuid',
        name: 'Updated Supplier',
        cnpj: '**************',
        tradeName: 'Updated Trade Name',
        address: {
          street: 'Updated Street',
          city: 'Updated City',
          zipCode: '********',
          state: 'US',
          number: undefined,
          complement: undefined,
          neighborhood: undefined,
        },
        status: SupplierStatus.ACTIVE,
        type: SupplierType.BANK,
        email: '<EMAIL>',
        createdAt: '2023-01-01T00:00:00.000Z',
        createdBy: 'user-uuid',
        updatedAt: '2023-01-02T00:00:00.000Z',
        updatedBy: userId,
      };

      // Create a separate mock for the updated supplier
      const updatedMockSupplier = {
        id: 'supplier-uuid',
        name: 'Updated Supplier',
        document: '**************',
        tradeName: 'Updated Trade Name',
        address: {
          toJSON: () => ({
            street: 'Updated Street',
            city: 'Updated City',
            zipCode: '********',
            state: 'US',
            number: undefined,
            complement: undefined,
            neighborhood: undefined,
          }),
        },
        status: SupplierStatus.ACTIVE,
        type: SupplierType.BANK,
        email: '<EMAIL>',
        createdAt: new Date('2023-01-01'),
        createdBy: 'user-uuid',
        updatedAt: new Date('2023-01-02'),
        updatedBy: userId,
        toJSON: () => updatedSupplierData,
      } as unknown as Supplier;

      const executeSpy = jest
        .spyOn(updateSupplierUseCase, 'execute')
        .mockResolvedValue(updatedMockSupplier);

      const result = await service.updateSupplier(uuid, updateDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(uuid, updateDto, userId);
      expect(result).toEqual(updatedSupplierData);
    });
  });
});
