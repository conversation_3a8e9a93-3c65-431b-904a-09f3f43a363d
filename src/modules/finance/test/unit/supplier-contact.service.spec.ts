import { Test, TestingModule } from '@nestjs/testing';
import { SupplierContactService } from '../../services/supplier-contact.service';
import { ListSupplierContactsUseCase } from '@/core/application/use-cases/supplier/list-supplier-contacts.use-case';
import { CreateSupplierContactUseCase } from '@/core/application/use-cases/supplier/create-supplier-contact.use-case';
import { UpdateSupplierContactUseCase } from '@/core/application/use-cases/supplier/update-supplier-contact.usecase';
import { DeleteSupplierContactUseCase } from '@/core/application/use-cases/supplier/delete-supplier-contact.usecase';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';
import { ContactResponseDto } from '../../dto/supplier-response.dto';

describe('SupplierContactService', () => {
  let service: SupplierContactService;
  let listSupplierContactsUseCase: ListSupplierContactsUseCase;
  let createSupplierContactUseCase: CreateSupplierContactUseCase;
  let updateSupplierContactUseCase: UpdateSupplierContactUseCase;
  let deleteSupplierContactUseCase: DeleteSupplierContactUseCase;
  let validateSupplierActivationUseCase: ValidateSupplierActivationUseCase;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierContactService,
        {
          provide: ListSupplierContactsUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: CreateSupplierContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UpdateSupplierContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteSupplierContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ValidateSupplierActivationUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<SupplierContactService>(SupplierContactService);
    listSupplierContactsUseCase = module.get<ListSupplierContactsUseCase>(ListSupplierContactsUseCase);
    createSupplierContactUseCase = module.get<CreateSupplierContactUseCase>(CreateSupplierContactUseCase);
    updateSupplierContactUseCase = module.get<UpdateSupplierContactUseCase>(UpdateSupplierContactUseCase);
    deleteSupplierContactUseCase = module.get<DeleteSupplierContactUseCase>(DeleteSupplierContactUseCase);
    validateSupplierActivationUseCase = module.get<ValidateSupplierActivationUseCase>(ValidateSupplierActivationUseCase);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('listSupplierContacts', () => {
    it('should return a list of contacts', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContacts = [
        {
          id: 'contact-uuid',
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
      ] as any[];

      jest.spyOn(listSupplierContactsUseCase, 'execute').mockResolvedValue(mockContacts);

      const result = await service.listSupplierContacts(supplierUuid);

      expect(result.contacts).toHaveLength(1);
      expect(result.contacts[0].contact).toBe('<EMAIL>');
      expect(listSupplierContactsUseCase.execute).toHaveBeenCalledWith(supplierUuid);
    });

    it('should return an empty list if no contacts are found', async () => {
      const supplierUuid = 'supplier-uuid';

      jest.spyOn(listSupplierContactsUseCase, 'execute').mockResolvedValue([]);

      const result = await service.listSupplierContacts(supplierUuid);

      expect(result.contacts).toEqual([]);
    });
  });

  describe('createSupplierContacts', () => {
    it('should create contacts and activate supplier successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const contactsData = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Support',
          responsible: 'Jane Doe',
        },
      ];
      const mockCreatedContact = {
        id: 'contact-uuid',
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
      };

      jest.spyOn(createSupplierContactUseCase, 'execute').mockResolvedValue(mockCreatedContact as any);
      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockResolvedValue(true);

      const result = await service.createSupplierContacts(supplierUuid, contactsData);

      expect(createSupplierContactUseCase.execute).toHaveBeenCalledTimes(2);
      expect(validateSupplierActivationUseCase.execute).toHaveBeenCalledWith(supplierUuid, 'system');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        contact: mockCreatedContact.contact,
        type: mockCreatedContact.type,
        area: mockCreatedContact.area,
        responsible: mockCreatedContact.responsible,
      });
    });

    it('should still create contacts if supplier activation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const contactsData = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
      ];
      const mockCreatedContact = {
        id: 'contact-uuid',
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
      };

      jest.spyOn(createSupplierContactUseCase, 'execute').mockResolvedValue(mockCreatedContact as any);
      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockRejectedValue(new Error('Activation failed'));

      const result = await service.createSupplierContacts(supplierUuid, contactsData);

      expect(createSupplierContactUseCase.execute).toHaveBeenCalledTimes(1);
      expect(result).toHaveLength(1);
    });

    it('should handle empty contacts array and still attempt activation', async () => {
      const supplierUuid = 'supplier-uuid';
      const contactsData = [];

      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockResolvedValue(true);

      const result = await service.createSupplierContacts(supplierUuid, contactsData);

      expect(createSupplierContactUseCase.execute).not.toHaveBeenCalled();
      expect(validateSupplierActivationUseCase.execute).toHaveBeenCalledWith(supplierUuid, 'system');
      expect(result).toHaveLength(0);
    });
  });

  describe('updateSupplierContacts', () => {
    it('should update a contact successfully', async () => {
      const contactUUID = 'contact-uuid';
      const updateData = {
        contact: '<EMAIL>',
        type: 'email',
        area: 'Support',
        responsible: 'Jane Doe',
      };
      const mockUpdatedContact = {
        id: contactUUID,
        ...updateData,
      };

      jest.spyOn(updateSupplierContactUseCase, 'execute').mockResolvedValue(mockUpdatedContact as any);

      const result = await service.updateSupplierContacts(contactUUID, updateData);

      expect(updateSupplierContactUseCase.execute).toHaveBeenCalledWith(contactUUID, updateData);
      expect(result).toEqual(mockUpdatedContact);
    });

    it('should update a contact with partial data', async () => {
      const contactUUID = 'contact-uuid';
      const updateData = {
        contact: '<EMAIL>',
      };
      const mockUpdatedContact = {
        id: contactUUID,
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
      };

      jest.spyOn(updateSupplierContactUseCase, 'execute').mockResolvedValue(mockUpdatedContact as any);

      const result = await service.updateSupplierContacts(contactUUID, updateData);

      expect(updateSupplierContactUseCase.execute).toHaveBeenCalledWith(contactUUID, updateData);
      expect(result).toEqual(mockUpdatedContact);
    });
  });

  describe('deleteSupplierContacts', () => {
    it('should delete a contact successfully', async () => {
      const contactUUID = 'contact-uuid';

      jest.spyOn(deleteSupplierContactUseCase, 'execute').mockResolvedValue(undefined);

      const result = await service.deleteSupplierContacts(contactUUID);

      expect(deleteSupplierContactUseCase.execute).toHaveBeenCalledWith(contactUUID);
      expect(result).toBeUndefined();
    });
  });
}); 