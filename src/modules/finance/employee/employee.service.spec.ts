import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeService } from './employee.service';
import { CreateEmployeeUseCase } from '@/core/application/use-cases/employee/create-employee.use-case';
import {
  Employee,
  Address,
  PersonalDocument,
  Dependent,
  Vacation,
  WorkSchedule,
} from '@/core/domain/entities/employee.entity';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { EmployeeResponseDto } from './dto/employee-response.dto';
import { UpdateEmployeeUseCase } from '@/core/application/use-cases/employee/update-employee.use-case';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { ListEmployeeByUuidUseCase } from '@/core/application/use-cases/employee/list-employee-by-uuid.use-case';
import { DeleteEmployeeUseCase } from '@/core/application/use-cases/employee/delete-employee.use-case';
import { NotFoundException } from '@nestjs/common';
import { ListEmployeeUseCase } from '@/core/application/use-cases/employee/list-employee.use-case';
import { EmployeeStatus as PrismaEmployeeStatus } from '@prisma/client';
import {
  ContractType,
  Seniority,
  Shift,
} from '@/core/domain/enums/employee.enum';
import { CreateEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/create-employee-archive.use-case';
import { FindEmployeeArchiveByUuidUseCase } from '@/core/application/use-cases/employee/find-employee-archive-by-uuid.use-case';
import { FindOneEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/find-one-employee-archive.use-case';
import { DeleteEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/delete-employee-archive.use-case';
import { UploadEmployeePersonalDocumentUseCase } from '@/core/application/use-cases/employee/upload-employee-personal-document.use-case';
import { ListPersonalDocumentsByEmployeeUuidUseCase } from '@/core/application/use-cases/employee/list-personal-documents-by-employee-uuid.use-case';
import { DeleteEmployeePersonalDocumentUseCase } from '@/core/application/use-cases/employee/delete-employee-personal-document.use-case';

describe('EmployeeService', () => {
  let service: EmployeeService;
  let createEmployeeUseCase: jest.Mocked<CreateEmployeeUseCase>;
  let updateEmployeeUseCase: jest.Mocked<UpdateEmployeeUseCase>;
  let deleteEmployeeUseCase: jest.Mocked<DeleteEmployeeUseCase>;
  let listEmployeeByUuidUseCase: jest.Mocked<ListEmployeeByUuidUseCase>;
  let listEmployeeUseCase: jest.Mocked<ListEmployeeUseCase>;

  const mockAddress: Address = {
    street: 'Rua Teste',
    number: '123',
    neighborhood: 'Bairro Teste',
    city: 'Cidade Teste',
    state: 'Estado Teste',
    zipCode: '12345-678',
    complement: 'Apto 101',
  };

  const mockPersonalDocument: PersonalDocument = {
    type: 'CPF',
    number: '123.456.789-00',
  };

  const mockDependent: Dependent = {
    name: 'Maria Silva',
    kinship: 'Filho(a)',
    birthDate: '2010-01-01',
    isTaxDependent: true,
    hasHealthPlan: true,
  };

  const mockWorkSchedule: WorkSchedule = {
    weekDays: [1, 2, 3, 4, 5],
    startingHour: '08:00',
    endingHour: '17:00',
  };

  const mockVacation: Vacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const mockEmployee = new Employee(
    1,
    'uuid-123',
    'Funcionário Teste',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date('2024-01-01T10:00:00Z'),
    mockAddress,
    [mockPersonalDocument],
    [mockDependent],
    PrismaEmployeeStatus.ACTIVE,
    'admin',
    'admin',
    new Date('2024-01-01T10:00:00Z'),
    new Date('2024-01-01T10:00:00Z'),
    mockWorkSchedule,
    Shift.MORNING,
    5000.0,
    500.0,
    300.0,
    'Unimed',
    ContractType.PJ,
    Seniority.SENIOR,
    '***********',
    new Date('1990-01-01'),
    '08:00-17:00',
    true,
    [mockVacation],
  );

  const mockCreateEmployeeDto: CreateEmployeeDto = {
    name: mockEmployee.name,
    email: mockEmployee.email,
    position: 'Software Engineer',
    department: 'Engineering',
    hireDate: new Date('2024-01-01T10:00:00Z').toISOString(),
    address: mockAddress,
    personalDocuments: [mockPersonalDocument],
    dependents: [mockDependent],
    status: PrismaEmployeeStatus.ACTIVE,
    workSchedule: mockWorkSchedule,
    shift: Shift.MORNING,
    grossSalary: 5000.0,
    mealAllowance: 500.0,
    transportAllowance: 300.0,
    healthPlan: 'Unimed',
    contractType: ContractType.PJ,
    seniority: Seniority.SENIOR,
    phone: '***********',
    birthDate: new Date('1990-01-01').toISOString(),
    workHours: '08:00-17:00',
    overtimeBank: true,
    vacations: [mockVacation],
    createdBy: 'admin',
    updatedBy: 'admin',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeService,
        {
          provide: CreateEmployeeUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListEmployeeUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListEmployeeByUuidUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UpdateEmployeeUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteEmployeeUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: CreateEmployeeArchiveUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: FindEmployeeArchiveByUuidUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: FindOneEmployeeArchiveUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteEmployeeArchiveUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UploadEmployeePersonalDocumentUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListPersonalDocumentsByEmployeeUuidUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteEmployeePersonalDocumentUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<EmployeeService>(EmployeeService);
    createEmployeeUseCase = module.get(CreateEmployeeUseCase);
    listEmployeeUseCase = module.get(ListEmployeeUseCase);
    listEmployeeByUuidUseCase = module.get(ListEmployeeByUuidUseCase);
    updateEmployeeUseCase = module.get(UpdateEmployeeUseCase);
    deleteEmployeeUseCase = module.get(DeleteEmployeeUseCase);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create an employee', async () => {
      createEmployeeUseCase.execute.mockResolvedValue(mockEmployee);
      const result = await service.create(mockCreateEmployeeDto);
      expect(createEmployeeUseCase.execute).toHaveBeenCalledWith(
        mockCreateEmployeeDto,
      );
      expect(result.uuid).toBe(mockEmployee.uuid);
      expect(result.dependents[0].kinship).toBe('Filho(a)');
    });
  });

  describe('findByUuid', () => {
    it('should get employee by uuid', async () => {
      listEmployeeByUuidUseCase.execute.mockResolvedValue(mockEmployee);
      const result = await service.findByUuid('uuid-123');
      expect(listEmployeeByUuidUseCase.execute).toHaveBeenCalledWith(
        'uuid-123',
      );
      expect(result.uuid).toBe(mockEmployee.uuid);
    });

    it('should throw NotFoundException when employee does not exist', async () => {
      listEmployeeByUuidUseCase.execute.mockResolvedValue(
        null as unknown as Employee,
      );
      await expect(service.findByUuid('non-existent-uuid')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update an employee', async () => {
      const uuid = 'uuid-123';
      const userId = 'user-id-for-update';
      const updateDto: UpdateEmployeeDto = {
        name: 'Updated Name',
        address: {
          street: 'Updated Street',
          number: '456',
          neighborhood: 'Updated Neighborhood',
          city: 'Updated City',
          state: 'UP',
          zipCode: '54321-987',
          complement: 'Updated Complement',
        }
      };
      const updatedEmployee = new Employee(
        1,
        'uuid-123',
        'Updated Name',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date('2024-01-01T10:00:00Z'),
        mockAddress,
        [mockPersonalDocument],
        [mockDependent],
        PrismaEmployeeStatus.ACTIVE,
        'admin',
        userId,
        new Date('2024-01-01T10:00:00Z'),
        new Date('2024-01-01T10:00:00Z'),
        mockWorkSchedule,
        Shift.MORNING,
        5000.0,
        500.0,
        300.0,
        'Unimed',
        ContractType.PJ,
        Seniority.SENIOR,
        '***********',
        new Date('1990-01-01'),
        '08:00-17:00',
        true,
        [mockVacation],
        'user-id-123',
      );

      updateEmployeeUseCase.execute.mockResolvedValue(updatedEmployee);

      const result = await service.update(uuid, updateDto, userId);

      expect(updateEmployeeUseCase.execute).toHaveBeenCalledWith(
        uuid,
        updateDto,
        userId,
      );
      expect(result.name).toBe('Updated Name');
      expect(result.updatedBy).toBe(userId);
    });
  });
});
