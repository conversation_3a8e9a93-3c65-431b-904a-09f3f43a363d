import { ApiProperty } from "@nestjs/swagger";

export class EmployeeArchiveResponseDto {
  @ApiProperty({
    description: 'Identificador único (UUID) do arquivo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Identificador único (UUID) do colaborador',
    example: '123e4567-e89b-12d3-a456-************',
  })
  employeeUuid: string;

  @ApiProperty({
    description: 'Nome do arquivo',
    example: 'contrato.pdf',
  })
  fileName: string;

  @ApiProperty({
    description: 'URL de download do arquivo',
    example: 'https://example.com/download/123e4567-e89b-12d3-a456-************',
  })
  downloadUrl: string;

  @ApiProperty({
    description: 'Timestamp da criação em formato ISO',
    example: '2023-01-15T10:30:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Timestamp da última atualização em formato ISO',
    example: '2023-02-20T14:45:30Z',
  })
  updatedAt: string;

  @ApiProperty({
    description: 'Timestamp da exclusão em formato ISO',
    example: '2023-02-20T14:45:30Z',
  })
  deletedAt: string;

  @ApiProperty({
    description: 'UUID do usuário que atualizou o arquivo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  updatedBy: string;

  @ApiProperty({
    description: 'UUID do usuário que deletou o arquivo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  deletedBy: string;

  @ApiProperty({
    description: 'UUID do usuário que atualizou o arquivo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  uploadedBy: string;
}