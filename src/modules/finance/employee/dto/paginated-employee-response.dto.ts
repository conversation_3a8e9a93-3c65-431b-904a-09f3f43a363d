import { ApiProperty } from '@nestjs/swagger';
import { EmployeeResponseDto } from './employee-response.dto';

export class PaginatedEmployeeResponseDto {
  @ApiProperty({
    type: [EmployeeResponseDto],
    description: 'Lista de colaboradores',
  })
  items: EmployeeResponseDto[];

  @ApiProperty({ description: 'Numero total de itens' })
  total: number;

  @ApiProperty({ description: 'Numero de itens por pagina' })
  limit: number;

  @ApiProperty({ description: 'Numero de itens pulados' })
  offset: number;
}
