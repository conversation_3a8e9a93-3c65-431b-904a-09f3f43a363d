import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateDocumentUseCase } from '../../documents/application/use-cases/create-document.use-case';
import { CreateDocumentDto } from '../../documents/infrastructure/dto/create-document.dto';
import { EntityType } from '../../documents/domain/enums/entity-type.enum';
import { Document } from '../../documents/domain/entities/document.entity';
import { ListDocumentsUseCase } from '../../documents/application/use-cases/list-documents.use-case';
import { DownloadDocumentUseCase } from '../../documents/application/use-cases/download-document.use-case';
import { PrismaService } from '../../../infrastructure/prisma/prisma.service';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';

@Injectable()
export class SupplierDocumentService {
  constructor(
    private readonly createDocumentUseCase: CreateDocumentUseCase,
    private readonly listDocumentsUseCase: ListDocumentsUseCase,
    private readonly downloadDocumentUseCase: DownloadDocumentUseCase,
    private readonly prisma: PrismaService,
    private readonly validateSupplierActivationUseCase: ValidateSupplierActivationUseCase,
  ) { }

  async uploadSupplierDocuments(
    supplierUuid: string,
    files: Express.Multer.File[],
    documentsMetadata: string,
    userId: string,
  ): Promise<Document[]> {
    let metadataArray: unknown[];
    try {
      metadataArray = JSON.parse(documentsMetadata);
    } catch (_error) {
      throw new Error(
        'O campo documentsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(metadataArray) ||
      files.length !== metadataArray.length
    ) {
      throw new Error(
        'O número de metadados de documento não corresponde ao número de arquivos enviados.',
      );
    }

    const results: Document[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const metadata = metadataArray[i] as Record<string, unknown>;

      const dto: CreateDocumentDto = {
        entityType: EntityType.SUPPLIER,
        entityUuid: supplierUuid,
        uploadedBy: userId,
        responsible: (metadata.responsible as string) || 'N/A',
        department: (metadata.department as string) || 'N/A',
        description: metadata.description as string,
        expirationDate: metadata.expirationDate as string,
      };

      const document = await this.createDocumentUseCase.execute(dto, file);
      results.push(document);
    }

    try {
      await this.validateSupplierActivationUseCase.execute(
        supplierUuid,
        userId,
      );
    } catch (error) {
      console.error(
        '[SupplierDocumentService] Erro ao tentar ativar supplier após upload de documentos:',
        error,
      );
    }

    return results;
  }

  async listSupplierDocuments(supplierUuid: string): Promise<Document[]> {
    const { items } = await this.listDocumentsUseCase.execute({
      entityType: EntityType.SUPPLIER,
      entityUuid: supplierUuid,
    });

    // Gerar URLs de download para todos os documentos
    const documentsWithUrls = await Promise.all(
      items.map(async (document) => {
        if (document.versions && document.versions.length > 0) {
          try {
            // Gerar URL para a versão atual
            const currentVersion = document.versions.find(v => v.versionId === document.currentVersion);
            if (currentVersion) {
              const { url, fileName } = await this.downloadDocumentUseCase.execute(document.uuid);
              return {
                ...document,
                downloadUrl: url,
                fileName: fileName,
              };
            }
          } catch (error) {
            console.error(`Erro ao gerar URL de download para documento ${document.uuid}:`, error);
          }
        }
        return document;
      })
    );

    return documentsWithUrls;
  }

  async getSupplierDocument(supplierUuid: string, documentUuid: string): Promise<Document | null> {
    const supplierExists = await this.prisma.supplier.findUnique({
      where: { id: supplierUuid },
    });
    if (!supplierExists) {
      throw new NotFoundException(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    }

    // Buscar documento específico
    const { items } = await this.listDocumentsUseCase.execute({
      entityType: EntityType.SUPPLIER,
      entityUuid: supplierUuid,
    });

    const document = items.find(doc => doc.uuid === documentUuid);
    if (!document) {
      return null;
    }

    // Gerar URL de download se houver versões
    if (document.versions && document.versions.length > 0) {
      try {
        const currentVersion = document.versions.find(v => v.versionId === document.currentVersion);
        if (currentVersion) {
          const { url, fileName } = await this.downloadDocumentUseCase.execute(document.uuid);
          return {
            ...document,
            downloadUrl: url,
            fileName: fileName,
          };
        }
      } catch (error) {
        console.error(`Erro ao gerar URL de download para documento ${document.uuid}:`, error);
      }
    }

    return document;
  }
} 