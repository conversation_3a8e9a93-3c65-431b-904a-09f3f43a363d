import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { CreateSupplierUseCase } from '@/core/application/use-cases/supplier/create-supplier.use-case';
import { CreateSupplierDto } from '../dto/create-supplier.dto';
import { SupplierResponseDto } from '../dto/supplier-response.dto';
import { Supplier } from '@/core/domain/supplier/entities/supplier.entity';
import { ListSupplierByUuidUseCase } from '@/core/application/use-cases/supplier/list-supplier-by-uuid.use-case';
import { ListSuppliersUseCase } from '@/core/application/use-cases/supplier/list-suppliers.use-case';
import { ListSuppliersDto } from '../dto/list-suppliers.dto';
import { PaginatedSuppliersResponseDto } from '../dto/paginated-suppliers-response.dto';
import { DeleteSupplierUseCase } from '@/core/application/use-cases/supplier/delete-supplier.use-case';
import { UpdateSupplierUseCase } from '@/core/application/use-cases/supplier/update-supplier.use-case';
import { UpdateSupplierDto } from '../dto/update-supplier.dto';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';
import { GetSupplierByUserIdUsecase } from '@/core/application/use-cases/supplier/get-supplier-by-user-id.use-case';
import { PrismaService } from '../../../infrastructure/prisma/prisma.service';
import { SupplierContractService } from './supplier-contract.service';
import { SupplierDocumentService } from './supplier-document.service';
import { SupplierContactService } from './supplier-contact.service';

@Injectable()
export class SupplierService {
  constructor(
    private readonly createSupplierUseCase: CreateSupplierUseCase,
    private readonly listSupplierByUuidUseCase: ListSupplierByUuidUseCase,
    private readonly listSuppliersUseCase: ListSuppliersUseCase,
    private readonly deleteSuppliersUseCase: DeleteSupplierUseCase,
    private readonly updateSupplierUseCase: UpdateSupplierUseCase,
    private readonly getSupplierByUserIdUseCase: GetSupplierByUserIdUsecase,
    private readonly prisma: PrismaService,
    private readonly supplierContractService: SupplierContractService,
    private readonly supplierDocumentService: SupplierDocumentService,
    private readonly supplierContactService: SupplierContactService,
  ) { }

  async listSupplierByUuid(uuid: string): Promise<SupplierResponseDto> {
    const supplier = await this.listSupplierByUuidUseCase.execute(uuid);
    return this.mapToResponseDto(supplier);
  }

  async createSupplier(
    dto: CreateSupplierDto,
    userId: string,
  ): Promise<SupplierResponseDto> {
    const supplier = await this.createSupplierUseCase.execute(dto, userId);
    return this.mapToResponseDto(supplier);
  }

  async listSuppliers(
    params: ListSuppliersDto,
  ): Promise<PaginatedSuppliersResponseDto> {
    const { items, total } = await this.listSuppliersUseCase.execute({
      limit: params.limit || 10,
      offset: params.offset || 0,
      cnpj: params.cnpj,
      name: params.name,
      type: params.type,
      status: params.status,
    });

    return {
      items: items.map((supplier) => this.mapToResponseDto(supplier)),
      total,
      limit: params.limit || 10,
      offset: params.offset || 0,
    };
  }

  async findByUserId(userId: string): Promise<SupplierResponseDto> {
    const supplier = await this.getSupplierByUserIdUseCase.execute(userId);
    return this.mapToResponseDto(supplier);
  }

  async deleteSupplier(uuid: string): Promise<void> {
    await this.deleteSuppliersUseCase.execute({ uuid });
  }

  async updateSupplier(
    uuid: string,
    dto: UpdateSupplierDto,
    userId: string,
  ): Promise<SupplierResponseDto> {
    const updated = await this.updateSupplierUseCase.execute(uuid, dto, userId);
    return this.mapToResponseDto(updated);
  }



  private mapToResponseDto(supplier: Supplier): SupplierResponseDto {
    const addressJson = supplier.address.toJSON();

    return {
      id: supplier.id,
      name: supplier.name,
      cnpj: supplier.document,
      email: supplier.email,
      type: supplier.type,
      tradeName: supplier.tradeName || undefined,
      address: {
        street: addressJson.street,
        number: addressJson.number || undefined,
        complement: addressJson.complement || undefined,
        neighborhood: addressJson.neighborhood,
        city: addressJson.city,
        zipCode: addressJson.zipCode,
        state: addressJson.state,
      },
      status: supplier.status,
      stateRegistration: supplier.stateRegistration,
      municipalRegistration: supplier.municipalRegistration,
      taxRegime: supplier.taxRegime,
      companySize: supplier.companySize,
      createdAt: supplier.createdAt.toISOString(),
      createdBy: supplier.createdBy,
      updatedAt: supplier.updatedAt.toISOString(),
      updatedBy: supplier.updatedBy,
    };
  }

  getSupplierTypes(): string[] {
    return Object.values(SupplierType);
  }
}
