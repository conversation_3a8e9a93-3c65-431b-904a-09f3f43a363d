import { Injectable } from '@nestjs/common';
import { CreatePayablesTypeDto } from '@modules/finance/dto/create-payables-type.dto';
import { PayablesTypeResponseDto } from '@modules/finance/dto/payables-type-response.dto';
import { CreatePayablesTypeUseCase } from '@core/application/use-cases/payables-type/create-payables-type.use-case';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';
import { ListPayablesTypeUseCase } from '@core/application/use-cases/payables-type/list-payables-type.use-case';
import { ListPayablesTypesDto } from '@modules/finance/dto/list-payables-type.dto';
import { PaginatedPayablesTypeResponseDto } from '@modules/finance/dto/paginated-payables-type-response.dto';
import { ListPayablesTypeByUuidUseCase } from '@core/application/use-cases/payables-type/list-payable-type-by-uuid.use-case';
import { UpdatePayablesTypeDto } from '../dto/update-payables-type.dto';
import { UpdatePayablesTypeUseCase } from '@core/application/use-cases/payables-type/update-payable-type.use-case';
import { DeletePayableTypeUseCase } from '@core/application/use-cases/payables-type/delete-payable-type.use-case';

@Injectable()
export class PayablesTypeService {
  constructor(
    private readonly createPayablesTypeUseCase: CreatePayablesTypeUseCase,
    private readonly listPayablesTypeUseCase: ListPayablesTypeUseCase,
    private readonly listPayablesTypeByUuidUseCase: ListPayablesTypeByUuidUseCase,
    private readonly updatePayablesTypeUseCase: UpdatePayablesTypeUseCase,
    private readonly deletePayableTypeUseCase: DeletePayableTypeUseCase,
  ) {}

  async findByUuid(uuid: string): Promise<PayablesTypeResponseDto> {
    const payablesType = await this.listPayablesTypeByUuidUseCase.execute(uuid);
    return this.toResponseDto(payablesType);
  }

  async create(
    createPayablesTypeDto: CreatePayablesTypeDto,
  ): Promise<PayablesTypeResponseDto> {
    const payablesType = await this.createPayablesTypeUseCase.execute(
      createPayablesTypeDto,
    );
    return this.toResponseDto(payablesType);
  }

  private toResponseDto(payablesType: PayablesType): PayablesTypeResponseDto {
    const json = payablesType.toJSON();
    return {
      id: json.id,
      code: json.code,
      description: json.description,
      createdBy: json.createdBy,
      createdAt: json.createdAt,
      updatedBy: json.updatedBy,
      updatedAt: json.updatedAt,
    };
  }

  async listPayablesTypes(
    query: ListPayablesTypesDto,
  ): Promise<PaginatedPayablesTypeResponseDto> {
    const result = await this.listPayablesTypeUseCase.execute({
      limit: query.limit ?? 10,
      offset: query.offset ?? 0,
      code: query.code,
      description: query.description,
    });

    return {
      items: result.items.map((item) => this.toResponseDto(item)),
      total: result.total,
      limit: query.limit ?? 10,
      offset: query.offset ?? 0,
    };
  }

  async update(
    uuid: string,
    updatePayablesTypeDto: UpdatePayablesTypeDto,
  ): Promise<PayablesTypeResponseDto> {
    const payablesType = await this.updatePayablesTypeUseCase.execute(
      uuid,
      updatePayablesTypeDto,
    );
    return this.toResponseDto(payablesType);
  }

  async delete(uuid: string): Promise<void> {
    await this.deletePayableTypeUseCase.execute({ uuid });
  }
}
