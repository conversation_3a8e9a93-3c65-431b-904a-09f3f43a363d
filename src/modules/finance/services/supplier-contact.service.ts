import { Injectable } from '@nestjs/common';
import { ListSupplierContactsUseCase } from '@/core/application/use-cases/supplier/list-supplier-contacts.use-case';
import { CreateSupplierContactUseCase } from '@/core/application/use-cases/supplier/create-supplier-contact.use-case';
import { UpdateSupplierContactUseCase } from '@/core/application/use-cases/supplier/update-supplier-contact.usecase';
import { DeleteSupplierContactUseCase } from '@/core/application/use-cases/supplier/delete-supplier-contact.usecase';
import { ContactResponseDto } from '../dto/supplier-response.dto';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';

@Injectable()
export class SupplierContactService {
  constructor(
    private readonly listSupplierContactsUseCase: ListSupplierContactsUseCase,
    private readonly createSupplierContactUseCase: CreateSupplierContactUseCase,
    private readonly updateSupplierContactUseCase: UpdateSupplierContactUseCase,
    private readonly deleteSupplierContactUseCase: DeleteSupplierContactUseCase,
    private readonly validateSupplierActivationUseCase: ValidateSupplierActivationUseCase,
  ) { }

  async listSupplierContacts(
    supplierUuid: string,
  ): Promise<{ contacts: ContactResponseDto[] }> {
    const contacts =
      await this.listSupplierContactsUseCase.execute(supplierUuid);
    const contactsResponse = contacts.map((contact) => ({
      id: contact.id,
      contact: contact.contact,
      type: contact.type,
      area: contact.area,
      responsible: contact.responsible,
    }));
    return { contacts: contactsResponse };
  }

  async createSupplierContacts(
    supplierUuid: string,
    contactsData: Array<{
      contact: string;
      type: string;
      area: string;
      responsible: string;
    }>,
  ): Promise<ContactResponseDto[]> {
    const now = new Date();
    const createdContacts = await Promise.all(
      contactsData.map((contactData) =>
        this.createSupplierContactUseCase.execute(supplierUuid, {
          ...contactData,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
        }),
      ),
    );

    // Tentar ativar o supplier automaticamente após criar contatos
    console.log(
      `[SupplierContactService] Tentando ativar supplier ${supplierUuid} após criar contatos`,
    );
    try {
      const activated = await this.validateSupplierActivationUseCase.execute(
        supplierUuid,
        'system',
      );
      console.log(
        `[SupplierContactService] Resultado da ativação após criar contatos: ${activated}`,
      );
    } catch (error) {
      console.error(
        '[SupplierContactService] Erro ao tentar ativar supplier após criar contatos:',
        error,
      );
    }

    return createdContacts.map((contact) => ({
      contact: contact.contact,
      type: contact.type,
      area: contact.area,
      responsible: contact.responsible,
    }));
  }

  async updateSupplierContacts(
    contactUUID: string,
    data: {
      contact?: string;
      type?: string;
      area?: string;
      responsible?: string;
    },
  ): Promise<ContactResponseDto> {
    const result = await this.updateSupplierContactUseCase.execute(contactUUID, data)
    return result;
  }

  async deleteSupplierContacts(contactUUID: string) {
    return await this.deleteSupplierContactUseCase.execute(contactUUID)
  }
} 