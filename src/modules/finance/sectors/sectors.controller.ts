import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
  Get,
  Param,
  NotFoundException,
  Patch,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { SectorsService } from './sectors.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../auth/enums/role.enum';
import { CreateSectorDto } from './dto/create-sector.dto';
import { SectorResponseDto } from './dto/sector-response.dto';
import { GetSectorParamsDto } from './dto/get-sector-params.dto';
import { isUUID } from 'class-validator';
import { UpdateSectorDto } from './dto/update-sector.dto';
import { ListSectorsQueryDto } from './dto/list-sectors-query.dto';
import { SectorListResponseDto } from './dto/sector-list-response.dto';
import {
  ApiCreateSector,
  ApiDeleteSector,
  ApiGetSector,
  ApiListSectors,
  ApiUpdateSector,
} from '@/infrastructure/swagger/decorators';

@ApiTags('Sectors')
@Controller('finance/sectors')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SectorsController {
  constructor(private readonly sectorsService: SectorsService) {}

  @Get(':uuid')
  @Roles(Role.FINANCE_ADMIN)
  @ApiGetSector()
  async getSectorByUuid(
    @Param() params: GetSectorParamsDto,
  ): Promise<SectorResponseDto> {
    if (!isUUID(params.uuid, 4)) {
      throw new BadRequestException({
        code: 'InvalidUUID',
        message: 'UUID mal formatado',
      });
    }

    try {
      const sector = await this.sectorsService.getSectorByUuid(params.uuid);
      return {
        uuid: sector.uuid,
        code: sector.code,
        description: sector.description,
        createdBy: sector.createdBy,
        updatedBy: sector.updatedBy,
        createdAt: sector.createdAt.toISOString(),
        updatedAt: sector.updatedAt.toISOString(),
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException({
          code: 'NotFound',
          message: `Sector not found for uuid ${params.uuid}`,
        });
      }
      throw error;
    }
  }

  @Post()
  @Roles(Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiCreateSector()
  async createSector(
    @Body() createSectorDto: CreateSectorDto,
  ): Promise<SectorResponseDto> {
    try {
      const sector = await this.sectorsService.createSector({
        code: createSectorDto.code,
        description: createSectorDto.description,
        createdBy: createSectorDto.createdBy,
        updatedBy: createSectorDto.updatedBy,
      });

      return {
        uuid: sector.uuid,
        code: sector.code,
        description: sector.description,
        createdBy: sector.createdBy,
        updatedBy: sector.updatedBy,
        createdAt: sector.createdAt.toISOString(),
        updatedAt: sector.updatedAt.toISOString(),
      };
    } catch (error: unknown) {
      if (
        error instanceof Error &&
        error.message === 'Sector code already exists'
      ) {
        throw new BadRequestException({
          code: 'DuplicateCode',
          message: 'Código do setor já existe',
        });
      }
      throw error;
    }
  }

  @Patch(':uuid')
  @Roles(Role.FINANCE_ADMIN)
  @ApiUpdateSector()
  async updateSectorPatch(
    @Param('uuid') uuid: string,
    @Body() dto: UpdateSectorDto,
  ): Promise<SectorResponseDto> {
    const sector = await this.sectorsService.updateSector(uuid, dto);

    return {
      uuid: sector.uuid,
      code: sector.code,
      description: sector.description,
      createdBy: sector.createdBy,
      updatedBy: sector.updatedBy,
      createdAt: sector.createdAt.toISOString(),
      updatedAt: sector.updatedAt.toISOString(),
    };
  }

  @Delete(':uuid')
  @HttpCode(204)
  @Roles(Role.FINANCE_ADMIN)
  @ApiDeleteSector()
  async delete(@Param('uuid') uuid: string): Promise<void> {
    return this.sectorsService.deleteSector(uuid);
  }

  @Get()
  @Roles(Role.FINANCE_ADMIN)
  @ApiListSectors()
  async listSectors(
    @Query() query: ListSectorsQueryDto,
  ): Promise<SectorListResponseDto> {
    const result = await this.sectorsService.listSectors({
      limit: query.limit ?? 10,
      offset: query.offset ?? 0,
      code: query.code,
      description: query.description,
    });

    return {
      items: result.items.map((sector) => ({
        uuid: sector.uuid,
        code: sector.code,
        description: sector.description,
        createdBy: sector.createdBy,
        updatedBy: sector.updatedBy,
        createdAt: sector.createdAt.toISOString(),
        updatedAt: sector.updatedAt.toISOString(),
      })),
      limit: query.limit ?? 10,
      offset: query.offset ?? 0,
      total: result.total,
    };
  }
}
