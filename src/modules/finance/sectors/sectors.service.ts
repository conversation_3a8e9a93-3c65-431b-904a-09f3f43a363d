import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import {
  CreateSectorUseCase,
  CreateSectorInput,
} from '@/core/application/use-cases/sector/create-sector.use-case';
import { Sector } from '@/core/domain/entities/sector.entity';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';
import { SectorRepositoryPort } from '@/core/ports/repositories/sector-repository.port';
import {
  UpdateSectorUseCase,
  UpdateSectorInput,
} from '@/core/application/use-cases/sector/update-sector.use-case';
import { DeleteSectorUseCase } from '@/core/application/use-cases/sector/delete-sector.use-case';
import {
  ListSectorsUseCase,
  ListSectorsInput,
} from '@/core/application/use-cases/sector/list-sectors.use-case';
import { ListSectorsResult } from '@/core/ports/repositories/sector-repository.port';

@Injectable()
export class SectorsService {
  constructor(
    private readonly createSectorUseCase: CreateSectorUseCase,
    private readonly updateSectorUseCase: UpdateSectorUseCase,
    private readonly deleteSectorUseCase: DeleteSectorUseCase,
    private readonly listSectorsUseCase: ListSectorsUseCase,
    @Inject(SECTOR_REPOSITORY)
    private readonly sectorRepository: SectorRepositoryPort,
  ) {}
  async createSector(input: CreateSectorInput): Promise<Sector> {
    return this.createSectorUseCase.execute(input);
  }

  async getSectorByUuid(uuid: string): Promise<Sector> {
    const sector = await this.sectorRepository.findByUuid(uuid);

    if (!sector) {
      throw new NotFoundException(`Setor não encontrado para uuid ${uuid}`);
    }

    return sector;
  }

  async updateSector(uuid: string, input: UpdateSectorInput): Promise<Sector> {
    return this.updateSectorUseCase.execute(uuid, input);
  }

  async deleteSector(uuid: string): Promise<void> {
    return this.deleteSectorUseCase.execute({ uuid });
  }

  async listSectors(input: ListSectorsInput): Promise<ListSectorsResult> {
    return this.listSectorsUseCase.execute(input);
  }
}
