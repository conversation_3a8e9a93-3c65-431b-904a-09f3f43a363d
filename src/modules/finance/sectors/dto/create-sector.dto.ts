import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  MaxLength,
  Matches,
  IsUUID,
} from 'class-validator';

export class CreateSectorDto {
  @ApiProperty({
    description: 'Unique code for the sector',
    example: 'HR',
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9]+$/, {
    message: 'Code must contain only alphanumeric characters',
  })
  code: string;

  @ApiProperty({
    description: 'Description of the sector',
    example: 'Human Resources',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  description: string;

  @ApiProperty({
    description: 'UUID of the user creating the sector',
    example: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  createdBy: string;

  @ApiProperty({
    description: 'UUID of the user updating the sector',
    example: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  updatedBy: string;
}
