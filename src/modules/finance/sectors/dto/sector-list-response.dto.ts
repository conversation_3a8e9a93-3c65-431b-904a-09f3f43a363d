import { ApiProperty } from '@nestjs/swagger';
import { SectorResponseDto } from './sector-response.dto';

export class SectorListResponseDto {
  @ApiProperty({
    description: 'List of sectors',
    type: [SectorResponseDto],
  })
  items: SectorResponseDto[];

  @ApiProperty({
    description: 'Number of items per page',
    example: 5,
  })
  limit: number;

  @ApiProperty({
    description: 'Number of items skipped',
    example: 0,
  })
  offset: number;

  @ApiProperty({
    description: 'Total number of items',
    example: 1,
  })
  total: number;
}
