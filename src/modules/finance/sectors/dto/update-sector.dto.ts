import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  MaxLength,
  MinLength,
  IsOptional,
  Matches,
} from 'class-validator';

export class UpdateSectorDto {
  @ApiProperty({
    description: 'Unique code for the sector',
    example: 'OFFICE',
    minLength: 1,
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9]+$/, {
    message: 'Code must contain only alphanumeric characters',
  })
  code?: string;

  @ApiProperty({
    description: 'Description of the sector',
    example: 'Despesas de Escritório',
    minLength: 1,
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  description?: string;

  @ApiProperty({
    description: 'UUID do usuario atualizando o setor',
    example: '11111111-**************-************',
  })
  @IsUUID()
  updatedBy: string;
}
