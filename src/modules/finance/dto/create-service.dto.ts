import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsString,
  IsUUID,
  Min<PERSON>ength,
  <PERSON><PERSON>ength,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EntityType } from '../../../core/domain/service/enums/entity-type.enum';

export class CreateServiceDto {
  @ApiProperty({
    description: 'Tipo da entidade (CLIENT ou SUPPLIER)',
    enum: EntityType,
    example: EntityType.SUPPLIER,
  })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID da entidade (Supplier ou Customer)',
    format: 'uuid',
    example: '40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4',
  })
  @IsUUID()
  entityUuid: string;

  @ApiProperty({
    description: 'Tipo do serviço',
    example: 'Consultoria Financeira',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  type: string;

  @ApiProperty({
    description: 'Taxa do serviço',
    example: '5.5%',
    minLength: 1,
    maxLength: 50,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  rate: string;

  @ApiProperty({
    description: 'Descrição detalhada do serviço',
    example:
      'Serviço de consultoria financeira especializada em análise de crédito',
    minLength: 10,
    maxLength: 1000,
  })
  @IsString()
  @MinLength(10)
  @MaxLength(1000)
  description: string;
}

export class ServiceItemDto {
  @ApiProperty({
    description: 'Tipo do serviço',
    example: 'Consultoria',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  type: string;

  @ApiProperty({
    description: 'Taxa do serviço',
    example: 'R$ 150/hora',
    minLength: 1,
    maxLength: 50,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  rate: string;

  @ApiProperty({
    description: 'Descrição detalhada do serviço',
    example: 'Consultoria em TI',
    minLength: 10,
    maxLength: 1000,
  })
  @IsString()
  @MinLength(10)
  @MaxLength(1000)
  description: string;
}

export class CreateMultipleServicesDto {
  @ApiProperty({
    description: 'Array de serviços para criar',
    type: [ServiceItemDto],
    example: [
      {
        type: 'Consultoria',
        rate: 'R$ 150/hora',
        description: 'Consultoria em TI',
      },
      {
        type: 'Desenvolvimento',
        rate: 'R$ 100/hora',
        description: 'Desenvolvimento de software',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServiceItemDto)
  services: ServiceItemDto[];
}
