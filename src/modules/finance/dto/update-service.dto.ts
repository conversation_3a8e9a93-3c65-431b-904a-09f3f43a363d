import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, MinLength, MaxLength } from 'class-validator';

export class UpdateServiceDto {
  @ApiPropertyOptional({
    description: 'Tipo do serviço',
    example: 'Consultoria Financeira Avançada',
    minLength: 3,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  type?: string;

  @ApiPropertyOptional({
    description: 'Taxa do serviço',
    example: '6.0%',
    minLength: 1,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  rate?: string;

  @ApiPropertyOptional({
    description: 'Descrição detalhada do serviço',
    example: 'Serviço de consultoria financeira especializada em análise de crédito e risco',
    minLength: 10,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MinLength(10)
  @MaxLength(1000)
  description?: string;
} 