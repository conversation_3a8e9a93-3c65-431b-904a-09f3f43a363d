import {
  IsString,
  IsE<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  Matches,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SupplierStatus } from '../../../core/domain/supplier/enums/supplier-status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';
import { TaxRegime, CompanySize } from '@prisma/client';

class AddressDto {
  @ApiProperty({
    description: 'Street address',
    example: 'Rua das Flores',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  street: string;

  @ApiProperty({
    description: 'Number of the address',
    example: '123',
    required: false,
    maxLength: 10,
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  number?: string;

  @ApiProperty({
    description: 'Complementary information (optional)',
    example: 'Apt 45',
    required: false,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  complement?: string;

  @ApiProperty({
    description: 'Neighborhood name',
    example: 'Jardim das <PERSON>s',
    required: false,
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  neighborhood?: string;


  @ApiProperty({
    description: 'City name',
    example: 'São Paulo',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  city: string;

  @ApiProperty({
    description: 'ZIP code in format 00000-000 or 00000000',
    example: '01311-000',
    pattern: '^\\d{5}-?\\d{3}$',
  })
  @IsString()
  @Matches(/^\d{5}-?\d{3}$/, {
    message: 'Invalid zip code format. Use: 00000-000 or 00000000',
  })
  zipCode: string;

  @ApiProperty({
    description: 'State abbreviation (2 characters)',
    example: 'SP',
    minLength: 2,
    maxLength: 2,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(2)
  state: string;
}

class ContactDto {
  @ApiProperty({ description: 'Contact info (e.g. email or phone)', example: '<EMAIL>' })
  @IsString()
  contact: string;

  @ApiProperty({ description: 'Type of contact', example: 'email' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Area/department', example: 'Finance' })
  @IsString()
  area: string;

  @ApiProperty({ description: 'Responsible person', example: 'Maria Silva' })
  @IsString()
  responsible: string;
}

export class UpdateSupplierDto {
  @ApiProperty({
    description: 'Company name',
    example: 'Company Name Ltd',
    minLength: 3,
    maxLength: 100,
    required: false,
  })
  @IsString()
  @IsOptional()
  @MinLength(3)
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'CNPJ (Brazilian company tax ID) - 14 digits',
    example: '**************',
    pattern: '^\\d{14}$',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d{14}$/, {
    message: 'CNPJ must be exactly 14 digits',
  })
  cnpj?: string;

  @ApiProperty({
    description: 'Company trade name (optional)',
    example: 'Company Trade Name',
    required: false,
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  tradeName?: string;

  @ApiProperty({
    description: 'Company address information',
    type: AddressDto,
    required: false,
  })
  @ValidateNested()
  @Type(() => AddressDto)
  @IsOptional()
  address?: Partial<AddressDto>;

  @ApiProperty({
    description: 'Supplier status',
    enum: SupplierStatus,
    example: SupplierStatus.ACTIVE,
    required: false,
  })
  @IsEnum(SupplierStatus)
  @IsOptional()
  status?: SupplierStatus;

  @ApiProperty({
    description: 'Supplier email',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'Supplier type',
    enum: SupplierType,
    example: 'BANK',
  })
  @IsEnum(SupplierType)
  type: SupplierType;

  @ApiProperty({
    description: 'Supplier state registration',
    example: '**************',
    required: false,
  })
  @IsString()
  @IsOptional()
  stateRegistration?: string;

  @ApiProperty({
    description: 'Supplier municipal registration',
    example: '**************',
    required: false,
  })
  @IsString()
  @IsOptional()
  municipalRegistration?: string;
  
  @ApiProperty({
    description: 'Supplier tax regime',
    example: 'SIMPLES_NACIONAL',
    required: false,
  })
  @IsEnum(TaxRegime)
  @IsOptional()
  taxRegime?: TaxRegime;

  @ApiProperty({
    description: 'Supplier company size',
    example: 'MEI',
    required: false,
  })
  @IsEnum(CompanySize)
  @IsOptional()
  companySize?: CompanySize;
}
