import { Injectable } from '@nestjs/common';
import { CreateCostCenterDto } from '@modules/finance/cost-center/dto/create-cost-center.dto';
import { CostCenterResponseDto } from '@modules/finance/cost-center/dto/cost-center-response.dto';
import { CreateCostCenterUseCase } from '@core/application/use-cases/cost-center/create-cost-center.use-case';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';
import { DeleteCostCenterUseCase } from '@/core/application/use-cases/cost-center/delete-cost-center.use-case';
import { UpdateCostCenterDto } from '../dto/update-cost-center.dto';
import { UpdateCostCenterUseCase } from '@/core/application/use-cases/cost-center/update-cost-center.use-case';
import { ListCostCentersUseCase } from '@/core/application/use-cases/cost-center/list-cost-centers.use-case';

@Injectable()
export class CostCenterService {
  constructor(
    private readonly createCostCenterUseCase: CreateCostCenterUseCase,
    private readonly deleteCostCenterUseCase: DeleteCostCenterUseCase,
    private readonly updateCostCenterUseCase: UpdateCostCenterUseCase,
    private readonly listCostCentersUseCase: ListCostCentersUseCase,
  ) {}

  async create(
    createCostCenterDto: CreateCostCenterDto,
  ): Promise<CostCenterResponseDto> {
    const costCenter =
      await this.createCostCenterUseCase.execute(createCostCenterDto);
    return this.toResponseDto(costCenter);
  }

  async delete(uuid: string): Promise<void> {
    await this.deleteCostCenterUseCase.execute(uuid);
  }

  async update(
    uuid: string,
    updateCostCenterDto: UpdateCostCenterDto,
  ): Promise<CostCenterResponseDto> {
    const costCenter = await this.updateCostCenterUseCase.execute(
      uuid,
      updateCostCenterDto,
    );
    return this.toResponseDto(costCenter);
  }

  async findAll(): Promise<CostCenterResponseDto[]> {
    const result = await this.listCostCentersUseCase.execute();
    return result.items.map((item: CostCenter) => this.toResponseDto(item));
  }

  private toResponseDto(costCenter: CostCenter): CostCenterResponseDto {
    const json = costCenter.toJSON();
    return {
      uuid: json.id,
      description: json.description,
      createdBy: json.createdBy,
      createdAt: json.createdAt,
      updatedBy: json.updatedBy,
      updatedAt: json.updatedAt,
    };
  }
}
