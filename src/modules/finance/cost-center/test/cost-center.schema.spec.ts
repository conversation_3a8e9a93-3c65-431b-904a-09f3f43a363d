import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../auth/guards/roles.guard';
import { CostCenterController } from '../controllers/cost-center.controller';
import { CostCenterService } from '../services/cost-center.service';
import { ConfigService } from '@nestjs/config';

interface CostCenterResponse {
  uuid: string;
  description: string;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
}

interface FormatErrorParams {
  format: string;
}

describe('CostCenter Schema Validation (Contract Tests)', () => {
  let app: INestApplication;
  const mockCostCenterService = {
    create: jest.fn(),
  };

  const mockAuthGuard = { canActivate: jest.fn().mockReturnValue(true) };
  const mockRolesGuard = { canActivate: jest.fn().mockReturnValue(true) };

  // Mock do ConfigService para o JwtAuthGuard
  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      if (key === 'jwt.secret') return 'test-secret';
      if (key === 'jwt.expiresIn') return '1h';
      return null;
    }),
  };

  // JSON Schema for CostCenterResponse
  const costCenterResponseSchema = {
    type: 'object',
    required: [
      'uuid',
      'description',
      'createdBy',
      'createdAt',
      'updatedBy',
      'updatedAt',
    ],
    properties: {
      uuid: { type: 'string', format: 'uuid' },
      description: { type: 'string', minLength: 1, maxLength: 255 },
      createdBy: { type: 'string', format: 'uuid' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedBy: { type: 'string', format: 'uuid' },
      updatedAt: { type: 'string', format: 'date-time' },
    },
    additionalProperties: false,
  } as const;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [CostCenterController],
      providers: [
        {
          provide: CostCenterService,
          useValue: mockCostCenterService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    // Configurar o ValidationPipe com as opções corretas
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
      }),
    );
    await app.init();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('CostCenter Schema', () => {
    it('should validate a mock response against JSON schema', () => {
      // Arrange
      const mockResponse: CostCenterResponse = {
        uuid: '11111111-**************-************',
        description: 'Despesas de Marketing',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: new Date().toISOString(),
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: new Date().toISOString(),
      };

      // Act
      const ajv = new Ajv({ allErrors: true });
      addFormats(ajv);
      const validate = ajv.compile(costCenterResponseSchema);
      const isValid = validate(mockResponse);

      // Assert
      if (!isValid && validate.errors) {
        console.error('Schema validation errors:', validate.errors);
      }

      expect(isValid).toBe(true);
    });

    it('should reject invalid UUID format', () => {
      // Arrange
      const invalidResponse: CostCenterResponse = {
        uuid: 'invalid-uuid',
        description: 'Despesas de Marketing',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: new Date().toISOString(),
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: new Date().toISOString(),
      };

      // Act
      const ajv = new Ajv({ allErrors: true });
      addFormats(ajv);
      const validate = ajv.compile(costCenterResponseSchema);
      const isValid = validate(invalidResponse);

      // Assert
      expect(isValid).toBe(false);
      expect(validate.errors).toBeDefined();
      expect(validate.errors?.[0].keyword).toBe('format');
      expect((validate.errors?.[0].params as FormatErrorParams).format).toBe(
        'uuid',
      );
    });

    it('should reject empty description', () => {
      // Arrange
      const invalidResponse: CostCenterResponse = {
        uuid: '11111111-**************-************',
        description: '',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: new Date().toISOString(),
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: new Date().toISOString(),
      };

      // Act
      const ajv = new Ajv({ allErrors: true });
      addFormats(ajv);
      const validate = ajv.compile(costCenterResponseSchema);
      const isValid = validate(invalidResponse);

      // Assert
      expect(isValid).toBe(false);
      expect(validate.errors).toBeDefined();
      expect(validate.errors?.[0].keyword).toBe('minLength');
    });

    it('should reject additional properties', () => {
      // Arrange
      const invalidResponse = {
        uuid: '11111111-**************-************',
        description: 'Despesas de Marketing',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: new Date().toISOString(),
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: new Date().toISOString(),
        extraField: 'should not be here',
      };

      // Act
      const ajv = new Ajv({ allErrors: true });
      addFormats(ajv);
      const validate = ajv.compile(costCenterResponseSchema);
      const isValid = validate(invalidResponse);

      // Assert
      expect(isValid).toBe(false);
      expect(validate.errors).toBeDefined();
      expect(validate.errors?.[0].keyword).toBe('additionalProperties');
    });
  });
});
