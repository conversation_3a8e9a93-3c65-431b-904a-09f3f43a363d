import { Test, TestingModule } from '@nestjs/testing';
import { CostCenterService } from '../services/cost-center.service';
import { CreateCostCenterUseCase } from '@core/application/use-cases/cost-center/create-cost-center.use-case';
import { CreateCostCenterDto } from '../dto/create-cost-center.dto';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';
import { DeleteCostCenterUseCase } from '@/core/application/use-cases/cost-center/delete-cost-center.use-case';
import { UpdateCostCenterUseCase } from '@/core/application/use-cases/cost-center/update-cost-center.use-case';
import { UpdateCostCenterDto } from '../dto/update-cost-center.dto';
import { ListCostCentersUseCase } from '@core/application/use-cases/cost-center/list-cost-centers.use-case';

describe('CostCenterService', () => {
  let service: CostCenterService;
  let createCostCenterUseCase: CreateCostCenterUseCase;
  let deleteCostCenterUseCase: DeleteCostCenterUseCase;
  let updateCostCenterUseCase: UpdateCostCenterUseCase;

  const mockCreateCostCenterUseCase = {
    execute: jest.fn(),
  };

  const mockDeleteCostCenterUseCase = {
    execute: jest.fn(),
  };

  const mockUpdateCostCenterUseCase = {
    execute: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CostCenterService,
        {
          provide: CreateCostCenterUseCase,
          useValue: mockCreateCostCenterUseCase,
        },
        {
          provide: DeleteCostCenterUseCase,
          useValue: mockDeleteCostCenterUseCase,
        },
        {
          provide: UpdateCostCenterUseCase,
          useValue: mockUpdateCostCenterUseCase,
        },
        {
          provide: ListCostCentersUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<CostCenterService>(CostCenterService);
    createCostCenterUseCase = module.get<CreateCostCenterUseCase>(
      CreateCostCenterUseCase,
    );

    deleteCostCenterUseCase = module.get<DeleteCostCenterUseCase>(
      DeleteCostCenterUseCase,
    );

    updateCostCenterUseCase = module.get<UpdateCostCenterUseCase>(
      UpdateCostCenterUseCase,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a cost center successfully', async () => {
      // Arrange
      const createDto: CreateCostCenterDto = {
        description: 'Test Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const costCenterEntity = new CostCenter({
        id: '11111111-**************-************',
        description: createDto.description,
        createdBy: createDto.createdBy,
        updatedBy: createDto.updatedBy,
        createdAt: new Date('2025-05-12T00:00:00Z'),
        updatedAt: new Date('2025-05-12T00:00:00Z'),
      });

      mockCreateCostCenterUseCase.execute.mockResolvedValue(costCenterEntity);

      // Act
      const result = await service.create(createDto);

      // Assert
      // Usando uma função arrow para evitar problema de unbound method
      const executeFn = jest.spyOn(createCostCenterUseCase, 'execute');
      expect(executeFn).toHaveBeenCalledWith(createDto);
      expect(result).toEqual({
        uuid: '11111111-**************-************',
        description: 'Test Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: '2025-05-12T00:00:00.000Z',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: '2025-05-12T00:00:00.000Z',
      });
    });

    it('should throw an error if use case throws', async () => {
      // Arrange
      const createDto: CreateCostCenterDto = {
        description: 'Test Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const error = new Error('Test error');
      mockCreateCostCenterUseCase.execute.mockRejectedValue(error);

      // Act & Assert
      await expect(service.create(createDto)).rejects.toThrow(error);
    });
  });

  describe('delete', () => {
    it('should delete a cost center successfully', async () => {
      // Arrange
      const uuid = '11111111-**************-************';

      // Act
      await service.delete(uuid);

      // Assert
      const executeFn = jest.spyOn(deleteCostCenterUseCase, 'execute');
      expect(executeFn).toHaveBeenCalledWith(uuid);
    });

    it('should throw an error if use case throws', async () => {
      // Arrange

      const uuid = '11111111-**************-************';

      const error = new Error('Test error');
      mockDeleteCostCenterUseCase.execute.mockRejectedValue(error);

      // Act & Assert
      await expect(service.delete(uuid)).rejects.toThrow(error);
    });
  });

  describe('update', () => {
    it('should update a cost center successfully', async () => {
      // Arrange
      const updateDto: UpdateCostCenterDto = {
        description: 'Updated Cost Center',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const costCenterEntity = new CostCenter({
        id: '11111111-**************-************',
        description: updateDto.description || '',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedBy: updateDto.updatedBy,
        createdAt: new Date('2025-05-12T00:00:00Z'),
        updatedAt: new Date('2025-05-12T00:00:00Z'),
      });

      mockUpdateCostCenterUseCase.execute.mockResolvedValue(costCenterEntity);

      // Act
      const result = await service.update(
        '11111111-**************-************',
        updateDto,
      );

      // Assert
      // Usando uma função arrow para evitar problema de unbound method
      const executeFn = jest.spyOn(updateCostCenterUseCase, 'execute');
      expect(executeFn).toHaveBeenCalledWith(
        '11111111-**************-************',
        updateDto,
      );
      expect(result).toEqual({
        uuid: '11111111-**************-************',
        description: 'Updated Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: '2025-05-12T00:00:00.000Z',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: '2025-05-12T00:00:00.000Z',
      });
    });

    it('should throw an error if use case throws', async () => {
      // Arrange
      const updateDto: UpdateCostCenterDto = {
        description: 'Updated Cost Center',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const error = new Error('Test error');
      mockUpdateCostCenterUseCase.execute.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.update('11111111-**************-************', updateDto),
      ).rejects.toThrow(error);
    });
  });
});
