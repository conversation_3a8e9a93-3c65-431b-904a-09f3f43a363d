import { ApiProperty } from '@nestjs/swagger';

export class CostCenterResponseDto {
  @ApiProperty({
    description: 'UUID of the cost center',
    example: '11111111-**************-************',
  })
  uuid: string;

  @ApiProperty({
    description: 'Description of the cost center',
    example: 'Despesas de Marketing',
  })
  description: string;

  @ApiProperty({
    description: 'UUID of the user who created the cost center',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  createdBy: string;

  @ApiProperty({
    description: 'The date and time when the cost center was created',
    example: '2025-05-12T00:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'UUID of the user who last updated the cost center',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  updatedBy: string;

  @ApiProperty({
    description: 'The date and time when the cost center was last updated',
    example: '2025-05-12T00:00:00Z',
  })
  updatedAt: string;
}
