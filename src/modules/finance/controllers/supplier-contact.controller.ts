import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../core/domain/role.enum';
import { SupplierContactService } from '../services/supplier-contact.service';
import { ContactResponseDto } from '../dto/supplier-response.dto';

@ApiTags('Supplier Contacts')
@ApiBearerAuth()
@Controller('core/suppliers/:uuid/contacts')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SupplierContactController {
  constructor(private readonly supplierContactService: SupplierContactService) { }

  @Get()
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Listar contatos do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de contatos do fornecedor retornada com sucesso.',
    type: [ContactResponseDto],
  })
  async listSupplierContacts(
    @Param('uuid') uuid: string,
  ): Promise<{ contacts: ContactResponseDto[] }> {
    return this.supplierContactService.listSupplierContacts(uuid);
  }

  @Post()
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Criar contatos do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          contact: { type: 'string' },
          type: { type: 'string' },
          area: { type: 'string' },
          responsible: { type: 'string' },
        },
        required: ['contact', 'type', 'area', 'responsible'],
        example: {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Finance',
          responsible: 'Maria Silva',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Contatos criados com sucesso.',
    type: [ContactResponseDto],
  })
  async createSupplierContact(
    @Param('uuid') uuid: string,
    @Body()
    contactsData: Array<{
      contact: string;
      type: string;
      area: string;
      responsible: string;
    }>,
  ): Promise<ContactResponseDto[]> {
    return this.supplierContactService.createSupplierContacts(uuid, contactsData);
  }

  @Patch(':contactUuid')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Atualizar contato do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiParam({
    name: 'contactUuid',
    required: true,
    description: 'Contact UUID',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        contact: { type: 'string' },
        type: { type: 'string' },
        area: { type: 'string' },
        responsible: { type: 'string' },
      },
      example: {
        contact: '<EMAIL>',
        type: 'email',
        area: 'Finance',
        responsible: 'Maria Silva',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Contato atualizado com sucesso.',
    type: ContactResponseDto,
  })
  async updateSupplierContact(
    @Param('uuid') _uuid: string,
    @Param('contactUuid') contactUuid: string,
    @Body()
    body: {
      contact: string;
      type: string;
      area: string;
      responsible: string;
    },
  ): Promise<ContactResponseDto> {
    return await this.supplierContactService.updateSupplierContacts(contactUuid, body);
  }

  @Delete(':contactUuid')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Deletar contato do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiParam({
    name: 'contactUuid',
    required: true,
    description: 'Contact UUID',
    type: String,
  })
  @ApiResponse({
    status: 204,
    description: 'Contato deletado com sucesso.',
  })
  async deleteSupplierContact(
    @Param('uuid') _uuid: string,
    @Param('contactUuid') contactUuid: string,
  ): Promise<void> {
    await this.supplierContactService.deleteSupplierContacts(contactUuid);
  }
} 