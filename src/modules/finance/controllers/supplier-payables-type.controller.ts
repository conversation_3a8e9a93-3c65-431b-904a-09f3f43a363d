import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Get,
  Query,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

import { Role } from '@/core/domain/role.enum';
import { PayablesTypeService } from '../services/supplier-payables-type.service';
import { CreatePayablesTypeDto } from '../dto/create-payables-type.dto';
import { PayablesTypeResponseDto } from '../dto/payables-type-response.dto';
import { ListPayablesTypesDto } from '../dto/list-payables-type.dto';
import { PaginatedPayablesTypeResponseDto } from '../dto/paginated-payables-type-response.dto';
import { UpdatePayablesTypeDto } from '../dto/update-payables-type.dto';
import {
  ApiCreatePayablesType,
  ApiDeletePayablesType,
  ApiGetPayablesType,
  ApiListPayablesTypes,
  ApiUpdatePayablesType,
} from '@/infrastructure/swagger/decorators';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

@ApiTags('Payable Types')
@Controller('finance/payables-types')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PayablesTypeController {
  constructor(private readonly payablesTypeService: PayablesTypeService) { }

  @Get()
  @Roles(Role.USER, Role.ADMIN)
  @ApiListPayablesTypes()
  async listPayablesTypes(
    @Query() query: ListPayablesTypesDto,
  ): Promise<PaginatedPayablesTypeResponseDto> {
    return this.payablesTypeService.listPayablesTypes(query);
  }

  @Get(':uuid')
  @Roles(Role.USER, Role.ADMIN)
  @ApiGetPayablesType()
  async listPayablesTypeByUuid(
    @Param('uuid') uuid: string,
  ): Promise<PayablesTypeResponseDto> {
    return this.payablesTypeService.findByUuid(uuid);
  }

  @Post()
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatePayablesType()
  async create(
    @Body() createPayablesTypeDto: CreatePayablesTypeDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<PayablesTypeResponseDto> {
    createPayablesTypeDto.createdBy = req.user.id;
    createPayablesTypeDto.updatedBy = req.user.id;
    return this.payablesTypeService.create(createPayablesTypeDto);
  }

  @Patch(':uuid')
  @Roles(Role.ADMIN)
  @ApiUpdatePayablesType()
  async updatePayablesTypePatch(
    @Param('uuid') uuid: string,
    @Body() dto: UpdatePayablesTypeDto,
  ): Promise<PayablesTypeResponseDto> {
    return this.payablesTypeService.update(uuid, dto);
  }

  @Delete(':uuid')
  @HttpCode(204)
  @Roles(Role.ADMIN)
  @ApiDeletePayablesType()
  async delete(@Param('uuid') uuid: string): Promise<void> {
    return this.payablesTypeService.delete(uuid);
  }
}
