import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  UploadedFiles,
  UseInterceptors,
  Request,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../core/domain/role.enum';
import { SupplierDocumentService } from '../services/supplier-document.service';
import { DocumentResponseDto } from '../../documents/infrastructure/dto/document-response.dto';
import { UploadSupplierDocumentsDto } from '../dto/upload-supplier-documents.dto';
import { Document } from '../../documents/domain/entities/document.entity';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    [key: string]: unknown;
  };
}

@ApiTags('Supplier Documents')
@ApiBearerAuth()
@Controller('core/suppliers/:uuid/documents')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SupplierDocumentController {
  constructor(private readonly supplierDocumentService: SupplierDocumentService) { }

  @Post()
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @UseInterceptors(FilesInterceptor('documents'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Fazer upload de documentos para o fornecedor.',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Fornecedor',
    type: String,
  })
  @ApiBody({
    description:
      "Dados para upload de documentos. 'documents' são os arquivos e 'documentsMetadata' é uma string JSON com os metadados.",
    type: UploadSupplierDocumentsDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Documentos enviados com sucesso.',
    type: [DocumentResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiResponse({ status: 403, description: 'Acesso negado.' })
  async uploadSupplierDocuments(
    @Param('uuid') supplierUuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body('documentsMetadata') documentsMetadata: string,
    @Request() req: AuthenticatedRequest,
  ): Promise<DocumentResponseDto[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    const documents = await this.supplierDocumentService.uploadSupplierDocuments(
      supplierUuid,
      files,
      documentsMetadata,
      req.user.id,
    );
    return documents.map(document => DocumentResponseDto.fromEntity(document));
  }

  @Get()
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER, Role.SUPPLIER)
  @ApiOperation({ summary: 'Listar documentos do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de documentos do fornecedor retornada com sucesso.',
    type: [DocumentResponseDto],
  })
  async listSupplierDocuments(
    @Param('uuid') uuid: string,
  ): Promise<DocumentResponseDto[]> {
    const documents = await this.supplierDocumentService.listSupplierDocuments(uuid);
    return documents.map(document => DocumentResponseDto.fromEntity(document));
  }
} 