import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  HttpCode,
  UploadedFiles,
  UseInterceptors,
  Request,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../core/domain/role.enum';
import { SupplierContractService } from '../services/supplier-contract.service';
import {
  CreateContractsDto,
  ContractApiBodyDto,
  ContractTextFormDataDto,
  ContractUpdatePatchDto,
} from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { ContractResponseDto } from '@/modules/documents/infrastructure/dto/contract-response.dto';
import { Contract } from '@/modules/documents/domain/entities/contract.entity';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    [key: string]: unknown;
  };
}

@ApiTags('Supplier Contracts')
@ApiBearerAuth()
@Controller('core/suppliers/:uuid/contracts')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SupplierContractController {
  constructor(private readonly supplierContractService: SupplierContractService) { }

  @Post()
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Criar contratos para o fornecedor e fazer upload dos arquivos.',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Fornecedor',
    type: String,
  })
  @ApiBody({
    description:
      "Dados para criação de contratos. 'files' são os arquivos e 'contractsMetadata' é uma string JSON com os metadados.",
    type: ContractApiBodyDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Contratos criados com sucesso.',
    type: Array,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiResponse({ status: 403, description: 'Acesso negado.' })
  async createSupplierContracts(
    @Param('uuid') supplierUuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() formData: ContractTextFormDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<Contract[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    let contractsMetadataArray: unknown;
    try {
      contractsMetadataArray = JSON.parse(formData.contractsMetadata);
    } catch {
      throw new Error(
        'O campo contractsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(contractsMetadataArray) ||
      files.length !== contractsMetadataArray.length
    ) {
      throw new Error(
        'O número de metadatos de contrato não corresponde ao número de arquivos enviados.',
      );
    }

    const processedContractsData = (contractsMetadataArray as unknown[]).map(
      (meta) => ({
        ...(meta as Record<string, unknown>),
        entityUuid: supplierUuid,
        uploadedBy: req.user.id,
      }),
    );

    const createContractsDto: CreateContractsDto = {
      contracts: processedContractsData as never,
    };

    return this.supplierContractService.createSupplierContract(
      supplierUuid,
      files,
      req.user.id,
      createContractsDto,
    );
  }

  @Get()
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER, Role.SUPPLIER)
  @ApiOperation({ summary: 'Listar contratos do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de contratos do fornecedor retornada com sucesso.',
    type: [ContractResponseDto],
  })
  async listSupplierContracts(
    @Param('uuid') uuid: string,
  ): Promise<ContractResponseDto[]> {
    const contracts = await this.supplierContractService.listSupplierContracts(uuid);
    return contracts.map((contract) =>
      ContractResponseDto.fromEntity(contract),
    );
  }

  @Get(':contractUuid')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER, Role.SUPPLIER)
  @ApiOperation({ summary: 'Buscar contrato específico do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiParam({
    name: 'contractUuid',
    required: true,
    description: 'Contract UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Contrato do fornecedor retornado com sucesso.',
    type: ContractResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contrato não encontrado para este fornecedor.',
  })
  async getSupplierContract(
    @Param('uuid') supplierUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<ContractResponseDto> {
    const contract = await this.supplierContractService.getSupplierContract(
      supplierUuid,
      contractUuid,
    );
    if (!contract) {
      throw new NotFoundException(
        'Contrato não encontrado para este fornecedor',
      );
    }
    return ContractResponseDto.fromEntity(contract);
  }

  @Patch(':contractUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Atualizar contrato do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Fornecedor',
    type: String,
  })
  @ApiParam({
    name: 'contractUuid',
    required: true,
    description: 'UUID do Contrato',
    type: String,
  })
  @ApiBody({
    description: 'Campos para atualização do contrato (todos opcionais)',
    type: ContractUpdatePatchDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Contrato atualizado com sucesso.',
    type: ContractResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contrato não encontrado para este fornecedor.',
  })
  async patchSupplierContract(
    @Param('uuid') supplierUuid: string,
    @Param('contractUuid') contractUuid: string,
    @Body() patchDto: ContractUpdatePatchDto,
  ): Promise<ContractResponseDto> {
    const contract = await this.supplierContractService.updateSupplierContract(
      supplierUuid,
      contractUuid,
      patchDto,
    );
    return ContractResponseDto.fromEntity(contract);
  }

  @Delete(':contractUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Remover contrato do fornecedor' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Fornecedor',
    type: String,
  })
  @ApiParam({
    name: 'contractUuid',
    required: true,
    description: 'UUID do Contrato',
    type: String,
  })
  @ApiResponse({ status: 204, description: 'Contrato removido com sucesso.' })
  @ApiResponse({
    status: 404,
    description: 'Contrato não encontrado para este fornecedor.',
  })
  @HttpCode(204)
  async deleteSupplierContract(
    @Param('uuid') supplierUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<void> {
    await this.supplierContractService.deleteSupplierContract(
      supplierUuid,
      contractUuid,
    );
  }
}
