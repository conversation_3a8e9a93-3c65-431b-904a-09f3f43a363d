import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsBoolean,
  MaxLength,
  Matches,
} from 'class-validator';
import {
  BankAccountType,
  BankDataStatus,
  BankPixKeyType,
} from '@prisma/client';

export class UpdateBankDataDto {
  @ApiProperty({
    description: 'Bank name',
    example: 'Banco do Brasil S.A.',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Bank name must be a string' })
  @MaxLength(100, {
    message: 'Bank name must be at most 100 characters long',
  })
  bankName?: string;

  @ApiProperty({
    description: 'Bank code (can be numeric or in the format 123-4)',
    example: '001',
    maxLength: 10,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Bank code must be a string' })
  @MaxLength(10, {
    message: 'Bank code must be at most 10 characters long',
  })
  @Matches(/^\d{1,10}$|^\d{1,7}-\d{1,2}$/, {
    message: 'Bank code must contain only numbers or be in the format 123-4',
  })
  bankCode?: string;

  @ApiProperty({
    description: 'Account type',
    enum: BankAccountType,
    example: BankAccountType.CHECKING,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankAccountType, {
    message: 'Account type must be a valid value',
  })
  accountType?: BankAccountType;

  @ApiProperty({
    description: 'Agency number',
    example: '1234',
    maxLength: 10,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Agency number must be a string' })
  @MaxLength(10, {
    message: 'Agency number must be at most 10 characters long',
  })
  @Matches(/^\d{1,10}$/, {
    message: 'Agency number must contain only numbers',
  })
  agencyNumber?: string;

  @ApiProperty({
    description: 'Agency digit',
    example: '5',
    maxLength: 5,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Agency digit must be a string' })
  @MaxLength(5, {
    message: 'Agency digit must be at most 5 characters long',
  })
  @Matches(/^\d{0,5}$/, {
    message: 'Agency digit must contain only numbers',
  })
  agencyDigit?: string;

  @ApiProperty({
    description: 'Account number',
    example: '123456',
    maxLength: 10,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account number must be a string' })
  @MaxLength(10, {
    message: 'Account number must be at most 10 characters long',
  })
  @Matches(/^\d{1,10}$/, {
    message: 'Account number must contain only numbers',
  })
  accountNumber?: string;

  @ApiProperty({
    description: 'Account digit',
    example: '7',
    maxLength: 5,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account digit must be a string' })
  @MaxLength(5, { message: 'Account digit must be at most 5 characters long' })
  @Matches(/^\d{1,5}$/, {
    message: 'Account digit must contain only numbers',
  })
  accountDigit?: string;

  @ApiProperty({
    description: 'Account holder name',
    example: 'João da Silva',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account holder name must be a string' })
  @MaxLength(100, {
    message: 'Account holder name must be at most 100 characters long',
  })
  accountHolderName?: string;

  @ApiProperty({
    description: 'Account holder document (CPF/CNPJ)',
    example: '***********',
    maxLength: 14,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account holder document must be a string' })
  @MaxLength(14, {
    message: 'Account holder document must be at most 14 characters long',
  })
  @Matches(/^\d{11,14}$/, {
    message:
      'Account holder document must contain only numbers and be between 11 and 14 digits long',
  })
  accountHolderDocument?: string;

  @ApiProperty({
    description: 'PIX key',
    example: '<EMAIL>',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'PIX key must be a string' })
  @MaxLength(100, { message: 'PIX key must be at most 100 characters long' })
  pixKey?: string;

  @ApiProperty({
    description: 'PIX key type',
    enum: BankPixKeyType,
    example: BankPixKeyType.EMAIL,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankPixKeyType, {
    message: 'PIX key type must be a valid value',
  })
  pixKeyType?: BankPixKeyType;

  @ApiProperty({
    description: 'Indicates if it is a digital bank',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({
    message: 'Digital bank indicator must be a boolean value',
  })
  isDigitalBank?: boolean;

  @ApiProperty({
    description: 'Status must be ACTIVE or INACTIVE',
    enum: BankDataStatus,
    required: false,
    example: BankDataStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(BankDataStatus, {
    message: 'Status must be a valid enum value',
  })
  status?: BankDataStatus;
}
