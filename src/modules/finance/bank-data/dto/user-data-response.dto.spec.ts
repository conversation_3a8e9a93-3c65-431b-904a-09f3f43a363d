import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  UserDataResponseDto,
  EmployeeDataDto,
  SupplierDataDto,
  CustomerDataDto,
} from './user-data-response.dto';

describe('UserDataResponseDto', () => {
  describe('Valid data', () => {
    it('should validate with all required fields', async () => {
      const dto = plainToClass(UserDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'ADMIN',
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with employee data', async () => {
      const dto = plainToClass(UserDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        email: '<EMAIL>',
        name: '<PERSON> <PERSON>e',
        role: 'EMPLOYEE',
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        employee: {
          id: 123,
          name: '<PERSON> <PERSON>e',
          document: '12345678901',
          status: 'ACTIVE',
          createdAt: new Date('2023-01-01T00:00:00.000Z'),
          updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with supplier data', async () => {
      const dto = plainToClass(UserDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        email: '<EMAIL>',
        name: 'Supplier User',
        role: 'SUPPLIER',
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        supplier: {
          id: 456,
          name: 'ABC Company',
          document: '12345678000195',
          status: 'ACTIVE',
          createdAt: new Date('2023-01-01T00:00:00.000Z'),
          updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with customer data', async () => {
      const dto = plainToClass(UserDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        email: '<EMAIL>',
        name: 'Customer User',
        role: 'CUSTOMER',
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        customer: {
          id: 789,
          name: 'XYZ Corporation',
          document: '98765432000123',
          status: 'ACTIVE',
          createdAt: new Date('2023-01-01T00:00:00.000Z'),
          updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('Data transformation', () => {
    it('should preserve all field values correctly', () => {
      const inputData = {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        email: '<EMAIL>',
        name: 'John Doe',
        role: 'ADMIN',
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        employee: {
          id: 123,
          name: 'John Doe',
          document: '12345678901',
          status: 'ACTIVE',
          createdAt: new Date('2023-01-01T00:00:00.000Z'),
          updatedAt: new Date('2023-01-01T00:00:00.000Z'),
        },
      };

      const dto = plainToClass(UserDataResponseDto, inputData);

      expect(dto.id).toBe(inputData.id);
      expect(dto.email).toBe(inputData.email);
      expect(dto.name).toBe(inputData.name);
      expect(dto.role).toBe(inputData.role);
      expect(dto.createdAt).toEqual(inputData.createdAt);
      expect(dto.updatedAt).toEqual(inputData.updatedAt);
      expect(dto.employee?.id).toBe(inputData.employee.id);
      expect(dto.employee?.name).toBe(inputData.employee.name);
    });
  });
});

describe('EmployeeDataDto', () => {
  it('should validate with all required fields', async () => {
    const dto = plainToClass(EmployeeDataDto, {
      id: 123,
      name: 'John Doe',
      document: '12345678901',
      status: 'ACTIVE',
      createdAt: new Date('2023-01-01T00:00:00.000Z'),
      updatedAt: new Date('2023-01-01T00:00:00.000Z'),
    });

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });
});

describe('SupplierDataDto', () => {
  it('should validate with all required fields', async () => {
    const dto = plainToClass(SupplierDataDto, {
      id: 456,
      name: 'ABC Company',
      document: '12345678000195',
      status: 'ACTIVE',
      createdAt: new Date('2023-01-01T00:00:00.000Z'),
      updatedAt: new Date('2023-01-01T00:00:00.000Z'),
    });

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });
});

describe('CustomerDataDto', () => {
  it('should validate with all required fields', async () => {
    const dto = plainToClass(CustomerDataDto, {
      id: 789,
      name: 'XYZ Corporation',
      document: '98765432000123',
      status: 'ACTIVE',
      createdAt: new Date('2023-01-01T00:00:00.000Z'),
      updatedAt: new Date('2023-01-01T00:00:00.000Z'),
    });

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });
});
