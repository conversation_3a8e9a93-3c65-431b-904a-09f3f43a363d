import { Test, TestingModule } from '@nestjs/testing';
import { HealthService } from './health.service';
import * as amqplib from 'amqplib';
import { PrismaService } from '../../infrastructure/prisma/prisma.service';

describe('HealthService', () => {
  let service: HealthService;

  const mockPrisma = {
    $queryRaw: jest.fn(),
  };

  const mockChannelModel = {
    close: jest.fn(),
    createChannel: jest.fn(),
    createConfirmChannel: jest.fn(),
    connection: {} as unknown as amqplib.Connection,
    updateSecret: jest.fn(),
  };

  afterEach(() => {
    jest.restoreAllMocks();
    jest.clearAllMocks();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: PrismaService,
          useValue: mockPrisma,
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return true if the app is ready (DB + RabbitMQ ok)', async () => {
    mockPrisma.$queryRaw.mockResolvedValueOnce(undefined);
    jest
      .spyOn(amqplib, 'connect')
      .mockResolvedValueOnce(
        mockChannelModel as unknown as Promise<amqplib.ChannelModel>,
      );

    const result = await service.isReady();
    expect(result).toBe(true);
    expect(mockPrisma.$queryRaw).toHaveBeenCalled();
    expect(mockChannelModel.close).toHaveBeenCalled();
  });

  it('should return false if the DB is not reachable', async () => {
    mockPrisma.$queryRaw.mockRejectedValueOnce(new Error('DB error'));
    jest
      .spyOn(amqplib, 'connect')
      .mockResolvedValueOnce(
        mockChannelModel as unknown as Promise<amqplib.ChannelModel>,
      );

    const result = await service.isReady();
    expect(result).toBe(false);
    expect(mockPrisma.$queryRaw).toHaveBeenCalled();
  });

  it('should return false if RabbitMQ is not reachable', async () => {
    mockPrisma.$queryRaw.mockResolvedValueOnce(undefined);

    jest
      .spyOn(amqplib, 'connect')
      .mockRejectedValueOnce(new Error('RabbitMQ error'));

    const result = await service.isReady();
    expect(result).toBe(false);
    expect(mockPrisma.$queryRaw).toHaveBeenCalled();
  });
});
