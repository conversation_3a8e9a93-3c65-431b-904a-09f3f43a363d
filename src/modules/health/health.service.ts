import { Injectable } from '@nestjs/common';
import * as amqplib from 'amqplib';
import { PrismaService } from '../../infrastructure/prisma/prisma.service';

@Injectable()
export class HealthService {
  constructor(private readonly prisma: PrismaService) {}

  async isReady(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;

      const conn = await amqplib.connect('amqp://guest:guest@rabbitmq:5672');
      await conn.close();

      return true;
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Health check failed:', error.message);
      } else {
        console.error('Health check failed:', error);
      }
      return false;
    }
  }
}
