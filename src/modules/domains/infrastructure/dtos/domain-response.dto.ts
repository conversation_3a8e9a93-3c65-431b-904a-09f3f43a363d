import { ApiProperty } from '@nestjs/swagger';
import { Domain } from '../../domain/entities/domain.entity';
import { LicenseType } from '@prisma/client';

export class DomainResponseDto {
  @ApiProperty({ description: 'UUID do domínio', format: 'uuid' })
  uuid: string;

  @ApiProperty({ description: 'Nome do domínio' })
  domain: string;

  @ApiProperty({ description: 'Nome da marca' })
  brandName: string;

  @ApiProperty({ description: 'Notas' })
  notes: string | null;

  @ApiProperty({ description: 'Número da licença' })
  licenseNumber: string;

  @ApiProperty({ description: 'Tipo de licença', enum: LicenseType })
  licenseType: LicenseType;

  @ApiProperty({ description: 'Data de criação', format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização', format: 'date-time' })
  updatedAt: Date;

  @ApiProperty({ description: 'Data de exclusão (soft delete)', format: 'date-time', nullable: true })
  deletedAt: Date | null;

  static fromEntity(domain: Domain): DomainResponseDto {
    const dto = new DomainResponseDto();
    dto.uuid = domain.uuid;
    dto.domain = domain.domain;
    dto.brandName = domain.brandName;
    dto.notes = domain.notes ?? null;
    dto.licenseNumber = domain.licenseNumber;
    dto.licenseType = domain.licenseType;
    dto.createdAt = domain.createdAt;
    dto.updatedAt = domain.updatedAt;
    dto.deletedAt = domain.deletedAt;
    return dto;
  }
} 