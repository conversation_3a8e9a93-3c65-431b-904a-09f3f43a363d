import { IsString, IsEnum, IsOptional, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LicenseType } from '@prisma/client';

export class DomainUpdateDto {
  @ApiProperty({
    description: 'Domain name',
    example: 'example.com',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, {
    message: 'Invalid domain format',
  })
  domain?: string;

  @ApiProperty({
    description: 'Brand name',
    example: 'Brand Name',
    required: false,
  })
  @IsString()
  @IsOptional()
  brandName?: string;

  @ApiProperty({
    description: 'Notes',
    example: 'Notes',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'License number',
    example: '**********',
    required: false,
  })
  @IsString()
  @IsOptional()
  licenseNumber?: string;

  @ApiProperty({
    description: 'License type',
    example: LicenseType.ESTADUAL,
    required: false,
  })
  @IsEnum(LicenseType)
  @IsOptional()
  licenseType?: LicenseType;
} 