import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { Domain } from '../../domain/entities/domain.entity';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';

@Injectable()
export class DomainRepository implements IDomainRepository {
  constructor(private readonly prisma: PrismaService) { }

  async create(domain: Domain): Promise<Domain> {
    const data: Prisma.DomainCreateInput = {
      uuid: domain.uuid,
      customer: { connect: { uuid: domain.customerUuid } },
      domain: domain.domain,
      brandName: domain.brandName,
      notes: domain.notes,
      licenseNumber: domain.licenseNumber,
      licenseType: domain.licenseType,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt,
      creator: { connect: { id: domain.createdBy } },
      updater: { connect: { id: domain.updatedBy } },
    };
    const result = await this.prisma.domain.create({ data });
    return this.mapToDomain(result);
  }

  async update(uuid: string, domain: Partial<Domain>): Promise<Domain> {
    const data: Prisma.DomainUpdateInput = {
      brandName: domain.brandName,
      licenseNumber: domain.licenseNumber,
      licenseType: domain.licenseType,
      notes: domain.notes,
      domain: domain.domain,
      updatedAt: new Date(),
      updater: domain.updatedBy ? { connect: { id: domain.updatedBy } } : undefined,
    };
    const result = await this.prisma.domain.update({
      where: { uuid },
      data,
    });
    return this.mapToDomain(result);
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.domain.update({
      where: { uuid },
      data: {
        deletedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  async findByUuid(uuid: string): Promise<Domain | null> {
    const result = await this.prisma.domain.findFirst({
      where: {
        uuid,
        deletedAt: null,
      },
    });
    return result ? this.mapToDomain(result) : null;
  }

  async findByCustomerUuid(
    customerUuid: string,
    params: {
      limit?: number;
      offset?: number;
      domain?: string;
    },
  ): Promise<{ items: Domain[]; total: number }> {
    const where: Prisma.DomainWhereInput = {
      customerUuid,
      deletedAt: null,
      ...(params.domain && { domain: { contains: params.domain } }),
    };

    const [items, total] = await Promise.all([
      this.prisma.domain.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.domain.count({ where }),
    ]);

    return { items: items.map(item => this.mapToDomain(item)), total };
  }

  async findByDomain(
    customerUuid: string,
    domain: string,
  ): Promise<Domain | null> {
    const result = await this.prisma.domain.findFirst({
      where: {
        customerUuid,
        domain,
        deletedAt: null,
      },
    });
    return result ? this.mapToDomain(result) : null;
  }

  private mapToDomain(prismaResult: any): Domain {
    return {
      ...prismaResult,
      notes: prismaResult.notes ?? undefined,
    };
  }
} 