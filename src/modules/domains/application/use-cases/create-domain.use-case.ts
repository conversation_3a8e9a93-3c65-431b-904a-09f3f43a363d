import { Injectable, Inject, ConflictException, NotFoundException } from '@nestjs/common';
import { Domain } from '../../domain/entities/domain.entity';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';
import { CreateDomainDto } from '../../infrastructure/dtos/domain-create.dto';
import { v4 as uuidv4 } from 'uuid';

export interface CreateDomainInput {
  customerUuid: string;
  domain: CreateDomainDto;
  createdBy: string;
}

@Injectable()
export class CreateDomainUseCase {
  constructor(
    @Inject('IDomainRepository')
    private readonly domainRepository: IDomainRepository,
  ) {}

  async execute(input: CreateDomainInput): Promise<Domain> {
    const existingDomain = await this.domainRepository.findByDomain(
      input.customerUuid,
      input.domain.domain,
    );

    if (existingDomain) {
      throw new ConflictException(
        `Domain ${input.domain.domain} already exists for this customer`,
      );
    }

    const customerDomains = await this.domainRepository.findByCustomerUuid(input.customerUuid, {
      limit: 3,
      offset: 0,
    });

    if (customerDomains.total >= 3) {
      throw new ConflictException(
        `Customer ${input.customerUuid} already has 3 domains`,
      );
    }

    const domain = {
      uuid: uuidv4(),
      customerUuid: input.customerUuid,
      domain: input.domain.domain,
      brandName: input.domain.brandName,
      notes: input.domain.notes,
      licenseNumber: input.domain.licenseNumber,
      licenseType: input.domain.licenseType,
      createdBy: input.createdBy,
      updatedBy: input.createdBy,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Domain;

    const createdDomain = await this.domainRepository.create(domain);

    return createdDomain;
  }
} 