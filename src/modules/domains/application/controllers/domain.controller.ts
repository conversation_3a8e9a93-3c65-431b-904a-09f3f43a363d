import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { CreateDomainUseCase } from '../use-cases/create-domain.use-case';
import { FindDomainByUuidUseCase } from '../use-cases/find-domain-by-uuid.use-case';
import { ListDomainsUseCase } from '../use-cases/list-domains.use-case';
import { UpdateDomainUseCase } from '../use-cases/update-domain.use-case';
import { DeleteDomainUseCase } from '../use-cases/delete-domain.use-case';
import { CreateDomainDto } from '../../infrastructure/dtos/domain-create.dto';
import { DomainUpdateDto } from '../../infrastructure/dtos/domain-update.dto';
import { DomainResponseDto } from '../../infrastructure/dtos/domain-response.dto';
import { DomainListDto } from '../../infrastructure/dtos/domain-list.dto';
import { RolesGuard } from '../../../auth/guards/roles.guard';
import { Roles } from '../../../auth/decorators/roles.decorator';
import { Role } from '@/core/enums/role.enum';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { User } from '@/modules/auth/decorators/user.decorator';
import { UsersService } from '@/modules/users/users.service';

@ApiTags('Domains')
@Controller('customers/:customerUuid/domains')
@UseGuards(JwtAuthGuard, RolesGuard)
export class DomainController {
  constructor(
    private readonly createDomainUseCase: CreateDomainUseCase,
    private readonly findDomainByUuidUseCase: FindDomainByUuidUseCase,
    private readonly listDomainsUseCase: ListDomainsUseCase,
    private readonly updateDomainUseCase: UpdateDomainUseCase,
    private readonly deleteDomainUseCase: DeleteDomainUseCase,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Criar um novo domínio para um cliente' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Domínio criado com sucesso',
    type: DomainResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Domínio já existente',
  })
  async create(
    @Param('customerUuid') customerUuid: string,
    @Body() createDomainDto: CreateDomainDto,
    @User() keycloakId: string,
  ): Promise<DomainResponseDto> {
    // Fetch user by keycloakId from JWT token
    const user = await this.usersService.findByKeycloakId(keycloakId);
    
    const domain = await this.createDomainUseCase.execute({
      customerUuid,
      domain: createDomainDto,
      createdBy: user.id,
    });
    return DomainResponseDto.fromEntity(domain);
  }

  @Get()
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Listar domínios de um cliente com filtros opcionais' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lista de domínios retornada com sucesso',
    type: DomainListDto,
  })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiQuery({ name: 'domain', required: false, type: String })
  @ApiQuery({ name: 'type', required: false, enum: ['primary', 'secondary'] })
  async list(
    @Param('customerUuid') customerUuid: string,
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
    @Query('domain') domain?: string,
  ): Promise<DomainListDto> {
    const result = await this.listDomainsUseCase.execute(
      customerUuid,
      { domain},
      limit,
      offset,
    );
    return {
      items: result.items.map(item => DomainResponseDto.fromEntity(item)),
      limit: result.limit,
      offset: result.offset,
      total: result.total,
    };
  }

  @Get(':domainUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Buscar um domínio específico de um cliente' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Domínio encontrado com sucesso',
    type: DomainResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Domínio não encontrado',
  })
  async findByUuid(
    @Param('customerUuid') customerUuid: string,
    @Param('domainUuid') domainUuid: string,
  ): Promise<DomainResponseDto> {
    const domain = await this.findDomainByUuidUseCase.execute(domainUuid);
    return DomainResponseDto.fromEntity(domain);
  }

  @Patch(':domainUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Atualizar parcialmente um domínio' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Domínio atualizado com sucesso',
    type: DomainResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Domínio não encontrado',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Domínio já existente',
  })
  async update(
    @Param('customerUuid') customerUuid: string,
    @Param('domainUuid') domainUuid: string,
    @Body() updateDomainDto: DomainUpdateDto,
    @User() keycloakId: string,
  ): Promise<DomainResponseDto> {
    // Fetch user by keycloakId from JWT token
    const user = await this.usersService.findByKeycloakId(keycloakId);
    
    const domain = await this.updateDomainUseCase.execute({
      customerUuid,
      uuid: domainUuid,
      data: updateDomainDto,
      updatedBy: user.id,
    });
    return DomainResponseDto.fromEntity(domain);
  }

  @Delete(':domainUuid')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Excluir (soft delete) um domínio' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Domínio excluído (soft delete) com sucesso',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Domínio não encontrado',
  })
  async delete(
    @Param('customerUuid') customerUuid: string,
    @Param('domainUuid') domainUuid: string,
  ): Promise<void> {
    await this.deleteDomainUseCase.execute(domainUuid);
  }
}