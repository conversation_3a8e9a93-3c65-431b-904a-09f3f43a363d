import { Domain } from '../entities/domain.entity';

export interface IDomainRepository {
  create(domain: Domain): Promise<Domain>;
  update(uuid: string, domain: Partial<Domain>): Promise<Domain>;
  delete(uuid: string): Promise<void>;
  findByUuid(uuid: string): Promise<Domain | null>;
  findByCustomerUuid(customerUuid: string, params: {
    limit?: number;
    offset?: number;
    domain?: string;
  }): Promise<{ items: Domain[]; total: number }>;
  findByDomain(customerUuid: string, domain: string): Promise<Domain | null>;
} 