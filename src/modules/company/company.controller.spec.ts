import { Test, TestingModule } from '@nestjs/testing';
import { CompanyController } from './company.controller';
import { CreateCompanyUseCase } from '../../core/application/use-cases/company/create-company.use-case';
import { UpdateCompanyUseCase } from '../../core/application/use-cases/company/update-company.use-case';
import { DeleteCompanyUseCase } from '../../core/application/use-cases/company/delete-company.use-case';
import { Company, CompanyStatus } from '../../core/domain/company.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ConfigService } from '@nestjs/config';
import { NotFoundException } from '@nestjs/common';
import { CompanyService } from '../finance/services/supplier-company.service';

interface CompanyAddress {
  street: string;
  city: string;
  zipCode: string;
  state: string;
}

interface CompanyOutput {
  id: number;
  uuid: string;
  razaoSocial: string;
  cnpj: string;
  address: CompanyAddress;
  phone: string;
  email: string;
  status: CompanyStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

describe('CompanyController', () => {
  let controller: CompanyController;
  let createCompanyUseCase: jest.Mocked<CreateCompanyUseCase>;
  let updateCompanyUseCase: jest.Mocked<UpdateCompanyUseCase>;
  let deleteCompanyUseCase: jest.Mocked<DeleteCompanyUseCase>;
  let companyService: jest.Mocked<CompanyService>;

  beforeEach(async () => {
    createCompanyUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<CreateCompanyUseCase>;

    updateCompanyUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<UpdateCompanyUseCase>;

    deleteCompanyUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<DeleteCompanyUseCase>;

    companyService = {
      getCompanyByUuid: jest.fn(),
      listCompanies: jest.fn(),
    } as unknown as jest.Mocked<CompanyService>;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CompanyController],
      providers: [
        {
          provide: CreateCompanyUseCase,
          useValue: createCompanyUseCase,
        },
        {
          provide: UpdateCompanyUseCase,
          useValue: updateCompanyUseCase,
        },
        {
          provide: DeleteCompanyUseCase,
          useValue: deleteCompanyUseCase,
        },
        {
          provide: CompanyService,
          useValue: companyService,
        },
        {
          provide: JwtAuthGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
        {
          provide: ConfigService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<CompanyController>(CompanyController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a company successfully', async () => {
    const createCompanyDto = {
      razaoSocial: 'Acme Ltda',
      cnpj: '12345678000195',
      address: {
        street: 'Av. Paulista, 1000',
        city: 'São Paulo',
        zipCode: '01310-100',
        state: 'SP',
      } as CompanyAddress,
      phone: '(11) 98765-4321',
      email: '<EMAIL>',
      status: CompanyStatus.ACTIVE,
    };

    const mockCompany = new Company(
      1,
      '550e8400-e29b-41d4-a716-************',
      createCompanyDto.razaoSocial,
      createCompanyDto.cnpj,
      createCompanyDto.address,
      createCompanyDto.phone,
      createCompanyDto.email,
      createCompanyDto.status,
      'admin-user',
    );

    const mockCompanyOutput: CompanyOutput = {
      id: mockCompany.id,
      uuid: mockCompany.uuid,
      razaoSocial: mockCompany.razaoSocial,
      cnpj: mockCompany.cnpj,
      address: {
        street: mockCompany.address.street,
        city: mockCompany.address.city,
        zipCode: mockCompany.address.zipCode,
        state: mockCompany.address.state,
      },
      phone: mockCompany.phone,
      email: mockCompany.email,
      status: mockCompany.status,
      createdAt: mockCompany.createdAt,
      updatedAt: mockCompany.updatedAt,
      createdBy: mockCompany.createdBy,
      updatedBy: mockCompany.updatedBy,
    };

    createCompanyUseCase.execute.mockResolvedValue({
      company: mockCompanyOutput,
    });

    const result = await controller.createCompany(
      createCompanyDto,
      'admin-user',
    );
    expect(result).toEqual(mockCompanyOutput);
  });

  it('should get company by UUID successfully', async () => {
    const uuid = '550e8400-e29b-41d4-a716-************';
    const mockCompanyOutput = {
      id: 1,
      uuid,
      razaoSocial: 'Acme Ltda',
      cnpj: '12345678000195',
      address: {
        street: 'Av. Paulista, 1000',
        city: 'São Paulo',
        zipCode: '01310-100',
        state: 'SP',
      },
      phone: '(11) 98765-4321',
      email: '<EMAIL>',
      status: CompanyStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin-user',
      updatedBy: 'admin-user',
    };

    const mockFn = jest.fn().mockResolvedValue(mockCompanyOutput);
    companyService.getCompanyByUuid = mockFn;

    const result = await controller.getCompanyByUuid(uuid);
    expect(result).toEqual(mockCompanyOutput);
    expect(mockFn).toHaveBeenCalledWith(uuid);
  });

  it('should list companies successfully', async () => {
    const filters = { limit: 10, offset: 0 };
    const mockCompanies = {
      items: [
        {
          id: 1,
          uuid: '550e8400-e29b-41d4-a716-************',
          razaoSocial: 'Acme Ltda',
          cnpj: '12345678000195',
          address: {
            street: 'Av. Paulista, 1000',
            city: 'São Paulo',
            zipCode: '01310-100',
            state: 'SP',
          },
          phone: '(11) 98765-4321',
          email: '<EMAIL>',
          status: CompanyStatus.ACTIVE,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin-user',
          updatedBy: 'admin-user',
        },
      ],
      total: 1,
      limit: 10,
      offset: 0,
    };

    const mockFn = jest.fn().mockResolvedValue(mockCompanies);
    companyService.listCompanies = mockFn;

    const result = await controller.listCompanies(filters);
    expect(result).toEqual(mockCompanies);
    expect(mockFn).toHaveBeenCalledWith(filters);
  });

  it('should update a company successfully', async () => {
    const updateCompanyDto = {
      phone: '(11) 99999-0000',
      status: CompanyStatus.INACTIVE,
    };

    const mockAddress: CompanyAddress = {
      street: 'Av. Paulista, 1000',
      city: 'São Paulo',
      zipCode: '01310-100',
      state: 'SP',
    };

    const mockCompany = new Company(
      1,
      '550e8400-e29b-41d4-a716-************',
      'Acme Ltda',
      '12345678000195',
      mockAddress,
      updateCompanyDto.phone,
      '<EMAIL>',
      updateCompanyDto.status,
      'admin-user',
    );

    const mockCompanyOutput: CompanyOutput = {
      id: mockCompany.id,
      uuid: mockCompany.uuid,
      razaoSocial: mockCompany.razaoSocial,
      cnpj: mockCompany.cnpj,
      address: {
        street: mockCompany.address.street,
        city: mockCompany.address.city,
        zipCode: mockCompany.address.zipCode,
        state: mockCompany.address.state,
      },
      phone: mockCompany.phone,
      email: mockCompany.email,
      status: mockCompany.status,
      createdAt: mockCompany.createdAt,
      updatedAt: mockCompany.updatedAt,
      createdBy: mockCompany.createdBy,
      updatedBy: mockCompany.updatedBy,
    };

    updateCompanyUseCase.execute.mockResolvedValue({ company: mockCompany });

    const result = await controller.updateCompany(
      '550e8400-e29b-41d4-a716-************',
      updateCompanyDto,
      'admin-user',
    );

    expect(result).toEqual(mockCompanyOutput);
  });

  describe('deleteCompany', () => {
    it('should delete a company successfully', async () => {
      // Mock a function to avoid unbound-method issues
      const mockExecute = jest.fn().mockResolvedValue(undefined);
      deleteCompanyUseCase.execute = mockExecute;

      const uuid = '550e8400-e29b-41d4-a716-************';
      await controller.deleteCompany(uuid);

      expect(mockExecute).toHaveBeenCalledWith({ uuid });
    });

    it('should throw NotFoundException when company does not exist', async () => {
      // Mock a function to avoid unbound-method issues
      const mockExecute = jest
        .fn()
        .mockRejectedValue(new NotFoundException('Empresa não encontrada.'));
      deleteCompanyUseCase.execute = mockExecute;

      const uuid = 'non-existent-uuid';
      await expect(controller.deleteCompany(uuid)).rejects.toThrow(
        NotFoundException,
      );

      expect(mockExecute).toHaveBeenCalledWith({ uuid });
    });

    it('should handle errors during company deletion', async () => {
      const error = new Error('Erro ao deletar empresa');

      // Mock a function to avoid unbound-method issues
      const mockExecute = jest.fn().mockRejectedValue(error);
      deleteCompanyUseCase.execute = mockExecute;

      const uuid = '550e8400-e29b-41d4-a716-************';
      await expect(controller.deleteCompany(uuid)).rejects.toThrow(error);

      expect(mockExecute).toHaveBeenCalledWith({ uuid });
    });
  });
});
