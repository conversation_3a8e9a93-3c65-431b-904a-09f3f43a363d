import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  Length,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CompanyStatus } from '../../../core/domain/company.entity';

class CompanyAddressDto {
  @ApiProperty({ example: 'Av. Paulista, 1000' })
  @IsString()
  @Length(1, 100)
  street: string;

  @ApiProperty({ example: 'São Paulo' })
  @IsString()
  @Length(1, 50)
  city: string;

  @ApiProperty({ example: '01310-100' })
  @IsString()
  @Length(8, 9)
  zipCode: string;

  @ApiProperty({ example: 'SP' })
  @IsString()
  @Length(2, 2)
  state: string;
}

export class UpdateCompanyDto {
  @ApiProperty({ example: 'Acme Ltda', required: false })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  razaoSocial?: string;

  @ApiProperty({ example: '12345678000195', required: false })
  @IsOptional()
  @IsString()
  @Length(14, 14)
  cnpj?: string;

  @ApiProperty({ type: CompanyAddressDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => CompanyAddressDto)
  address?: CompanyAddressDto;

  @ApiProperty({ example: '(11) 98765-4321', required: false })
  @IsOptional()
  @IsString()
  @Length(10, 15)
  phone?: string;

  @ApiProperty({ example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  @Length(1, 255)
  email?: string;

  @ApiProperty({ enum: CompanyStatus, required: false })
  @IsOptional()
  @IsEnum(CompanyStatus)
  status?: CompanyStatus;
}
