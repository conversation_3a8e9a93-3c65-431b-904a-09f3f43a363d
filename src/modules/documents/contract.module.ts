import { AwsModule } from "@/infrastructure/aws/aws.module";
import { PrismaModule } from "@/infrastructure/prisma/prisma.module";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { MulterModule } from "@nestjs/platform-express";
import { CreateContractUseCase } from "./application/use-cases/create-contract.use-case";
import { ListContractUseCase } from "./application/use-cases/list-contract.use-case";
import { DownloadContractUseCase } from "./application/use-cases/download-contract.use-case";
import { DeleteContractUseCase } from "./application/use-cases/delete-contract.use-case";
import { UpdateContractUseCase } from "./application/use-cases/update-contract.use-case";
import { ContractRepository } from "./infrastructure/repositories/contract.repository";
import { S3StorageProvider } from "@/infrastructure/aws/s3/s3-storage.provider";


@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    AwsModule,
    MulterModule.register({
      limits: {
        fileSize: 20 * 1024 * 1024,
      },
    }),
  ],
  providers: [
    CreateContractUseCase,
    ListContractUseCase,
    DownloadContractUseCase,
    DeleteContractUseCase,
    UpdateContractUseCase,
    {
      provide: 'IContractRepository',
      useClass: ContractRepository,
    },
    {
      provide: 'IStorageProvider',
      useExisting: S3StorageProvider,
    },
  ],
  exports: ['IContractRepository', 'IStorageProvider', CreateContractUseCase, ListContractUseCase, DownloadContractUseCase, DeleteContractUseCase, UpdateContractUseCase],
})
export class ContractModule { }
