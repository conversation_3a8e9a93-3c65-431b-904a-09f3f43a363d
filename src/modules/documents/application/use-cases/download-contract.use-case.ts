import { Inject, NotFoundException } from '@nestjs/common';
import { IContractRepository } from '../../domain/repositories/contract.repository.interface';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';

export class DownloadContractUseCase {
  constructor(
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(
    uuid: string,
    versionId?: number,
  ): Promise<{ url: string; fileName: string }> {
    const contract = await this.contractRepository.findByUuid(uuid);
    if (!contract) throw new NotFoundException('Contrato não encontrado');

    if (!contract.versions) {
      throw new NotFoundException('Contrato não possui versões');
    }

    const version =
      versionId != null
        ? contract.versions.find((v) => v.versionId === versionId)
        : contract.versions.find((v) => v.versionId === contract.currentVersion);

    if (!version)
      throw new NotFoundException('Versão do contrato não encontrada');

    const fileName = version.filePath.split('/').pop() || 'contract.pdf';
    const url = await this.storageProvider.getDownloadUrl(version.filePath, fileName); 

    return { url, fileName };
  }

  async executeMultiple(
    contracts: Array<{ uuid: string; versions?: Array<{ versionId: number; filePath: string }> }>,
  ): Promise<{ [contractUuid: string]: { [versionId: number]: { url: string; fileName: string } } }> {
    const result: { [contractUuid: string]: { [versionId: number]: { url: string; fileName: string } } } = {};

    for (const contract of contracts) {
      if (!contract.versions) continue;

      result[contract.uuid] = {};
      
      for (const version of contract.versions) {
        try {
          const fileName = version.filePath.split('/').pop() || 'contract.pdf';
          const url = await this.storageProvider.getDownloadUrl(version.filePath, fileName);
          
          result[contract.uuid][version.versionId] = { url, fileName };
        } catch (error) {
          console.error(`Erro ao gerar URL para contrato ${contract.uuid}, versão ${version.versionId}:`, error);
        }
      }
    }

    return result;
  }
} 