import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { Document } from '../../domain/entities/document.entity';

@Injectable()
export class FindDocumentByUuidUseCase {
  constructor(
    @Inject('IDocumentRepository')
    private readonly documentRepository: IDocumentRepository,
  ) {}

  async execute(uuid: string): Promise<Document> {
    const document = await this.documentRepository.findByUuid(uuid);

    if (!document) {
      throw new NotFoundException(`Documento com UUID ${uuid} não encontrado`);
    }

    return document;
  }
}
