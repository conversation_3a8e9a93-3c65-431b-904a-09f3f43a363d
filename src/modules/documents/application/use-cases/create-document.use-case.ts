import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { CreateDocumentDto } from '../../infrastructure/dto/create-document.dto';
import { Document } from '../../domain/entities/document.entity';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { DocumentStatus } from '../../domain/enums/document-status.enum';

@Injectable()
export class CreateDocumentUseCase {
  constructor(
    @Inject('IDocumentRepository')
    private readonly documentRepository: IDocumentRepository,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(
    dto: CreateDocumentDto,
    file: Express.Multer.File,
  ): Promise<Document> {
    if (!file) {
      throw new BadRequestException('Arquivo é obrigatório');
    }

    const uuid = uuidv4();
    const versionId = 1;
    const key = `documents/${uuid}/v${versionId}/${file.originalname}`;

    try {
      await this.storageProvider.upload(file.buffer, file.mimetype, key);

      const document: Document = {
        uuid,
        entityType: dto.entityType,
        entityUuid: dto.entityUuid,
        currentVersion: versionId,
        uploadedBy: dto.uploadedBy,
        status: DocumentStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
        responsible: dto.responsible,
        department: dto.department,
        description: dto.description,
        versions: [
          {
            versionId,
            filePath: key,
            uploadedAt: new Date(),
            uploadedBy: dto.uploadedBy,
            expirationDate: dto.expirationDate
              ? new Date(dto.expirationDate)
              : undefined,
            createdAt: new Date(),
          },
        ],
      };
      const documentCreated = await this.documentRepository.create(document);

      return documentCreated;
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException('Erro ao criar documento');
    }
  }
}
