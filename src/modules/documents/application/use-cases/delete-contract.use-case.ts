import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IContractRepository } from '../../domain/repositories/contract.repository.interface';

@Injectable()
export class DeleteContractUseCase {
  constructor(
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,
  ) { }

  async execute(contractUuid: string): Promise<void> {
    // Opcional: checar se existe antes de deletar
    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract) {
      throw new NotFoundException('Contrato não encontrado');
    }
    await this.contractRepository.delete(contractUuid);
  }
} 