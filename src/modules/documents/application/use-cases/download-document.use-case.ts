import { Inject, NotFoundException } from '@nestjs/common';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';

export class DownloadDocumentUseCase {
  constructor(
    @Inject('IDocumentRepository')
    private readonly documentRepository: IDocumentRepository,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(
    uuid: string,
    versionId?: number,
  ): Promise<{ url: string; fileName: string }> {
    const document = await this.documentRepository.findByUuid(uuid);
    if (!document) throw new NotFoundException('Documento não encontrado');

    if (!document.versions) {
      throw new NotFoundException('Documento não possui versões');
    }

    const version =
      versionId != null
        ? document.versions.find((v) => v.versionId === versionId)
        : document.versions.find((v) => v.versionId === document.currentVersion);

    if (!version)
      throw new NotFoundException('Versão do documento não encontrada');

    const fileName = version.filePath.split('/').pop() || 'document.pdf';
    const url = await this.storageProvider.getDownloadUrl(version.filePath, fileName); 

    return { url, fileName };
  }

  async executeMultiple(
    documents: Array<{ uuid: string; versions?: Array<{ versionId: number; filePath: string }> }>,
  ): Promise<{ [documentUuid: string]: { [versionId: number]: { url: string; fileName: string } } }> {
    const result: { [documentUuid: string]: { [versionId: number]: { url: string; fileName: string } } } = {};

    for (const document of documents) {
      if (!document.versions) continue;

      result[document.uuid] = {};
      
      for (const version of document.versions) {
        try {
          const fileName = version.filePath.split('/').pop() || 'document.pdf';
          const url = await this.storageProvider.getDownloadUrl(version.filePath, fileName);
          
          result[document.uuid][version.versionId] = { url, fileName };
        } catch (error) {
          console.error(`Erro ao gerar URL para documento ${document.uuid}, versão ${version.versionId}:`, error);
        }
      }
    }

    return result;
  }
}
