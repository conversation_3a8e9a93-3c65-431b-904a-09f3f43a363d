import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { Document } from '../../domain/entities/document.entity';
import { DocumentStatus } from '../../domain/enums/document-status.enum';
import { EntityType } from '../../domain/enums/entity-type.enum';
import {
  Prisma,
  DocumentStatus as PrismaDocumentStatus,
  EntityType as PrismaEntityType,
} from '@prisma/client';

@Injectable()
export class DocumentRepository implements IDocumentRepository {
  constructor(private readonly prisma: PrismaService) { }
  async updateStatusToArchived(
    uuid: string,
    archivedBy: string,
    updatedAt: Date,
  ): Promise<void> {
    await this.prisma.document.update({
      where: { id: uuid },
      data: {
        status: PrismaDocumentStatus.ARCHIVED,
        updatedBy: archivedBy,
        updatedAt,
      },
    });
  }

  async create(document: Document): Promise<Document> {
    const result = await this.prisma.$transaction(async (tx) => {
      const created = await tx.document.create({
        data: {
          id: document.uuid,
          entityType: document.entityType as PrismaEntityType,
          entityUuid: document.entityUuid,
          uploadedBy: document.uploadedBy,
          currentVersion: document.currentVersion,
          status: document.status as PrismaDocumentStatus,
          createdAt: document.createdAt,
          updatedAt: document.updatedAt,
          responsible: document.responsible,
          department: document.department,
          description: document.description,
          versions: {
            create: document.versions?.map((v) => ({
              versionId: v.versionId,
              uploadedBy: v.uploadedBy,
              filePath: v.filePath,
              expirationDate: v.expirationDate,
              uploadedAt: v.uploadedAt,
              createdAt: v.uploadedAt,
            })),
          },
        },
        include: { versions: true },
      });

      return {
        uuid: created.id,
        entityType: created.entityType as EntityType,
        entityUuid: created.entityUuid,
        currentVersion: created.currentVersion,
        status: created.status as DocumentStatus,
        uploadedBy: created.uploadedBy,
        createdAt: created.createdAt,
        updatedAt: created.updatedAt,
        responsible: created.responsible ?? undefined,
        department: created.department ?? undefined,
        description: created.description ?? undefined,
        versions: created.versions.map((v) => ({
          versionId: v.versionId,
          uploadedBy: v.uploadedBy,
          filePath: v.filePath,
          expirationDate: v.expirationDate ?? undefined,
          uploadedAt: v.uploadedAt,
          createdAt: v.uploadedAt,
        })),
      };
    });

    return result;
  }

  async findByUuid(uuid: string): Promise<Document | null> {
    const result = await this.prisma.document.findUnique({
      where: { id: uuid },
      include: { versions: true },
    });

    if (!result) return null;

    return {
      uuid: result.id,
      entityType: result.entityType as EntityType,
      entityUuid: result.entityUuid,
      currentVersion: result.currentVersion,
      status: result.status as DocumentStatus,
      uploadedBy: result.uploadedBy,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      responsible: result.responsible ?? undefined,
      department: result.department ?? undefined,
      description: result.description ?? undefined,
      versions: result.versions.map((v) => ({
        versionId: v.versionId,
        uploadedBy: v.uploadedBy,
        filePath: v.filePath,
        expirationDate: v.expirationDate ?? undefined,
        uploadedAt: v.uploadedAt,
        createdAt: v.uploadedAt,
      })),
    };
  }

  async list(
    filters: Partial<Pick<Document, 'entityType' | 'status' | 'entityUuid'>>,
    limit: number,
    offset: number,
  ): Promise<{ items: Document[]; total: number }> {
    const where: Prisma.DocumentWhereInput = {};

    if (filters.entityType)
      where.entityType = filters.entityType as PrismaEntityType;
    if (filters.status) where.status = filters.status as PrismaDocumentStatus;
    if (filters.entityUuid) where.entityUuid = filters.entityUuid;

    const [itemsRaw, total] = await this.prisma.$transaction([
      this.prisma.document.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: { updatedAt: 'desc' },
        include: { versions: true },
      }),
      this.prisma.document.count({ where }),
    ]);

    const items: Document[] = itemsRaw.map((d) => ({
      uuid: d.id,
      entityType: d.entityType as EntityType,
      entityUuid: d.entityUuid,
      currentVersion: d.currentVersion,
      status: d.status as DocumentStatus,
      uploadedBy: d.uploadedBy,
      createdAt: d.createdAt,
      updatedAt: d.updatedAt,
      responsible: d.responsible ?? undefined,
      department: d.department ?? undefined,
      description: d.description ?? undefined,
      versions: d.versions.map((v) => ({
        versionId: v.versionId,
        uploadedBy: v.uploadedBy,
        filePath: v.filePath,
        expirationDate: v.expirationDate ?? undefined,
        uploadedAt: v.uploadedAt,
        createdAt: v.uploadedAt,
      })),
    }));

    return { items, total };
  }
}
