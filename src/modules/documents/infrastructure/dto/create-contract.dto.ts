import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, IsUUID, MinLength, ValidateNested } from 'class-validator';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { ContractType, ContractStatus } from '@prisma/client';
import { Type } from 'class-transformer';

export class CreateContractDto {
  @ApiProperty({
    description: 'Identificador único ou nome para o contrato/arquivo dentro do lote.',
    example: 'contrato_servico_principal.pdf',
  })
  @IsString()
  @MinLength(1)
  contractIdentifier: string;

  @ApiProperty({
    description: 'Tipo da entidade à qual o contrato está associado (ex: SUPPLIER).',
    enum: EntityType,
    example: EntityType.SUPPLIER,
  })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID da entidade principal (ex: Fornecedor) à qual este contrato pertence.',
    format: 'uuid'
  })
  @IsUUID()
  entityActualUuid: string;

  @ApiPropertyOptional({
    description: 'Data de início do contrato (opcional)',
    format: 'date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento (opcional)',
    format: 'date',
    example: '2025-12-31',
  })
  @IsOptional()
  @IsString()
  expirationDate?: string;

  @ApiPropertyOptional({
    description: 'Tipo de contrato',
    enum: ContractType,
    example: ContractType.CERT_PLATFORM,
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  @ApiPropertyOptional({
    description: 'Se o contrato foi assinado',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  signed?: boolean;

  @ApiPropertyOptional({
    description: 'Observações sobre o contrato',
    example: 'Contrato com cláusulas especiais de confidencialidade',
  })
  @IsOptional()
  @IsString()
  observations?: string;
}

export class CreateContractsDto {
  @ApiProperty({
    description: 'Lista de metadados dos contratos',
    type: [CreateContractDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateContractDto)
  contracts: CreateContractDto[];
}

export class ContractApiBodyDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    description: "Arquivos dos contratos a serem enviados. Deve haver um arquivo para cada objeto na string JSON 'contractsMetadata'."
  })
  files: any[];

  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada contrato (corresponde à estrutura de CreateContractDto). O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"contractIdentifier": "contrato_principal.pdf", "entityType": "SUPPLIER", "entityActualUuid": "40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4", "contractType": "CERT_PLATFORM", "signed": true, "startDate": "2024-01-01", "expirationDate": "2025-12-31", "observations": "Contrato com cláusulas especiais de confidencialidade"}]'
  })
  @IsString()
  contractsMetadata: string;
}

export class ContractTextFormDataDto {
  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada contrato (corresponde à estrutura de CreateContractDto). O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"contractIdentifier": "contrato_principal.pdf", "entityType": "SUPPLIER", "entityActualUuid": "40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4", "contractType": "CERT_PLATFORM", "signed": true, "startDate": "2024-01-01", "expirationDate": "2025-12-31", "observations": "Contrato com cláusulas especiais de confidencialidade"}]'
  })
  @IsString()
  contractsMetadata: string;
}

export class ContractSignPatchDto {
  @ApiProperty({ description: 'Indica se o contrato foi assinado', type: Boolean })
  @IsBoolean()
  isSigned: boolean;
}

export class ContractUpdatePatchDto {
  @ApiPropertyOptional({
    description: 'Tipo de contrato',
    enum: ContractType,
    example: ContractType.CERT_PLATFORM,
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  @ApiPropertyOptional({
    description: 'Indica se o contrato foi assinado (determina automaticamente o status: true=APPROVED, false=REJECTED)',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  signed?: boolean;

  @ApiPropertyOptional({
    description: 'Data de início do contrato (opcional)',
    format: 'date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do contrato (opcional)',
    format: 'date',
    example: '2025-12-31',
  })
  @IsOptional()
  @IsString()
  expirationDate?: string;

  @ApiPropertyOptional({
    description: 'Observações sobre o contrato',
    example: 'Contrato com cláusulas especiais de confidencialidade',
  })
  @IsOptional()
  @IsString()
  observations?: string;
}