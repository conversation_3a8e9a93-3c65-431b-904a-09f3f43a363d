import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { EntityType } from '../../domain/enums/entity-type.enum';

export class CreateDocumentDto {
  @ApiProperty({
    description: 'Tipo da entidade vinculada ao documento',
    enum: EntityType,
    example: EntityType.CLIENT,
  })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID da entidade vinculada',
    format: 'uuid',
    example: 'e3f86be7-19d6-4f4c-8024-f6aa4dbaec8e',
  })
  @IsUUID()
  entityUuid: string;

  @IsOptional()
  @ApiProperty({
    description: 'Nome do responsável pelo documento',
    example: 'John Doe',
  })
  @IsString()
  responsible?: string;

  @IsOptional()
  @ApiProperty({
    description: 'Departamento responsável pelo documento',
    example: 'Financeiro',
  })
  @IsString()
  department?: string;

  @IsOptional()
  @ApiProperty({
    description: 'Descrição do documento',
    example: 'Contrato de prestação de serviços',
  })
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento (opcional)',
    format: 'date',
    example: '2025-12-31',
  })
  @IsOptional()
  expirationDate?: string;

  @ApiProperty({
    description: 'UUID do usuário que está realizando o upload',
    format: 'uuid',
    example: 'd8c80a59-4059-49e0-a0f9-9a25c5e6a96e',
  })
  @IsUUID()
  uploadedBy: string;
}
