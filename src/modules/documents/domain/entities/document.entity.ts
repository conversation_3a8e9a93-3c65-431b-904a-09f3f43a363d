import { DocumentStatus } from '../enums/document-status.enum';
import { EntityType } from '../enums/entity-type.enum';
import { DocumentVersion } from './document-version.entity';

export interface Document {
  uuid: string;
  entityType: EntityType;
  entityUuid: string;
  currentVersion: number;
  status: DocumentStatus;
  uploadedBy: string;
  createdAt: Date;
  updatedAt: Date;
  versions?: DocumentVersion[];
  updatedBy?: string;
  responsible?: string;
  department?: string;
  description?: string;
  downloadUrl?: string;
  fileName?: string;
}
