import { Document } from '../entities/document.entity';

export interface IDocumentRepository {
  create(document: Partial<Document>): Promise<Document>;
  findByUuid(uuid: string): Promise<Document | null>;
  list(
    filters: Partial<Pick<Document, 'entityType' | 'status' | 'entityUuid'>>,
    limit: number,
    offset: number,
  ): Promise<{ items: Document[]; total: number }>;
  updateStatusToArchived(
    uuid: string,
    archivedBy: string,
    updatedAt: Date,
  ): Promise<void>;
}
