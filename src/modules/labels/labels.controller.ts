import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { LabelsService } from './labels.service';
import { CreateLabelDto } from './dto/create-label.dto';
import { UpdateLabelDto } from './dto/update-label.dto';
import { LabelResponseDto } from './dto/label-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../../core/domain/role.enum';

@ApiTags('Labels')
@Controller('labels')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@ApiBearerAuth()
export class LabelsController {
  constructor(private readonly labelsService: LabelsService) {}

  @Post()
  @ApiOperation({ summary: 'Criar uma nova label' })
  @ApiResponse({ status: 201, type: LabelResponseDto })
  async create(@Body() dto: CreateLabelDto): Promise<LabelResponseDto> {
    return this.labelsService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todas as labels' })
  @ApiResponse({ status: 200, type: [LabelResponseDto] })
  async findAll(): Promise<LabelResponseDto[]> {
    return this.labelsService.findAll();
  }

  @Get('by-modulo')
  @ApiOperation({ summary: 'Listar labels por módulo' })
  @ApiQuery({ name: 'modulo', required: true })
  @ApiResponse({ status: 200, type: [LabelResponseDto] })
  async findByModulo(@Query('modulo') modulo: string): Promise<LabelResponseDto[]> {
    return this.labelsService.findByModulo(modulo);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma label por ID' })
  @ApiResponse({ status: 200, type: LabelResponseDto })
  async findOne(@Param('id') id: string): Promise<LabelResponseDto> {
    return this.labelsService.findOne(Number(id));
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar uma label' })
  @ApiResponse({ status: 200, type: LabelResponseDto })
  async update(@Param('id') id: string, @Body() dto: UpdateLabelDto): Promise<LabelResponseDto> {
    return this.labelsService.update(Number(id), dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Deletar uma label (físico)' })
  @ApiResponse({ status: 204 })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id') id: string): Promise<void> {
    await this.labelsService.delete(Number(id));
  }
} 