import { Modu<PERSON> } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { PrismaModule } from '../../infrastructure/prisma/prisma.module';
import { PrismaUserRepository } from '../../infrastructure/repositories/prisma-user.repository';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';
import { KeycloakModule } from '@/infrastructure/keycloak/keycloak.module';

@Module({
  imports: [PrismaModule, KeycloakModule],
  providers: [
    UsersService,
    {
      provide: 'UserRepository',
      useClass: PrismaUserRepository,
    },
    KeycloakIdentityProviderService,
  ],
  controllers: [UsersController],
  exports: [UsersService, 'UserRepository'],
})
export class UsersModule { }
