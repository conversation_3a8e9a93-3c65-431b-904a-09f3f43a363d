import { ApiProperty } from "@nestjs/swagger";

export class CreateContactDto {
    @ApiProperty({ example: '<EMAIL>' })
    contact: string;
    
    @ApiProperty({ example: 'email' })
    type: string;
    
    @ApiProperty({ example: 'Finance' })
    area: string;
    
    @ApiProperty({ example: '<PERSON>' })
    responsible: string;
}

export class ContactResponseDto {
  @ApiProperty({ example: '<EMAIL>' })
  contact: string;

  @ApiProperty({ example: 'email' })
  type: string;

  @ApiProperty({ example: 'Finance' })
  area: string;

  @ApiProperty({ example: '<PERSON>' })
  responsible: string;
}
