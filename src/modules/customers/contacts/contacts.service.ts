import { Injectable, NotFoundException } from '@nestjs/common';
import { ContactResponseDto, CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { CreateCustomerContactUseCase } from '@/core/application/use-cases/customer/create-customer-contact.use-case';
import { FindCustomerByUuidUseCase } from '../application/use-cases/find-customer-by-uuid.use-case';
import { ListCustomerContactsUseCase } from '@/core/application/use-cases/customer/list-customer-contacts.use-case';
import { DeleteCustomerContactUseCase } from '@/core/application/use-cases/customer/delete-customer-contact.use-case';
import { UpdateCustomerContactUseCase } from '@/core/application/use-cases/customer/update-customer-contact.use-case';

@Injectable()
export class ContactsService {
  constructor(
    private readonly findCustomerByUuidUseCase: FindCustomerByUuidUseCase,
    private readonly createCustomerContactUseCase: CreateCustomerContactUseCase,
    private readonly listCustomerContactsUseCase: ListCustomerContactsUseCase,
    private readonly deleteCustomerContactUseCase: DeleteCustomerContactUseCase,
    private readonly updateCustomerContactUseCase: UpdateCustomerContactUseCase,
  ) {}

  async create(uuid: string, contactsData: Array<CreateContactDto>) : Promise<ContactResponseDto[]> {
    const customer = await this.findCustomerByUuidUseCase.execute(uuid);

    const createdContacts = await Promise.all(
      contactsData.map((contactData) =>
        this.createCustomerContactUseCase.execute(customer.id!, {
          ...contactData,
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      ),
    );

    return createdContacts.map((contact) => ({
      contact: contact.contact,
      type: contact.type,
      area: contact.area,
      responsible: contact.responsible,
    }));
  }

  async findAll(uuid: string) {
    const customer = await this.findCustomerByUuidUseCase.execute(uuid);
    const contacts = await this.listCustomerContactsUseCase.execute(customer.id!);

    const contactsResponse = contacts.map((contact) => ({
      id: contact.id,
      contact: contact.contact,
      type: contact.type,
      area: contact.area,
      responsible: contact.responsible,
    }));
    return { contacts: contactsResponse };
  }

  findOne(id: number) {
    return `This action returns a #${id} contact`;
  }

  update(id: string, updateContactDto: UpdateContactDto) {
    return this.updateCustomerContactUseCase.execute(id, updateContactDto)
  }

  remove(id: string) {
    return this.deleteCustomerContactUseCase.execute(id)
  }
}
