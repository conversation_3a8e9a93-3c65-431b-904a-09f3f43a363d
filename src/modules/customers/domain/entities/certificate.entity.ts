import { AggregateRoot } from '../../../../core/domain/base/aggregate-root';
import { CertificateCategory } from '../enums/certificate-category.enum';
import { CertificateType } from '../enums/certificate-type.enum';
import { randomUUID } from 'crypto';

export interface CertificateProps {
  id?: string;
  customerId: number;
  category: CertificateCategory;
  type: CertificateType;
  fileUrl: string;
  notes?: string;
  uploadedById: string;
  createdAt?: Date;
  updatedAt?: Date;
  uploadedByName?: string;
}

export class Certificate extends AggregateRoot {
  public readonly id: string;
  public customerId: number;
  public category: CertificateCategory;
  public type: CertificateType;
  public fileUrl: string;
  public notes?: string;
  public uploadedById: string;
  public readonly createdAt: Date;
  public updatedAt: Date;
  public uploadedByName?: string;

  private constructor(props: CertificateProps) {
    super();
    this.id = props.id ?? randomUUID();
    this.customerId = props.customerId;
    this.category = props.category;
    this.type = props.type;
    this.fileUrl = props.fileUrl;
    this.notes = props.notes;
    this.uploadedById = props.uploadedById;
    this.createdAt = props.createdAt ?? new Date();
    this.updatedAt = props.updatedAt ?? new Date();
    this.uploadedByName = props.uploadedByName;
  }

  public static create(props: CertificateProps): Certificate {
    const instance = new Certificate(props);
    // Aqui podemos adicionar eventos de domínio, se necessário
    // instance.addEvent(new CertificateCreatedEvent(instance));
    return instance;
  }
} 