import { Customer } from '../entities/customer.entity';

export interface CustomerFilterCriteria {
  corporateName?: string;
  cnpj?: string;
  type?: string;
  status?: string;
}

export interface CustomerListResponse {
  items: Customer[];
  limit: number;
  offset: number;
  total: number;
}

export interface IListCustomersUseCase {
  execute(
    criteria: CustomerFilterCriteria,
    limit: number,
    offset: number,
  ): Promise<CustomerListResponse>;
}
