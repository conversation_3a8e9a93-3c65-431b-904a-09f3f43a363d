import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { ICustomerDocumentRepository } from '../../domain/repositories/customer-document.repository.interface';
import { CUSTOMER_DOCUMENT_REPOSITORY } from '../../domain/constants/tokens';

@Injectable()
export class DownloadCustomerDocumentUseCase {
  constructor(
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
    @Inject(CUSTOMER_DOCUMENT_REPOSITORY)
    private readonly documentRepository: ICustomerDocumentRepository,
  ) {}

  async execute(customerUuid: string, documentId: string, expiresIn: number = 3600): Promise<{ downloadUrl: string; fileName: string }> {
    const document = await this.documentRepository.findById(documentId);
    
    if (!document || document.customerUuid !== customerUuid) {
      throw new NotFoundException('Documento não encontrado');
    }

    // Verificar se o documento tem arquivo no S3
    if (!document.url || document.url.trim() === '') {
      throw new NotFoundException('Arquivo do documento não encontrado');
    }

    const key = document.url; // Assumindo que o URL armazena a chave S3
    
    // Extrair o fileName real do documento ou do filePath
    let fileName = document.fileName;
    if (!fileName && key) {
      const parts = key.split('/');
      fileName = parts.length > 0 ? parts[parts.length - 1] : `${document.name}.pdf`;
    }
    fileName = fileName || `${document.name}.pdf`;
    
    const downloadUrl = await this.storageProvider.getDownloadUrl(key, fileName, expiresIn);
    
    return {
      downloadUrl,
      fileName,
    };
  }

  async executeMultiple(customerUuid: string, documentIds: string[], expiresIn: number = 3600): Promise<Array<{ documentId: string; downloadUrl: string; fileName: string }>> {
    const results = await Promise.all(
      documentIds.map(async (documentId) => {
        try {
          const result = await this.execute(customerUuid, documentId, expiresIn);
          return {
            documentId,
            ...result,
          };
        } catch (error) {
          return {
            documentId,
            downloadUrl: '',
            fileName: '',
          };
        }
      })
    );

    return results;
  }
} 