import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  UploadedFile,
  UseInterceptors,
  Query,
  UseGuards,
  Inject,
  UploadedFiles,
  Request,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { CreateCustomerDocumentUseCase } from '../use-cases/create-customer-document.use-case';
import { DeleteCustomerDocumentUseCase } from '../use-cases/delete-customer-document.use-case';
import { GetCustomerDocumentUseCase } from '../use-cases/get-customer-document.use-case';
import {
  ListCustomerDocumentUseCase,
  ListCustomerDocumentResponse,
} from '../use-cases/list-customer-document.use-case';
import { UpdateCustomerDocumentUseCase } from '../use-cases/update-customer-document.use-case';
import { DownloadCustomerDocumentUseCase } from '../use-cases/download-customer-document.use-case';
import {
  UploadCustomerDocumentDto,
  UpdateCustomerDocumentDto,
  CustomerDocumentResponseDto,
  CustomerDocumentListItemDto,
  CustomerDocumentApiBodyDto,
  CustomerDocumentTextFormDataDto,
  CreateCustomerDocumentsDto,
} from '../../domain/dtos/customer-document.dto';
import { JwtAuthGuard } from '../../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../../auth/guards/roles.guard';
import { Roles } from '../../../../auth/decorators/roles.decorator';
import { Role } from '../../../../../core/domain/role.enum';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    [key: string]: any;
  };
}

@ApiTags('Customer Documents')
@Controller('core/customers/:uuid/documents')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
@ApiBearerAuth()
export class CustomerDocumentController {
  constructor(
    private readonly createCustomerDocumentUseCase: CreateCustomerDocumentUseCase,
    private readonly deleteCustomerDocumentUseCase: DeleteCustomerDocumentUseCase,
    private readonly getCustomerDocumentUseCase: GetCustomerDocumentUseCase,
    private readonly listCustomerDocumentUseCase: ListCustomerDocumentUseCase,
    private readonly updateCustomerDocumentUseCase: UpdateCustomerDocumentUseCase,
    private readonly downloadCustomerDocumentUseCase: DownloadCustomerDocumentUseCase,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiOperation({
    summary: 'Criar documentos para o cliente e fazer upload dos arquivos.',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Cliente',
    type: String,
  })
  @ApiBody({
    description:
      "Dados para criação de documentos. 'files' são os arquivos e 'documentsMetadata' é uma string JSON com os metadados.",
    type: CustomerDocumentApiBodyDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Documentos criados com sucesso.',
    type: [CustomerDocumentResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiResponse({ status: 403, description: 'Acesso negado.' })
  async createDocuments(
    @Param('uuid') customerUuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() formData: CustomerDocumentTextFormDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<CustomerDocumentResponseDto[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    let documentsMetadataArray: unknown;
    try {
      documentsMetadataArray = JSON.parse(formData.documentsMetadata);
    } catch (_error) {
      throw new Error(
        'O campo documentsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(documentsMetadataArray) ||
      files.length !== documentsMetadataArray.length
    ) {
      throw new Error(
        'O número de metadatos de documento não corresponde ao número de arquivos enviados.',
      );
    }

    const processedDocumentsData = (documentsMetadataArray as unknown[]).map((meta) => ({
      ...(meta as Record<string, unknown>),
      customerUuid: customerUuid,
      uploadedBy: req.user.id,
    }));

    const createDocumentsDto: CreateCustomerDocumentsDto = {
      documents: processedDocumentsData as never,
    };

    const documents = await this.createCustomerDocumentUseCase.executeMultiple(
      customerUuid,
      files,
      req.user.id,
      createDocumentsDto,
    );

    return documents.map(document => CustomerDocumentResponseDto.fromEntity(document));
  }

  @Post('legacy')
  @HttpCode(HttpStatus.CREATED)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Criar documento para o cliente (método legado)' })
  @ApiResponse({ status: 201, type: CustomerDocumentResponseDto })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'RG', description: 'Nome do documento' },
        file: { type: 'string', format: 'binary', description: 'Arquivo do documento' },
      },
      required: ['name', 'file'],
    },
  })
  async create(
    @Param('uuid') customerUuid: string,
    @Body() dto: UploadCustomerDocumentDto,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<CustomerDocumentResponseDto> {
    if (!file) {
      throw new Error('Arquivo é obrigatório');
    }

    const document = await this.createCustomerDocumentUseCase.execute({
      customerUuid,
      name: dto.name,
      file,
    });

    return CustomerDocumentResponseDto.fromEntity(document);
  }

  @Get()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Listar documentos do cliente' })
  @ApiResponse({ status: 200, type: [CustomerDocumentListItemDto] })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Número de itens por página',
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Número de itens para pular',
  })
  async list(
    @Param('uuid') customerUuid: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<ListCustomerDocumentResponse> {
    return this.listCustomerDocumentUseCase.execute(
      customerUuid,
      limit ? Number(limit) : undefined,
      offset ? Number(offset) : undefined,
    );
  }

  @Get(':id')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Buscar documento específico do cliente' })
  @ApiResponse({ status: 200, type: CustomerDocumentResponseDto })
  async get(@Param('id') id: string): Promise<CustomerDocumentResponseDto> {
    const document = await this.getCustomerDocumentUseCase.execute(id);
    return CustomerDocumentResponseDto.fromEntity(document);
  }

  @Put(':id')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Atualizar documento do cliente' })
  @ApiResponse({ status: 200, type: CustomerDocumentResponseDto })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'RG Atualizado', description: 'Nome do documento' },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Novo arquivo do documento (opcional)',
        },
      },
    },
  })
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateCustomerDocumentDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<CustomerDocumentResponseDto> {
    const document = await this.updateCustomerDocumentUseCase.execute(id, {
      name: dto.name,
      file: file,
    });
    return CustomerDocumentResponseDto.fromEntity(document);
  }

  @Get(':id/download')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Obter URL de download do documento' })
  @ApiResponse({ 
    status: 200, 
    schema: {
      type: 'object',
      properties: {
        downloadUrl: { type: 'string', description: 'URL para download do documento' },
        fileName: { type: 'string', description: 'Nome do arquivo' }
      }
    }
  })
  async getDownloadUrl(
    @Param('uuid') customerUuid: string,
    @Param('id') id: string,
  ): Promise<{ downloadUrl: string; fileName: string }> {
    return this.downloadCustomerDocumentUseCase.execute(customerUuid, id);
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover documento do cliente' })
  @ApiResponse({ status: 204 })
  async delete(@Param('id') id: string): Promise<void> {
    await this.deleteCustomerDocumentUseCase.execute(id);
  }
}
