import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { ICustomerDocumentRepository } from '../../domain/repositories/customer-document.repository.interface';
import { CustomerDocument } from '../../domain/entities/customer-document.entity';
import { randomUUID } from 'crypto';

export const CUSTOMER_DOCUMENT_REPOSITORY = 'CUSTOMER_DOCUMENT_REPOSITORY';

@Injectable()
export class CustomerDocumentRepository implements ICustomerDocumentRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(document: Partial<CustomerDocument>): Promise<CustomerDocument> {
    const created = await this.prisma.$queryRaw<CustomerDocument[]>`
      INSERT INTO core.customer_documents (id, "customerUuid", name, url, file_name, created_at, updated_at)
      VALUES (
        ${randomUUID()},
        ${document.customerUuid},
        ${document.name},
        ${document.url},
        ${document.fileName || null},
        NOW(),
        NOW()
      )
      RETURNING *
    `;

    return this.mapDbToEntity(created[0]);
  }

  async createWithMetadata(document: {
    customerUuid: string;
    name: string;
    url: string;
    fileName?: string;
    responsible?: string;
    department?: string;
    description?: string;
    expirationDate?: string;
    uploadedBy?: string;
  }): Promise<CustomerDocument> {
    const created = await this.prisma.$queryRaw<any[]>`
      INSERT INTO core.customer_documents (
        id, "customerUuid", name, url, file_name, responsible, department, 
        description, expiration_date, uploaded_by, created_at, updated_at
      )
      VALUES (
        ${randomUUID()},
        ${document.customerUuid},
        ${document.name},
        ${document.url},
        ${document.fileName || null},
        ${document.responsible || null},
        ${document.department || null},
        ${document.description || null},
        ${document.expirationDate || null},
        ${document.uploadedBy || null},
        NOW(),
        NOW()
      )
      RETURNING *
    `;

    return this.mapDbToEntity(created[0]);
  }

  private mapDbToEntity(dbRow: any): CustomerDocument {
    return {
      id: dbRow.id,
      customerUuid: dbRow.customerUuid,
      name: dbRow.name,
      url: dbRow.url,
      fileName: dbRow.file_name,
      responsible: dbRow.responsible,
      department: dbRow.department,
      description: dbRow.description,
      expirationDate: dbRow.expiration_date,
      uploadedBy: dbRow.uploaded_by,
      createdAt: dbRow.created_at,
      updatedAt: dbRow.updated_at,
      deletedAt: dbRow.deleted_at,
    };
  }

  async findById(id: string): Promise<CustomerDocument | null> {
    const [document] = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM core.customer_documents
      WHERE id = ${id}
      AND deleted_at IS NULL
    `;

    return document ? this.mapDbToEntity(document) : null;
  }

  async findAllByCustomer(
    customerUuid: string,
    limit: number,
    offset: number,
  ): Promise<{ documents: CustomerDocument[]; total: number }> {
    const [total] = await this.prisma.$queryRaw<[{ count: number }]>`
      SELECT COUNT(*) as count FROM core.customer_documents
      WHERE "customerUuid" = ${customerUuid}
      AND deleted_at IS NULL
    `;

    const documents = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM core.customer_documents
      WHERE "customerUuid" = ${customerUuid}
      AND deleted_at IS NULL
      ORDER BY created_at DESC
      LIMIT ${limit}
      OFFSET ${offset}
    `;

    return {
      documents: documents.map(doc => this.mapDbToEntity(doc)),
      total: Number(total.count),
    };
  }

  async update(id: string, data: Partial<CustomerDocument>): Promise<CustomerDocument> {
    const [updated] = await this.prisma.$queryRaw<any[]>`
      UPDATE core.customer_documents
      SET
        name = COALESCE(${data.name}, name),
        url = COALESCE(${data.url}, url),
        file_name = COALESCE(${data.fileName || null}, file_name),
        responsible = COALESCE(${data.responsible || null}, responsible),
        department = COALESCE(${data.department || null}, department),
        description = COALESCE(${data.description || null}, description),
        expiration_date = COALESCE(${data.expirationDate || null}, expiration_date),
        updated_at = NOW()
      WHERE id = ${id}
      AND deleted_at IS NULL
      RETURNING *
    `;

    return this.mapDbToEntity(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.$queryRaw`
      UPDATE core.customer_documents
      SET deleted_at = NOW()
      WHERE id = ${id}
      AND deleted_at IS NULL
    `;
  }

  async restore(id: string): Promise<CustomerDocument> {
    const [restored] = await this.prisma.$queryRaw<any[]>`
      UPDATE core.customer_documents
      SET deleted_at = NULL
      WHERE id = ${id}
      AND deleted_at IS NOT NULL
      RETURNING *
    `;

    return this.mapDbToEntity(restored);
  }
}
