import { CustomerDocument } from '../entities/customer-document.entity';

export interface ICustomerDocumentRepository {
  create(document: Partial<CustomerDocument>): Promise<CustomerDocument>;
  createWithMetadata(document: {
    customerUuid: string;
    name: string;
    url: string;
    fileName?: string;
    responsible?: string;
    department?: string;
    description?: string;
    expirationDate?: string;
    uploadedBy?: string;
  }): Promise<CustomerDocument>;
  findById(id: string): Promise<CustomerDocument | null>;
  findAllByCustomer(
    customerUuid: string,
    limit: number,
    offset: number,
  ): Promise<{ documents: CustomerDocument[]; total: number }>;
  update(id: string, data: Partial<CustomerDocument>): Promise<CustomerDocument>;
  delete(id: string): Promise<void>;
  restore(id: string): Promise<CustomerDocument>;
}
