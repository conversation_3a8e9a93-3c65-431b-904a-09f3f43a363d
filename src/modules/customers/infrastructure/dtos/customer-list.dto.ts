import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import {
  Customer,
  CustomerStatus,
} from '../../domain/entities/customer.entity';

export class CustomerListQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by partial match of corporate name',
    example: 'ACME',
  })
  @IsOptional()
  @IsString()
  filterCorporateName?: string;

  @ApiPropertyOptional({
    description: 'Filter by exact CNPJ',
    example: '12345678901234',
  })
  @IsOptional()
  @IsString()
  filterCNPJ?: string;

  @ApiPropertyOptional({
    description: 'Filter by customer type',
    example: 'SPORT BETTING',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Filter by customer status',
    example: 'ACTIVE',
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: 'Limit of items per page',
    default: 10,
    minimum: 1,
    example: 5,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Offset for pagination',
    default: 0,
    minimum: 0,
    example: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  offset?: number = 0;
}

export class CustomerResponseDto {
  @ApiProperty({
    description: 'Customer UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  uuid: string;

  @ApiProperty({
    description: 'Razão Social',
    example: 'ACME Corp',
  })
  razaoSocial: string;

  @ApiProperty({
    description: 'CNPJ',
    example: '12345678901234',
  })
  cnpj: string;

  @ApiProperty({
    description: 'Email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiPropertyOptional({
    description: 'Phone',
    example: '******-456-7890',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: 'Address information',
    example: {
      street: 'Main St',
      number: '123',
      city: 'Anytown',
      state: 'ST',
      zipCode: '12345-678',
    },
  })
  address?: Record<string, unknown>;

  @ApiProperty({
    description: 'Imagem (base64 ou URL)',
    example: 'https://example.com/image.png',
    required: false,
  })
  image?: string;

  @ApiProperty({
    description: 'Customer status',
    enum: CustomerStatus,
    example: CustomerStatus.ACTIVE,
  })
  status: CustomerStatus;

  @ApiProperty({ description: 'url', example: 'https://example.com' })
  url: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Created by user',
    example: 'system',
  })
  createdBy?: string;

  @ApiProperty({
    description: 'Last updated by user',
    example: 'system',
  })
  updatedBy?: string;

  static fromEntity(customer: Customer): CustomerResponseDto {
    return {
      uuid: customer.uuid,
      razaoSocial: customer.razaoSocial,
      cnpj: customer.cnpj,
      email: customer.email,
      phone: customer.phone,
      address: customer.address,
      image: customer.image,
      status: customer.status,
      url: customer.url,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      createdBy: customer.createdBy,
      updatedBy: customer.updatedBy,
    };
  }
}

export class CustomerListResponseDto {
  @ApiProperty({
    description: 'List of customers',
    type: [CustomerResponseDto],
  })
  items: CustomerResponseDto[];

  @ApiProperty({
    description: 'Limit of items per page',
    example: 5,
  })
  limit: number;

  @ApiProperty({
    description: 'Offset for pagination',
    example: 0,
  })
  offset: number;

  @ApiProperty({
    description: 'Total number of customers that match the filters',
    example: 1,
  })
  total: number;
}
