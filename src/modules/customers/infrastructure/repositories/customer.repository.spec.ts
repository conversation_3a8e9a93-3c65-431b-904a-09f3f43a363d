import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { CustomerRepository } from './customer.repository';
import { CustomerStatus } from '../../domain/entities/customer.entity';

describe('CustomerRepository (integration)', () => {
  let repository: CustomerRepository;
  let prisma: PrismaService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CustomerRepository, PrismaService],
    }).compile();
    repository = module.get(CustomerRepository);
    prisma = module.get(PrismaService);
  });

  afterEach(async () => {
    await prisma.customer.deleteMany({});
  });

  it('should soft delete a customer', async () => {
    // Cria um customer
    const created = await repository.create({
      uuid: 'test-uuid',
      razaoSocial: 'Test Company',
      cnpj: '12345678901234',
      email: '<EMAIL>',
      phone: '11999999999',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    // Deleta
    await repository.delete(created.uuid);
    // Busca por UUID (deve retornar com deletedAt preenchido)
    const byUuid = await repository.findByUuid(created.uuid);
    expect(byUuid).toBeDefined();
    expect(byUuid?.deletedAt).toBeInstanceOf(Date);
    // Busca por documento (deve retornar null)
    const byDoc = await repository.findByDocument(created.cnpj);
    expect(byDoc).toBeNull();
    // Listagem (deve retornar vazio)
    const list = await repository.listCustomers({}, 10, 0);
    expect(list.total).toBe(0);
  });

  it('should update a customer', async () => {
    const created = await repository.create({
      uuid: 'patch-uuid',
      razaoSocial: 'Old Company Name',
      cnpj: '99999999999999',
      email: '<EMAIL>',
      phone: '11888888888',
      status: CustomerStatus.ACTIVE,
      userId: 'user2',
      url: 'https://old.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    const updated = await repository.update(created.uuid, {
      razaoSocial: 'New Company Name',
      email: '<EMAIL>',
      updatedBy: 'admin',
    });
    expect(updated.razaoSocial).toBe('New Company Name');
    expect(updated.email).toBe('<EMAIL>');
    expect(updated.uuid).toBe(created.uuid);
  });
});
