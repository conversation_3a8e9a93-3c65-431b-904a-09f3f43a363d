import { Test, TestingModule } from '@nestjs/testing';
import { CreateCustomerUseCase } from './create-customer.use-case';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { UsersService } from '@/modules/users/users.service';
import { AuthService } from '@/modules/auth/auth.service';
import { CustomerStatus } from '../../domain/entities/customer.entity';
import { DuplicateEmailError } from '@/infrastructure/exceptions/duplicate-email.error';
import { DuplicateCnpjError } from '@/infrastructure/exceptions/duplicate-cnpj.error';
import { NotFoundException } from '@nestjs/common';
import { Role } from '@/core/domain/role.enum';

describe('CreateCustomerUseCase', () => {
  let useCase: CreateCustomerUseCase;
  let customerRepository: jest.Mocked<ICustomerRepository>;
  let usersService: jest.Mocked<UsersService>;
  let authService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateCustomerUseCase,
        {
          provide: 'ICustomerRepository',
          useValue: {
            findByDocument: jest.fn(),
            findByEmail: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: UsersService,
          useValue: {
            findByEmail: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: AuthService,
          useValue: {
            forgotPassword: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get(CreateCustomerUseCase);
    customerRepository = module.get('ICustomerRepository');
    usersService = module.get(UsersService);
    authService = module.get(AuthService);
  });

  const createCustomerDto = {
    razaoSocial: 'Test Company',
    cnpj: '12345678901234',
    email: '<EMAIL>',
    phone: '11999999999',
    address: {
      street: 'Test Street',
      city: 'Test City',
      state: 'TS',
      zipCode: '12345-678',
    },
    url: 'https://test.com',
  };

  it('should throw DuplicateCnpjError if CNPJ already exists', async () => {
    customerRepository.findByDocument.mockResolvedValue({
      uuid: 'existing-uuid',
      razaoSocial: 'Existing Company',
      cnpj: '12345678901234',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://existing.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await expect(
      useCase.execute({
        customer: createCustomerDto,
        createdBy: 'admin',
      }),
    ).rejects.toThrow(DuplicateCnpjError);

    expect(customerRepository.findByDocument).toHaveBeenCalledWith('12345678901234');
  });

  it('should throw DuplicateEmailError if email already exists in customers', async () => {
    customerRepository.findByDocument.mockResolvedValue(null);
    customerRepository.findByEmail.mockResolvedValue({
      uuid: 'existing-uuid',
      razaoSocial: 'Existing Company',
      cnpj: '99999999999999',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://existing.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await expect(
      useCase.execute({
        customer: createCustomerDto,
        createdBy: 'admin',
      }),
    ).rejects.toThrow(DuplicateEmailError);

    expect(customerRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should throw DuplicateEmailError if email already exists in users', async () => {
    customerRepository.findByDocument.mockResolvedValue(null);
    customerRepository.findByEmail.mockResolvedValue(null);
    usersService.findByEmail.mockResolvedValue({
      id: 'existing-user-id',
      email: '<EMAIL>',
      name: 'Existing User',
      role: Role.ADMIN,
    } as any);

    await expect(
      useCase.execute({
        customer: createCustomerDto,
        createdBy: 'admin',
      }),
    ).rejects.toThrow(DuplicateEmailError);

    expect(usersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should create customer successfully when no conflicts exist', async () => {
    customerRepository.findByDocument.mockResolvedValue(null);
    customerRepository.findByEmail.mockResolvedValue(null);
    usersService.findByEmail.mockRejectedValue(new NotFoundException());
    usersService.create.mockResolvedValue({
      id: 'new-user-id',
      email: '<EMAIL>',
      name: 'Test Company',
      role: Role.CUSTOMER_VIEWER,
    } as any);
    customerRepository.create.mockResolvedValue({
      id: 1,
      uuid: 'new-uuid',
      razaoSocial: 'Test Company',
      cnpj: '12345678901234',
      email: '<EMAIL>',
      phone: '11999999999',
      status: CustomerStatus.PENDING,
      userId: 'new-user-id',
      url: 'https://test.com',
      address: createCustomerDto.address,
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    authService.forgotPassword.mockResolvedValue(undefined);

    const result = await useCase.execute({
      customer: createCustomerDto,
      createdBy: 'admin',
    });

    expect(result).toBeDefined();
    expect(result.cnpj).toBe('12345678901234');
    expect(result.email).toBe('<EMAIL>');
    expect(customerRepository.create).toHaveBeenCalled();
    expect(usersService.create).toHaveBeenCalledWith({
      name: 'Test Company',
      email: '<EMAIL>',
      password: 'sut@t6@LuhKX29*C',
      role: Role.CUSTOMER_VIEWER,
    });
    expect(authService.forgotPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
    });
  });
}); 