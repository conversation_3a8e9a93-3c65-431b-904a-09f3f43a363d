import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';
import { CustomerPaymentPreference } from '../../domain/entities/customer-payment-preference.entity';

@Injectable()
export class GetCustomerPaymentPreferenceUseCase {
  constructor(
    @Inject('ICustomerPaymentPreferenceRepository')
    private readonly repository: ICustomerPaymentPreferenceRepository,
  ) {}

  async execute(id: string): Promise<CustomerPaymentPreference> {
    const preference = await this.repository.findById(id);
    if (!preference) {
      throw new NotFoundException('Customer payment preference not found');
    }
    return preference;
  }
} 