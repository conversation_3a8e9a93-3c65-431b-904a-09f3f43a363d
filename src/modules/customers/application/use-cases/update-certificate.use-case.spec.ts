import { Test, TestingModule } from '@nestjs/testing';
import { UpdateCertificateUseCase } from './update-certificate.use-case';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { Certificate } from '../../domain/entities/certificate.entity';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';
import { NotFoundException } from '@nestjs/common';
import { UpdateCertificateDto } from '../../infrastructure/dtos/update-certificate.dto';

describe('UpdateCertificateUseCase', () => {
  let useCase: UpdateCertificateUseCase;
  let certificateRepository: jest.Mocked<ICertificateRepository>;
  let storageProvider: jest.Mocked<IStorageProvider>;

  const mockCertificate = Certificate.create({
    id: 'cert-uuid-123',
    customerId: 1,
    category: CertificateCategory.SPORTSBOOK,
    type: CertificateType.CERTIFICADO_DE_SPORTSBOOK,
    fileUrl: 'path/to/old.pdf',
    uploadedById: 'admin-id',
  });

  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'new.pdf',
    mimetype: 'application/pdf',
    buffer: Buffer.from('new-file'),
    // ... outras propriedades do mock de arquivo
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateCertificateUseCase,
        {
          provide: CERTIFICATE_REPOSITORY,
          useValue: {
            findById: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: 'IStorageProvider',
          useValue: {
            upload: jest.fn(),
            // delete: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get<UpdateCertificateUseCase>(UpdateCertificateUseCase);
    certificateRepository = module.get(CERTIFICATE_REPOSITORY);
    storageProvider = module.get('IStorageProvider');
  });

  it('should update certificate fields successfully', async () => {
    certificateRepository.findById.mockResolvedValue(mockCertificate);
    certificateRepository.update.mockImplementation(async (cert) => cert);

    const updateDto: UpdateCertificateDto = {
      notes: 'New notes',
      category: CertificateCategory.PAGAMENTOS,
    };

    const result = await useCase.execute({
      id: mockCertificate.id,
      data: updateDto,
      updatedBy: 'user-id',
    });

    expect(result.notes).toBe('New notes');
    expect(result.category).toBe(CertificateCategory.PAGAMENTOS);
    expect(result.type).toBe(mockCertificate.type); // Should not change
  });

  it('should upload a new file and update fileUrl if a file is provided', async () => {
    certificateRepository.findById.mockResolvedValue(mockCertificate);
    certificateRepository.update.mockImplementation(async (cert) => cert);
    storageProvider.upload.mockResolvedValue(undefined);

    const result = await useCase.execute({
      id: mockCertificate.id,
      data: {},
      file: mockFile,
      updatedBy: 'user-id',
    });

    expect(storageProvider.upload).toHaveBeenCalled();
    expect(result.fileUrl).not.toBe('path/to/old.pdf');
    expect(result.fileUrl).toContain('new.pdf');
  });

  it('should throw NotFoundException if certificate does not exist', async () => {
    certificateRepository.findById.mockResolvedValue(null);

    await expect(
      useCase.execute({
        id: 'non-existent-id',
        data: {},
        updatedBy: 'user-id',
      }),
    ).rejects.toThrow(NotFoundException);
  });
}); 