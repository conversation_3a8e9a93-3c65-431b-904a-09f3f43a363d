import { Test, TestingModule } from '@nestjs/testing';
import { DeleteCertificateUseCase } from './delete-certificate.use-case';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { Certificate } from '../../domain/entities/certificate.entity';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';
import { NotFoundException } from '@nestjs/common';

describe('DeleteCertificateUseCase', () => {
  let useCase: DeleteCertificateUseCase;
  let certificateRepository: jest.Mocked<ICertificateRepository>;
  let storageProvider: jest.Mocked<IStorageProvider>;

  const mockCertificate = Certificate.create({
    id: 'cert-uuid-123',
    customerId: 1,
    category: CertificateCategory.SPORTSBOOK,
    type: CertificateType.CERTIFICADO_DE_SPORTSBOOK,
    fileUrl: 'path/to/sportsbook.pdf',
    uploadedById: 'admin-id',
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteCertificateUseCase,
        {
          provide: CERTIFICATE_REPOSITORY,
          useValue: {
            findById: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: 'IStorageProvider',
          useValue: {
            // Mock a delete method if it existed
            // delete: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get<DeleteCertificateUseCase>(DeleteCertificateUseCase);
    certificateRepository = module.get(CERTIFICATE_REPOSITORY);
    storageProvider = module.get('IStorageProvider');
  });

  it('should delete a certificate successfully', async () => {
    certificateRepository.findById.mockResolvedValue(mockCertificate);
    certificateRepository.delete.mockResolvedValue(undefined);

    await useCase.execute(mockCertificate.id);

    expect(certificateRepository.findById).toHaveBeenCalledWith(mockCertificate.id);
    expect(certificateRepository.delete).toHaveBeenCalledWith(mockCertificate.id);
    // expect(storageProvider.delete).toHaveBeenCalledWith(mockCertificate.fileUrl);
  });

  it('should throw NotFoundException if certificate does not exist', async () => {
    certificateRepository.findById.mockResolvedValue(null);

    await expect(useCase.execute('non-existent-id')).rejects.toThrow(
      NotFoundException,
    );
  });
}); 