/* eslint-disable */
import { Test, TestingModule } from '@nestjs/testing';
import { CreateCertificateUseCase } from '@/modules/customers/application/use-cases/create-certificate.use-case';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { FindCustomerByUuidUseCase } from '@/modules/customers/application/use-cases/find-customer-by-uuid.use-case';
import { CertificateCategory } from '@/modules/customers/domain/enums/certificate-category.enum';
import { CertificateType } from '@/modules/customers/domain/enums/certificate-type.enum';
import {
  Customer,
  CustomerStatus,
} from '@/modules/customers/domain/entities/customer.entity';
import { NotFoundException } from '@nestjs/common';
import { Certificate } from '@/modules/customers/domain/entities/certificate.entity';

describe('CreateCertificateUseCase', () => {
  let useCase: CreateCertificateUseCase;
  let certificateRepository: jest.Mocked<ICertificateRepository>;
  let storageProvider: jest.Mocked<IStorageProvider>;
  let findCustomerUseCase: jest.Mocked<FindCustomerByUuidUseCase>;
  let userRepository: jest.Mocked<any>;

  const mockCustomer: Customer = {
    id: 1,
    uuid: 'customer-uuid-123',
    razaoSocial: 'Test Customer',
    cnpj: '12345678000195',
    email: '<EMAIL>',
    status: CustomerStatus.ACTIVE,
    url: '',
    userId: 'user-id-customer',
    createdBy: 'admin',
    updatedBy: 'admin',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024,
    buffer: Buffer.from('test'),
    stream: jest.fn() as any,
    destination: '',
    filename: '',
    path: '',
  };

  beforeEach(async () => {
    const mockCertificateRepo = {
      create: jest.fn(),
      findById: jest.fn(),
      findByCustomerId: jest.fn(),
      delete: jest.fn(),
    };

    const mockStorageProvider = {
      upload: jest.fn(),
      getFileStream: jest.fn(),
      getFileUrl: jest.fn(),
      getDownloadUrl: jest.fn(),
    };

    const mockFindCustomerUseCase = {
      execute: jest.fn(),
    };

    const mockUserRepository = {
      findByKeycloakId: jest.fn(),
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateCertificateUseCase,
        {
          provide: CERTIFICATE_REPOSITORY,
          useValue: mockCertificateRepo,
        },
        {
          provide: 'IStorageProvider',
          useValue: mockStorageProvider,
        },
        {
          provide: FindCustomerByUuidUseCase,
          useValue: mockFindCustomerUseCase,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateCertificateUseCase>(CreateCertificateUseCase);
    certificateRepository = module.get(CERTIFICATE_REPOSITORY);
    storageProvider = module.get('IStorageProvider');
    findCustomerUseCase = module.get(FindCustomerByUuidUseCase);
    userRepository = module.get('UserRepository');
  });

  it('should create a certificate successfully', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);
    storageProvider.upload.mockResolvedValue(undefined);

    const mockUser = {
      id: 'user-admin-id',
      name: 'Admin User',
      email: '<EMAIL>',
      username: 'admin',
      password: '',
      role: 'USER',
      createdAt: new Date(),
      updatedAt: new Date(),
      keycloakId: 'user-admin-id',
    };

    userRepository.findByKeycloakId.mockResolvedValue(mockUser);

    const createdCertificate = Certificate.create({
      customerId: mockCustomer.id!,
      category: CertificateCategory.PAGAMENTOS,
      type: CertificateType.CERTIFICADO_DE_MEIOS_DE_PAGAMENTO,
      fileUrl: 'some/path/test.pdf',
      uploadedById: 'user-admin-id',
    });
    certificateRepository.create.mockResolvedValue(createdCertificate);

    const result = await useCase.execute({
      customerUuid: mockCustomer.uuid,
      uploadedById: 'user-admin-id',
      file: mockFile,
      data: {
        category: CertificateCategory.PAGAMENTOS,
        type: CertificateType.CERTIFICADO_DE_MEIOS_DE_PAGAMENTO,
      },
    });

    expect(findCustomerUseCase.execute).toHaveBeenCalledWith(mockCustomer.uuid);
    expect(userRepository.findByKeycloakId).toHaveBeenCalledWith(
      'user-admin-id',
    );
    expect(storageProvider.upload).toHaveBeenCalled();
    expect(certificateRepository.create).toHaveBeenCalled();
    expect(result.customerId).toBe(mockCustomer.id);
    expect(result.category).toBe(CertificateCategory.PAGAMENTOS);
  });

  it('should throw NotFoundException if customer does not exist', async () => {
    findCustomerUseCase.execute.mockResolvedValue(null as any);

    userRepository.findByKeycloakId.mockResolvedValue({
      id: 'user-admin-id',
      name: 'Admin User',
      email: '<EMAIL>',
      username: 'admin',
      password: '',
      role: 'USER',
      createdAt: new Date(),
      updatedAt: new Date(),
      keycloakId: 'user-admin-id',
    });

    await expect(
      useCase.execute({
        customerUuid: 'non-existent-uuid',
        uploadedById: 'user-admin-id',
        file: mockFile,
        data: {
          category: CertificateCategory.PAGAMENTOS,
          type: CertificateType.CERTIFICADO_DE_MEIOS_DE_PAGAMENTO,
        },
      }),
    ).rejects.toThrow(NotFoundException);
  });

  it('should call storageProvider with correct path', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);
    storageProvider.upload.mockResolvedValue(undefined);
    certificateRepository.create.mockImplementation(async (cert) => cert);

    userRepository.findByKeycloakId.mockResolvedValue({
      id: 'user-admin-id',
      name: 'Admin User',
      email: '<EMAIL>',
      username: 'admin',
      password: '',
      role: 'USER',
      createdAt: new Date(),
      updatedAt: new Date(),
      keycloakId: 'user-admin-id',
    });

    await useCase.execute({
      customerUuid: mockCustomer.uuid,
      uploadedById: 'user-admin-id',
      file: mockFile,
      data: {
        category: CertificateCategory.PAGAMENTOS,
        type: CertificateType.CERTIFICADO_DE_MEIOS_DE_PAGAMENTO,
      },
    });

    expect(storageProvider.upload).toHaveBeenCalledWith(
      mockFile.buffer,
      mockFile.mimetype,
      expect.stringContaining(`customers/${mockCustomer.uuid}/certificates/`),
    );
  });
});
