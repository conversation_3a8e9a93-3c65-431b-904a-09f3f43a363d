import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';

@Injectable()
export class DeleteCertificateUseCase {
  constructor(
    @Inject(CERTIFICATE_REPOSITORY)
    private readonly certificateRepository: ICertificateRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(id: string): Promise<void> {
    // 1. Encontrar o certificado
    const certificate = await this.certificateRepository.findById(id);
    if (!certificate) {
      throw new NotFoundException('Certificado não encontrado.');
    }

    // 2. Deletar o arquivo do S3
    // O ideal é ter um método delete no storage provider.
    // Como não tem, vamos pular essa parte por enquanto, mas deixo o comentário.
    // await this.storageProvider.delete(certificate.fileUrl);

    // 3. Deletar do banco de dados
    await this.certificateRepository.delete(id);
  }
} 