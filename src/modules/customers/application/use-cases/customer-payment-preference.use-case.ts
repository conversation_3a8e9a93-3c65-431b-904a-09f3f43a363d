import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { CustomerPaymentPreference } from '../../domain/entities/customer-payment-preference.entity';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';
import { CreateCustomerPaymentPreferenceDto, UpdateCustomerPaymentPreferenceDto } from '../../infrastructure/dtos/customer-payment-preference.dto';

@Injectable()
export class CustomerPaymentPreferenceUseCase {
  constructor(
    @Inject('ICustomerPaymentPreferenceRepository')
    private readonly repository: ICustomerPaymentPreferenceRepository,
  ) {}

  async create(customerId: string, dto: CreateCustomerPaymentPreferenceDto): Promise<CustomerPaymentPreference> {
    const now = new Date();
    return this.repository.create({
      customerId,
      department: dto.department,
      responsible: dto.responsible,
      paymentMethodId: dto.paymentMethodId,
      paymentCondition: dto.paymentCondition,
      billingPreference: dto.billingPreference,
      createdAt: now,
      updatedAt: now,
    } as any);
  }

  async findAll(): Promise<CustomerPaymentPreference[]> {
    return this.repository.findAll();
  }

  async findById(id: string): Promise<CustomerPaymentPreference> {
    const preference = await this.repository.findById(id);
    if (!preference) {
      throw new NotFoundException('Customer payment preference not found');
    }
    return preference;
  }

  async findByCustomerId(customerId: string): Promise<CustomerPaymentPreference[]> {
    return await this.repository.findByCustomerId(customerId);
  }

  async update(id: string, dto: UpdateCustomerPaymentPreferenceDto): Promise<CustomerPaymentPreference> {
    const preference = await this.findById(id);
    return this.repository.update(id, {
      ...dto,
      updatedAt: new Date(),
    });
  }

  async delete(id: string): Promise<void> {
    const preference = await this.findById(id);
    await this.repository.delete(id);
  }
} 