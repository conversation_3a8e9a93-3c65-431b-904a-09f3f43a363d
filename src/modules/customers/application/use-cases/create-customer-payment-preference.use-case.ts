import { Injectable, Inject } from '@nestjs/common';
import { CreateCustomerPaymentPreferenceDto } from '../../infrastructure/dtos/customer-payment-preference.dto';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';
import { CustomerPaymentPreference } from '../../domain/entities/customer-payment-preference.entity';

@Injectable()
export class CreateCustomerPaymentPreferenceUseCase {
  constructor(
    @Inject('ICustomerPaymentPreferenceRepository')
    private readonly repository: ICustomerPaymentPreferenceRepository,
  ) {}

  async execute(dto: CreateCustomerPaymentPreferenceDto): Promise<CustomerPaymentPreference> {
    return this.repository.create({ ...dto });
  }
} 