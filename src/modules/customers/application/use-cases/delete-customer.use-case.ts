import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';

@Injectable()
export class DeleteCustomerUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
  ) {}

  async execute(uuid: string): Promise<void> {
    const customer = await this.customerRepository.findByUuid(uuid);
    if (!customer) {
      throw new NotFoundException('Cliente não encontrado.');
    }
    await this.customerRepository.delete(uuid);
  }
}
