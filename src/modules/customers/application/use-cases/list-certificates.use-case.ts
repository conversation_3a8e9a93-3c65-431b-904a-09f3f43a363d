import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { FindCustomerByUuidUseCase } from './find-customer-by-uuid.use-case';
import { CertificateResponseDto } from '../../infrastructure/dtos/certificate.response.dto';
import { CERTIFICATES_BY_CATEGORY } from '../../domain/constants/certificates-by-category';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';

export enum CertificateStatus {
  ACTIVE = 'ATIVO',
  PENDING = 'PENDENTE',
}

export interface ListedCertificateDto extends Partial<CertificateResponseDto> {
  status: CertificateStatus;
  category: CertificateCategory;
  type: CertificateType;
  fullFileUrl?: string; // URL assinada para download
}

export interface ListCertificatesResponseDto {
  category: CertificateCategory;
  certificates: ListedCertificateDto[];
}

@Injectable()
export class ListCertificatesUseCase {
  constructor(
    @Inject(CERTIFICATE_REPOSITORY)
    private readonly certificateRepository: ICertificateRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
    private readonly findCustomerByUuidUseCase: FindCustomerByUuidUseCase,
    @Inject('UserRepository')
    private readonly userRepository: UserRepository,
  ) {}

  async execute(customerUuid: string): Promise<ListCertificatesResponseDto[]> {
    const customer = await this.findCustomerByUuidUseCase.execute(customerUuid);
    if (!customer) {
      throw new NotFoundException('Cliente não encontrado.');
    }

    const uploadedCerts = await this.certificateRepository.findByCustomerId(
      customer.id!,
    );
    const uploadedCertMap = new Map(
      uploadedCerts.map((cert) => [`${cert.category}:${cert.type}`, cert]),
    );

    const userIds = uploadedCerts
      .map((cert) => cert.uploadedById)
      .filter((id) => id);
    const users = await this.userRepository.findByIds(userIds);
    const userMap = new Map(users.map((user) => [user.id, user.name]));

    const result: ListCertificatesResponseDto[] = [];

    for (const category in CERTIFICATES_BY_CATEGORY) {
      const categoryEnum = category as CertificateCategory;
      const requiredTypes =
        CERTIFICATES_BY_CATEGORY[
          categoryEnum as keyof typeof CERTIFICATES_BY_CATEGORY
        ];

      const certificates: ListedCertificateDto[] = await Promise.all(
        requiredTypes.map(async (type) => {
          const key = `${categoryEnum}:${type}`;
          const uploadedCert = uploadedCertMap.get(key);

          if (uploadedCert) {
            const uploadedByName = userMap.get(uploadedCert.uploadedById);
            
            try {
              // Gerar URL de download para o certificado
              const fileName = uploadedCert.fileUrl.split('/').pop() || 'certificate.pdf';
              const downloadUrl = await this.storageProvider.getDownloadUrl(
                uploadedCert.fileUrl,
                fileName,
                3600 // 1 hora de expiração
              );

              return {
                ...CertificateResponseDto.fromEntity(uploadedCert),
                status: CertificateStatus.ACTIVE,
                uploadedByName: uploadedByName || 'Usuário desconhecido',
                fullFileUrl: downloadUrl,
              };
            } catch (error) {
              console.error(`Erro ao gerar URL de download para certificado ${uploadedCert.id}:`, error);
              // Retorna o certificado sem URL de download em caso de erro
              return {
                ...CertificateResponseDto.fromEntity(uploadedCert),
                status: CertificateStatus.ACTIVE,
                uploadedByName: uploadedByName || 'Usuário desconhecido',
                fullFileUrl: undefined,
              };
            }
          } else {
            return {
              category: categoryEnum,
              type: type,
              status: CertificateStatus.PENDING,
            };
          }
        })
      );

      result.push({
        category: categoryEnum,
        certificates,
      });
    }

    return result;
  }
}
