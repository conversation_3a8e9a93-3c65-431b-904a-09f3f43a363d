import { Test, TestingModule } from '@nestjs/testing';
import { ListCertificatesUseCase, CertificateStatus } from './list-certificates.use-case';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { FindCustomerByUuidUseCase } from './find-customer-by-uuid.use-case';
import { Customer } from '../../domain/entities/customer.entity';
import { CustomerStatus as DomainCustomerStatus } from '../../domain/entities/customer.entity';
import { Certificate } from '../../domain/entities/certificate.entity';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';
import { NotFoundException } from '@nestjs/common';
import { CERTIFICATES_BY_CATEGORY } from '../../domain/constants/certificates-by-category';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { User } from '@/core/domain/user.entity';
import { Role } from '@/core/domain/role.enum';

describe('ListCertificatesUseCase', () => {
  let useCase: ListCertificatesUseCase;
  let certificateRepository: jest.Mocked<ICertificateRepository>;
  let storageProvider: jest.Mocked<IStorageProvider>;
  let findCustomerUseCase: jest.Mocked<FindCustomerByUuidUseCase>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockCustomer: Customer = {
    id: 1,
    uuid: 'customer-uuid-123',
    razaoSocial: 'Test Customer',
    cnpj: '12345678000195',
    email: '<EMAIL>',
    status: DomainCustomerStatus.ACTIVE,
    url: '',
    userId: 'user-id-customer',
    createdBy: 'admin',
    updatedBy: 'admin',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUser = new User(
    'admin-id',
    '<EMAIL>',
    'Admin User',
    'hashed-password',
    Role.ADMIN,
    new Date(),
    new Date(),
    'keycloak-id'
  );

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListCertificatesUseCase,
        {
          provide: CERTIFICATE_REPOSITORY,
          useValue: {
            findByCustomerId: jest.fn(),
          },
        },
        {
          provide: 'IStorageProvider',
          useValue: {
            getDownloadUrl: jest.fn(),
          },
        },
        {
          provide: FindCustomerByUuidUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: 'UserRepository',
          useValue: {
            findByIds: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get<ListCertificatesUseCase>(ListCertificatesUseCase);
    certificateRepository = module.get(CERTIFICATE_REPOSITORY);
    storageProvider = module.get('IStorageProvider');
    findCustomerUseCase = module.get(FindCustomerByUuidUseCase);
    userRepository = module.get('UserRepository');
  });

  it('should return a list of active and pending certificates with download URLs', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);

    const uploadedCert = Certificate.create({
      customerId: mockCustomer.id!,
      category: CertificateCategory.FORNECEDORES_JOGOS,
      type: CertificateType.CERTIFICADO_DE_JOGO,
      fileUrl: 'customers/customer-uuid-123/certificates/jogo.pdf',
      uploadedById: 'admin-id',
    });
    certificateRepository.findByCustomerId.mockResolvedValue([uploadedCert]);
    userRepository.findByIds.mockResolvedValue([mockUser]);
    
    const mockDownloadUrl = 'https://s3.amazonaws.com/bucket/signed-url';
    storageProvider.getDownloadUrl.mockResolvedValue(mockDownloadUrl);

    const result = await useCase.execute(mockCustomer.uuid);

    const jogosCategory = result.find(
      (r) => r.category === CertificateCategory.FORNECEDORES_JOGOS,
    );
    const jogoCert = jogosCategory?.certificates.find(
      (c) => c.type === CertificateType.CERTIFICADO_DE_JOGO,
    );
    const rngCert = jogosCategory?.certificates.find(
      (c) => c.type === CertificateType.CERTIFICADO_RNG,
    );

    expect(result.length).toBe(Object.keys(CERTIFICATES_BY_CATEGORY).length);
    expect(jogoCert?.status).toBe(CertificateStatus.ACTIVE);
    expect(jogoCert?.id).toBe(uploadedCert.id);
    expect(jogoCert?.fullFileUrl).toBe(mockDownloadUrl);
    expect(jogoCert?.uploadedByName).toBe('Admin User');
    expect(rngCert?.status).toBe(CertificateStatus.PENDING);
    expect(rngCert?.fullFileUrl).toBeUndefined();
    
    expect(storageProvider.getDownloadUrl).toHaveBeenCalledWith(
      'customers/customer-uuid-123/certificates/jogo.pdf',
      'jogo.pdf',
      3600
    );
  });

  it('should return all certificates as pending if none are uploaded', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);
    certificateRepository.findByCustomerId.mockResolvedValue([]);
    userRepository.findByIds.mockResolvedValue([]);

    const result = await useCase.execute(mockCustomer.uuid);
    const allCerts = result.flatMap((r) => r.certificates);

    expect(allCerts.every((c) => c.status === CertificateStatus.PENDING)).toBe(true);
    expect(allCerts.every((c) => c.fullFileUrl === undefined)).toBe(true);
  });

  it('should handle error when generating download URL', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);

    const uploadedCert = Certificate.create({
      customerId: mockCustomer.id!,
      category: CertificateCategory.FORNECEDORES_JOGOS,
      type: CertificateType.CERTIFICADO_DE_JOGO,
      fileUrl: 'customers/customer-uuid-123/certificates/jogo.pdf',
      uploadedById: 'admin-id',
    });
    certificateRepository.findByCustomerId.mockResolvedValue([uploadedCert]);
    userRepository.findByIds.mockResolvedValue([mockUser]);
    
    // Simular erro na geração da URL
    storageProvider.getDownloadUrl.mockRejectedValue(new Error('S3 error'));

    const result = await useCase.execute(mockCustomer.uuid);

    const jogosCategory = result.find(
      (r) => r.category === CertificateCategory.FORNECEDORES_JOGOS,
    );
    const jogoCert = jogosCategory?.certificates.find(
      (c) => c.type === CertificateType.CERTIFICADO_DE_JOGO,
    );

    expect(jogoCert?.status).toBe(CertificateStatus.ACTIVE);
    expect(jogoCert?.id).toBe(uploadedCert.id);
    expect(jogoCert?.fullFileUrl).toBeUndefined(); // Deve ser undefined devido ao erro
    expect(jogoCert?.uploadedByName).toBe('Admin User');
  });

  it('should extract filename from fileUrl path', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);

    const uploadedCert = Certificate.create({
      customerId: mockCustomer.id!,
      category: CertificateCategory.FORNECEDORES_JOGOS,
      type: CertificateType.CERTIFICADO_DE_JOGO,
      fileUrl: 'customers/customer-uuid-123/certificates/**********-certificate.pdf',
      uploadedById: 'admin-id',
    });
    certificateRepository.findByCustomerId.mockResolvedValue([uploadedCert]);
    userRepository.findByIds.mockResolvedValue([mockUser]);
    
    const mockDownloadUrl = 'https://s3.amazonaws.com/bucket/signed-url';
    storageProvider.getDownloadUrl.mockResolvedValue(mockDownloadUrl);

    await useCase.execute(mockCustomer.uuid);

    expect(storageProvider.getDownloadUrl).toHaveBeenCalledWith(
      'customers/customer-uuid-123/certificates/**********-certificate.pdf',
      '**********-certificate.pdf',
      3600
    );
  });

  it('should use fallback filename if path parsing fails', async () => {
    findCustomerUseCase.execute.mockResolvedValue(mockCustomer);

    const uploadedCert = Certificate.create({
      customerId: mockCustomer.id!,
      category: CertificateCategory.FORNECEDORES_JOGOS,
      type: CertificateType.CERTIFICADO_DE_JOGO,
      fileUrl: 'invalid-path',
      uploadedById: 'admin-id',
    });
    certificateRepository.findByCustomerId.mockResolvedValue([uploadedCert]);
    userRepository.findByIds.mockResolvedValue([mockUser]);
    
    const mockDownloadUrl = 'https://s3.amazonaws.com/bucket/signed-url';
    storageProvider.getDownloadUrl.mockResolvedValue(mockDownloadUrl);

    await useCase.execute(mockCustomer.uuid);

    expect(storageProvider.getDownloadUrl).toHaveBeenCalledWith(
      'invalid-path',
      'invalid-path',
      3600
    );
  });

  it('should throw NotFoundException if customer does not exist', async () => {
    findCustomerUseCase.execute.mockResolvedValue(null as any);

    await expect(useCase.execute('non-existent-uuid')).rejects.toThrow(
      NotFoundException,
    );
  });
}); 