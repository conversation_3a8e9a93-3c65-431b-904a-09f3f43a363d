import {
  Controller,
  Post,
  Param,
  UseInterceptors,
  UploadedFile,
  Body,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Get,
  Delete,
  Patch,
} from '@nestjs/common';
import { Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { CreateCertificateDto } from '../../infrastructure/dtos/create-certificate.dto';
import { CreateCertificateUseCase } from '../use-cases/create-certificate.use-case';
import { CertificateResponseDto } from '../../infrastructure/dtos/certificate.response.dto';
import { ListCertificatesUseCase } from '../use-cases/list-certificates.use-case';
import { DeleteCertificateUseCase } from '../use-cases/delete-certificate.use-case';
import { CustomerUuidRouteParamDto } from '../../infrastructure/dtos/customer-uuid.dto';
import { UpdateCertificateUseCase } from '../use-cases/update-certificate.use-case';
import { UpdateCertificateDto } from '../../infrastructure/dtos/update-certificate.dto';
import { FindCustomerByUuidUseCase } from '@/modules/customers/application/use-cases/find-customer-by-uuid.use-case';

@ApiTags('Customers -> Certificates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('core/customers/:customerUuid/certificates')
export class CertificateController {
  constructor(
    private readonly createCertificateUseCase: CreateCertificateUseCase,
    private readonly listCertificatesUseCase: ListCertificatesUseCase,
    private readonly deleteCertificateUseCase: DeleteCertificateUseCase,
    private readonly updateCertificateUseCase: UpdateCertificateUseCase,
    private readonly findCustomerByUuidUseCase: FindCustomerByUuidUseCase,
  ) {}

  @Post()
  @Roles(Role.ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload de um certificado para um cliente' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        category: {
          type: 'string',
          description: 'Categoria do certificado',
          example: 'FORNECEDORES_JOGOS',
        },
        type: {
          type: 'string',
          description: 'Tipo do certificado',
          example: 'CERTIFICADO_DE_JOGO',
        },
        notes: {
          type: 'string',
          description: 'Observações sobre o certificado (opcional)',
          example: 'Válido até 2025',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Arquivo do certificado.',
        },
      },
      required: ['category', 'type', 'file'],
    },
  })
  @ApiResponse({ status: 201, type: CertificateResponseDto })
  async createCertificate(
    @Param() params: CustomerUuidRouteParamDto,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: CreateCertificateDto,
    @Req()
    req: Request & { user: { id: string; email: string; username: string } },
  ): Promise<CertificateResponseDto> {
    const certificate = await this.createCertificateUseCase.execute({
      customerUuid: params.customerUuid,
      uploadedById: req.user.id,
      file,
      data: body,
      userInfo: {
        email: req.user.email || req.user.username || '<EMAIL>',
        username: req.user.username || req.user.email || 'Usuário Keycloak',
      },
    });
    return CertificateResponseDto.fromEntity(certificate);
  }

  @Patch(':certificateId')
  @Roles(Role.ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Atualizar um certificado' })
  @ApiResponse({ status: 200, type: CertificateResponseDto })
  async updateCertificate(
    @Param('certificateId') certificateId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: UpdateCertificateDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<CertificateResponseDto> {
    const certificate = await this.updateCertificateUseCase.execute({
      id: certificateId,
      updatedBy: req.user.id,
      file,
      data: body,
    });
    return CertificateResponseDto.fromEntity(certificate);
  }

  @Get()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({
    summary: 'Lista os certificados (pendentes e ativos) de um cliente',
  })
  async listCertificates(@Param() params: CustomerUuidRouteParamDto) {
    // A visibilidade (cliente vs admin) será tratada no frontend por enquanto.
    return this.listCertificatesUseCase.execute(params.customerUuid);
  }

  @Delete(':certificateId')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deleta um certificado' })
  async deleteCertificate(
    @Param('certificateId') certificateId: string,
  ): Promise<void> {
    await this.deleteCertificateUseCase.execute(certificateId);
  }
}
