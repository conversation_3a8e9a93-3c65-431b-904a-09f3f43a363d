import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CustomerPaymentPreferenceUseCase } from '../use-cases/customer-payment-preference.use-case';
import {
  CustomerPaymentPreferenceDto,
  CreateCustomerPaymentPreferenceDto,
  UpdateCustomerPaymentPreferenceDto,
} from '../../infrastructure/dtos/customer-payment-preference.dto';

@ApiTags('Customer Payment Preferences')
@Controller('core/customer-payment-preferences')
export class CustomerPaymentPreferenceController {
  constructor(private readonly useCase: CustomerPaymentPreferenceUseCase) {}

  @Post(':id')
  @ApiOperation({ summary: 'Create a new customer payment preference' })
  @ApiResponse({
    status: 201,
    description:
      'The customer payment preference has been successfully created.',
    type: CustomerPaymentPreferenceDto,
  })
  async create(
    @Param('id', ParseUUIDPipe) customerId: string,
    @Body() createDto: CreateCustomerPaymentPreferenceDto,
  ) {
    return await this.useCase.create(customerId, createDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all customer payment preferences' })
  @ApiResponse({
    status: 200,
    description: 'Return all customer payment preferences.',
    type: [CustomerPaymentPreferenceDto],
  })
  async findAll() {
    return await this.useCase.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get customer payment preferences by customer id' })
  @ApiResponse({
    status: 200,
    description: 'Return the customer payment preferences for the customer.',
    type: [CustomerPaymentPreferenceDto],
  })
  async findOne(@Param('id', ParseUUIDPipe) customerId: string) {
    return await this.useCase.findByCustomerId(customerId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a customer payment preference' })
  @ApiResponse({
    status: 200,
    description:
      'The customer payment preference has been successfully updated.',
    type: CustomerPaymentPreferenceDto,
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateCustomerPaymentPreferenceDto,
  ) {
    return await this.useCase.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a customer payment preference' })
  @ApiResponse({
    status: 200,
    description:
      'The customer payment preference has been successfully deleted.',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    return await this.useCase.delete(id);
  }
}
