import { Test, TestingModule } from '@nestjs/testing';
import { CertificateController } from './certificate.controller';
import { CreateCertificateUseCase } from '../use-cases/create-certificate.use-case';
import { ListCertificatesUseCase } from '../use-cases/list-certificates.use-case';
import { DeleteCertificateUseCase } from '../use-cases/delete-certificate.use-case';
import { FindCustomerByUuidUseCase } from '../use-cases/find-customer-by-uuid.use-case';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';
import { UpdateCertificateUseCase } from '../use-cases/update-certificate.use-case';
import { Certificate } from '../../domain/entities/certificate.entity';

describe('CertificateController', () => {
  let controller: CertificateController;
  let createUseCase: jest.Mocked<CreateCertificateUseCase>;
  let listUseCase: jest.Mocked<ListCertificatesUseCase>;
  let deleteUseCase: jest.Mocked<DeleteCertificateUseCase>;
  let updateUseCase: jest.Mocked<UpdateCertificateUseCase>;
  let findCustomerUseCase: jest.Mocked<FindCustomerByUuidUseCase>;

  const mockCertificate = Certificate.create({
    id: 'cert-uuid-123',
    customerId: 1,
    category: CertificateCategory.PAGAMENTOS,
    type: CertificateType.CERTIFICADO_DE_INTEGRACAO,
    fileUrl: 'path/to/file.pdf',
    uploadedById: 'admin-user-id',
  });

  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024,
    buffer: Buffer.from('test'),
    stream: jest.fn() as any,
    destination: '',
    filename: '',
    path: '',
  };

  const mockRequest = {
    user: { id: 'admin-user-id' },
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CertificateController],
      providers: [
        {
          provide: CreateCertificateUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListCertificatesUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteCertificateUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UpdateCertificateUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: FindCustomerByUuidUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CertificateController>(CertificateController);
    createUseCase = module.get(CreateCertificateUseCase);
    listUseCase = module.get(ListCertificatesUseCase);
    deleteUseCase = module.get(DeleteCertificateUseCase);
    updateUseCase = module.get(UpdateCertificateUseCase);
    findCustomerUseCase = module.get(FindCustomerByUuidUseCase);
  });

  describe('createCertificate', () => {
    it('should call CreateCertificateUseCase with correct parameters', async () => {
      const customerUuid = 'customer-123';
      const createDto = {
        category: CertificateCategory.PAGAMENTOS,
        type: CertificateType.CERTIFICADO_DE_INTEGRACAO,
        file: mockFile,
      };

      createUseCase.execute.mockResolvedValue(mockCertificate);

      await controller.createCertificate(
        { customerUuid: customerUuid },
        mockFile,
        createDto,
        mockRequest,
      );

      expect(createUseCase.execute).toHaveBeenCalledWith({
        customerUuid,
        uploadedById: mockRequest.user.id,
        file: mockFile,
        data: createDto,
        userInfo: {
          email: mockRequest.user.email || mockRequest.user.username || '<EMAIL>',
          username: mockRequest.user.username || mockRequest.user.email || 'Usuário Keycloak',
        },
      });
    });
  });

  describe('updateCertificate', () => {
    it('should call UpdateCertificateUseCase with correct parameters', async () => {
      const certificateId = 'cert-456';
      const updateDto = { notes: 'updated notes' };

      updateUseCase.execute.mockResolvedValue(mockCertificate);

      await controller.updateCertificate(
        certificateId,
        mockFile,
        updateDto,
        mockRequest,
      );

      expect(updateUseCase.execute).toHaveBeenCalledWith({
        id: certificateId,
        file: mockFile,
        data: updateDto,
        updatedBy: mockRequest.user.id,
      });
    });
  });

  describe('listCertificates', () => {
    it('should call ListCertificatesUseCase with correct customer UUID', async () => {
      const customerUuid = 'customer-123';
      await controller.listCertificates({ customerUuid: customerUuid });
      expect(listUseCase.execute).toHaveBeenCalledWith(customerUuid);
    });
  });

  describe('deleteCertificate', () => {
    it('should call DeleteCertificateUseCase with correct certificate ID', async () => {
      const certificateId = 'cert-456';
      await controller.deleteCertificate(certificateId);
      expect(deleteUseCase.execute).toHaveBeenCalledWith(certificateId);
    });
  });
}); 