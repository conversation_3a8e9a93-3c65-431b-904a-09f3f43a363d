import { Module, forwardRef } from '@nestjs/common';
import { PrismaModule } from '@/infrastructure/prisma/prisma.module';
import { CustomerController } from './application/controllers/customer.controller';
import { FindCustomerByUuidUseCase } from './application/use-cases/find-customer-by-uuid.use-case';
import { ListCustomersUseCase } from './application/use-cases/list-customers.use-case';
import { CustomerContractController } from './contracts/application/controllers/customer-contract.controller';
import { CustomerContractService } from './contracts/application/services/customer-contract.service';
import { ContractModule } from '../documents/contract.module';
import { CustomerDocumentController } from './documents/application/controllers/customer-document.controller';
import { CustomerDocumentRepository } from './documents/infrastructure/repositories/customer-document.repository';
import { CreateCustomerDocumentUseCase } from './documents/application/use-cases/create-customer-document.use-case';
import { DeleteCustomerDocumentUseCase } from './documents/application/use-cases/delete-customer-document.use-case';
import { GetCustomerDocumentUseCase } from './documents/application/use-cases/get-customer-document.use-case';
import { ListCustomerDocumentUseCase } from './documents/application/use-cases/list-customer-document.use-case';
import { UpdateCustomerDocumentUseCase } from './documents/application/use-cases/update-customer-document.use-case';
import { DownloadCustomerDocumentUseCase } from './documents/application/use-cases/download-customer-document.use-case';
import { MulterModule } from '@nestjs/platform-express';
import { CUSTOMER_DOCUMENT_REPOSITORY } from './documents/domain/constants/tokens';
import { CustomerRepository } from './infrastructure/repositories/customer.repository';
import { CustomerContractRepository } from './contracts/infrastructure/repositories/customer-contract.repository';
import { CreateCustomerUseCase } from './application/use-cases/create-customer.use-case';
import { DeleteCustomerUseCase } from './application/use-cases/delete-customer.use-case';
import { UpdateCustomerUseCase } from './application/use-cases/update-customer.use-case';
import { CustomerPaymentPreferenceController } from './application/controllers/customer-payment-preference.controller';
import { CustomerPaymentPreferenceRepository } from './infrastructure/repositories/customer-payment-preference.repository';
import { CustomerPaymentPreferenceUseCase } from './application/use-cases/customer-payment-preference.use-case';
import { CreateCustomerPaymentPreferenceUseCase } from './application/use-cases/create-customer-payment-preference.use-case';
import { ListCustomerPaymentPreferencesUseCase } from './application/use-cases/list-customer-payment-preferences.use-case';
import { GetCustomerPaymentPreferenceUseCase } from './application/use-cases/get-customer-payment-preference.use-case';
import { UpdateCustomerPaymentPreferenceUseCase } from './application/use-cases/update-customer-payment-preference.use-case';
import { DeleteCustomerPaymentPreferenceUseCase } from './application/use-cases/delete-customer-payment-preference.use-case';
import { S3StorageProvider } from '@/infrastructure/aws/s3/s3-storage.provider';
import { AwsModule } from '@/infrastructure/aws/aws.module';
import { CreateServiceUseCase } from '../../core/application/use-cases/service/create-service.use-case';
import { ListServicesUseCase } from '../../core/application/use-cases/service/list-services.use-case';
import { PrismaServiceRepository } from '../../infrastructure/repositories/prisma-service.repository';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { ContactsModule } from './contacts/contacts.module';
import { CertificateController } from './application/controllers/certificate.controller';
import { CreateCertificateUseCase } from './application/use-cases/create-certificate.use-case';
import { ListCertificatesUseCase } from './application/use-cases/list-certificates.use-case';
import { DeleteCertificateUseCase } from './application/use-cases/delete-certificate.use-case';
import { UpdateCertificateUseCase } from './application/use-cases/update-certificate.use-case';
import {
  CERTIFICATE_REPOSITORY,
} from '@/core/ports/repositories/certificate.repository.port';
import { PrismaCertificateRepository } from '@/infrastructure/repositories/prisma-certificate.repository';

@Module({
  imports: [
    PrismaModule,
    MulterModule.register({
      limits: {
        fileSize: 20 * 1024 * 1024, // 20MB
      },
    }),
    AwsModule,
    UsersModule,
    forwardRef(() => AuthModule),
    ContactsModule,
    ContractModule,
  ],
  controllers: [
    CustomerController,
    CustomerContractController,
    CustomerDocumentController,
    CustomerPaymentPreferenceController,
    CertificateController,
  ],
  providers: [
    {
      provide: 'ICustomerRepository',
      useClass: CustomerRepository,
    },
    {
      provide: 'ICustomerContractRepository',
      useClass: CustomerContractRepository,
    },
    {
      provide: CUSTOMER_DOCUMENT_REPOSITORY,
      useClass: CustomerDocumentRepository,
    },
    CreateCustomerDocumentUseCase,
    DeleteCustomerDocumentUseCase,
    GetCustomerDocumentUseCase,
    ListCustomerDocumentUseCase,
    UpdateCustomerDocumentUseCase,
    DownloadCustomerDocumentUseCase,
    FindCustomerByUuidUseCase,
    ListCustomersUseCase,
    CustomerContractService,
    CreateCustomerUseCase,
    DeleteCustomerUseCase,
    UpdateCustomerUseCase,
    {
      provide: 'ICustomerPaymentPreferenceRepository',
      useClass: CustomerPaymentPreferenceRepository,
    },
    CustomerPaymentPreferenceUseCase,
    CreateCustomerPaymentPreferenceUseCase,
    ListCustomerPaymentPreferencesUseCase,
    GetCustomerPaymentPreferenceUseCase,
    UpdateCustomerPaymentPreferenceUseCase,
    DeleteCustomerPaymentPreferenceUseCase,
    {
      provide: 'SERVICE_REPOSITORY',
      useClass: PrismaServiceRepository,
    },
    {
      provide: 'IStorageProvider',
      useClass: S3StorageProvider,
    },
    {
      provide: CERTIFICATE_REPOSITORY,
      useClass: PrismaCertificateRepository,
    },
    CreateCertificateUseCase,
    ListCertificatesUseCase,
    DeleteCertificateUseCase,
    UpdateCertificateUseCase,
    CreateServiceUseCase,
    ListServicesUseCase,
  ],
  exports: [
    CreateCustomerDocumentUseCase,
    DeleteCustomerDocumentUseCase,
    GetCustomerDocumentUseCase,
    ListCustomerDocumentUseCase,
    UpdateCustomerDocumentUseCase,
    DownloadCustomerDocumentUseCase,
    FindCustomerByUuidUseCase,
    ListCustomersUseCase,
    CustomerContractService,
    CreateCustomerUseCase,
    DeleteCustomerUseCase,
    UpdateCustomerUseCase,
    CustomerPaymentPreferenceUseCase,
    CreateCustomerPaymentPreferenceUseCase,
    ListCustomerPaymentPreferencesUseCase,
    GetCustomerPaymentPreferenceUseCase,
    UpdateCustomerPaymentPreferenceUseCase,
    DeleteCustomerPaymentPreferenceUseCase,
    CreateServiceUseCase,
    ListServicesUseCase,
  ],
})
export class CustomersModule { }
