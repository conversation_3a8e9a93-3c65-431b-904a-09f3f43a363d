export interface PasswordResetToken {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PasswordResetTokenRepository {
  create(
    data: Omit<PasswordResetToken, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<PasswordResetToken>;
  findByToken(token: string): Promise<PasswordResetToken | null>;
  markAsUsed(token: string): Promise<void>;
  deleteExpiredTokens(): Promise<void>;
  deleteByToken(token: string): Promise<void>;
  deleteByUserId(userId: string): Promise<void>;
}
