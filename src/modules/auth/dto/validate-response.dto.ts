import { ApiProperty } from '@nestjs/swagger';

export class ValidateResponseDto {
  @ApiProperty({
    description: 'Mensagem de sucesso',
    example: 'Token válido',
  })
  message: string;

  @ApiProperty({
    description: 'Informações do usuário autenticado',
    example: {
      sub: '123e4567-e89b-12d3-a456-426614174000',
      email: '<EMAIL>',
      role: 'ADMIN',
    },
  })
  user: {
    sub: string;
    email: string;
    role: 'ADMIN';
  };
}
