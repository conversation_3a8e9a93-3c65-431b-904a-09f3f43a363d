import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../auth.service';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { UsersService } from '../../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { KeycloakService } from '../../../infrastructure/keycloak/keycloak.service';
import { EventPublisherService } from '../../../infrastructure/events/event-publisher.service';
import { KeycloakIdentityProviderService } from '../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { ValidationPipe } from '@nestjs/common';
import { APP_PIPE } from '@nestjs/core';
import { CustomerRepository } from '../../customers/infrastructure/repositories/customer.repository';
import { EmployeeService } from '../../finance/employee/employee.service';
import { SupplierAuthService } from '../supplier-auth/supplier-auth.service';
import { PrismaSupplierRepository } from '../../../infrastructure/repositories/prisma-supplier.repository';
import { PrismaEmployeeRepository } from '../../../infrastructure/repositories/prisma-employee.repository';

interface MockUser {
  id: string;
  email: string;
}

interface MockUserRepository {
  findByEmail: jest.Mock<Promise<MockUser | null>, [string]>;
}

interface MockTokenResponse {
  id: string;
  token: string;
  expiresAt: Date;
}

interface CreateTokenData {
  token: string;
  expiresAt: Date;
  userId: string;
}

interface MockPasswordResetTokenRepository {
  create: jest.Mock<Promise<MockTokenResponse>, [CreateTokenData]>;
  findByToken: jest.Mock;
  markAsUsed: jest.Mock;
  deleteExpiredTokens: jest.Mock;
  deleteByToken: jest.Mock;
  deleteByUserId: jest.Mock;
  mock: {
    calls: Array<[CreateTokenData]>;
  };
}

interface MockEmailService {
  sendPasswordResetEmail: jest.Mock<Promise<void>, [string, string]>;
}

interface MockUsersService {
  hashPassword: jest.Mock<Promise<string>, [string]>;
}

interface JwtPayload {
  sub: string;
  email: string;
}

interface MockJwtService {
  sign: jest.Mock<string, [JwtPayload]>;
  verify: jest.Mock;
}

interface MockKeycloakService {
  login: jest.Mock;
  logout: jest.Mock;
  refreshToken: jest.Mock;
  validateToken: jest.Mock;
  getUserInfo: jest.Mock;
}

describe('AuthService - Forgot Password', () => {
  let service: AuthService;
  let mockUserRepository: MockUserRepository;
  let mockPasswordResetTokenRepository: MockPasswordResetTokenRepository;
  let mockEmailService: MockEmailService;
  let mockUsersService: MockUsersService;
  let mockJwtService: MockJwtService;
  let mockKeycloakService: MockKeycloakService;

  beforeEach(async () => {
    mockUserRepository = {
      findByEmail: jest
        .fn<Promise<MockUser | null>, [string]>()
        .mockImplementation((_email: string) =>
          Promise.resolve<MockUser | null>(null),
        ),
    };

    mockPasswordResetTokenRepository = {
      create: jest
        .fn<Promise<MockTokenResponse>, [CreateTokenData]>()
        .mockImplementation((_data: CreateTokenData) =>
          Promise.resolve<MockTokenResponse>({
            id: '',
            token: '',
            expiresAt: new Date(),
          }),
        ),
      findByToken: jest.fn(),
      markAsUsed: jest.fn(),
      deleteExpiredTokens: jest.fn(),
      deleteByToken: jest.fn(),
      deleteByUserId: jest.fn(),
      mock: {
        calls: [],
      },
    };

    mockEmailService = {
      sendPasswordResetEmail: jest
        .fn<Promise<void>, [string, string]>()
        .mockImplementation((_email: string, _token: string) =>
          Promise.resolve(),
        ),
    };

    mockUsersService = {
      hashPassword: jest
        .fn<Promise<string>, [string]>()
        .mockImplementation((_password: string) => Promise.resolve('')),
    };

    mockJwtService = {
      sign: jest
        .fn<string, [JwtPayload]>()
        .mockImplementation((_payload: JwtPayload) => ''),
      verify: jest.fn(),
    };

    mockKeycloakService = {
      login: jest.fn(),
      logout: jest.fn(),
      refreshToken: jest.fn(),
      validateToken: jest.fn(),
      getUserInfo: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useValue: mockKeycloakService,
        },
        {
          provide: EventPublisherService,
          useValue: {},
        },
        {
          provide: 'KeycloakIdentityProviderService',
          useValue: {},
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: {},
        },
        {
          provide: APP_PIPE,
          useValue: new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
          }),
        },
        {
          provide: CustomerRepository,
          useValue: {},
        },
        {
          provide: EmployeeService,
          useValue: {},
        },
        {
          provide: SupplierAuthService,
          useValue: {},
        },
        {
          provide: PrismaSupplierRepository,
          useValue: {},
        },
        {
          provide: PrismaEmployeeRepository,
          useValue: {},
        },
        {
          provide: 'UsersOtpRepository',
          useValue: {
            create: jest.fn(),
            findByEmail: jest.fn(),
            markAsUsed: jest.fn(),
            deleteExpiredTokens: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('forgotPassword', () => {
    const validEmail = '<EMAIL>';
    const mockUser: MockUser = {
      id: 'user-123',
      email: validEmail,
    };

    // Test UUID generation
    describe('UUID generation', () => {
      it('should use fixed token for password reset', async () => {
        mockUserRepository.findByEmail = jest
          .fn<Promise<MockUser>, [string]>()
          .mockResolvedValue(mockUser);

        mockPasswordResetTokenRepository.create = jest
          .fn<Promise<MockTokenResponse>, [CreateTokenData]>()
          .mockResolvedValue({
            id: 'token-123',
            token: '123456',
            expiresAt: new Date(),
          });

        const dto: ForgotPasswordDto = { email: validEmail };
        await service.forgotPassword(dto);

        expect(mockPasswordResetTokenRepository.create).toHaveBeenCalledWith(
          expect.objectContaining({
            token: expect.any(String),
          }),
        );
      });
    });

    // Test TTL calculation
    describe('TTL calculation', () => {
      it('should set token expiration to 1 hour from creation', async () => {
        mockUserRepository.findByEmail = jest
          .fn<Promise<MockUser>, [string]>()
          .mockResolvedValue(mockUser);

        const now = new Date();
        jest.useFakeTimers().setSystemTime(now);

        const dto: ForgotPasswordDto = { email: validEmail };
        await service.forgotPassword(dto);

        const expectedExpiresAt = new Date(now);
        expectedExpiresAt.setHours(expectedExpiresAt.getHours() + 1);

        expect(mockPasswordResetTokenRepository.create).toHaveBeenCalledWith(
          expect.objectContaining({
            expiresAt: expect.any(Date) as unknown,
          }),
        );

        const mockCall = mockPasswordResetTokenRepository.create.mock.calls[0];
        const actualExpiresAt = mockCall[0].expiresAt;
        expect(actualExpiresAt.getTime()).toBe(expectedExpiresAt.getTime());

        jest.useRealTimers();
      });
    });

    // Test successful flow
    it('should return 204 when user exists and save token', async () => {
      mockUserRepository.findByEmail = jest
        .fn<Promise<MockUser>, [string]>()
        .mockResolvedValue(mockUser);
      mockPasswordResetTokenRepository.create = jest
        .fn<Promise<MockTokenResponse>, [CreateTokenData]>()
        .mockResolvedValue({
          id: 'token-123',
          token: '123456',
          expiresAt: new Date(),
        });

      const dto: ForgotPasswordDto = { email: validEmail };
      await service.forgotPassword(dto);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(mockPasswordResetTokenRepository.create).toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).toHaveBeenCalledWith(
        validEmail,
        expect.any(String),
      );
    });

    // Test security measure
    it('should return 204 when user does not exist (prevent email enumeration)', async () => {
      mockUserRepository.findByEmail = jest
        .fn<Promise<MockUser | null>, [string]>()
        .mockResolvedValue(null);

      const dto: ForgotPasswordDto = { email: '<EMAIL>' };
      await service.forgotPassword(dto);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(
        '<EMAIL>',
      );
      expect(mockPasswordResetTokenRepository.create).not.toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
    });

    // Test error handling
    it('should handle errors gracefully', async () => {
      mockUserRepository.findByEmail = jest
        .fn<Promise<never>, [string]>()
        .mockRejectedValue(new Error('Database error'));

      const dto: ForgotPasswordDto = { email: validEmail };
      await expect(service.forgotPassword(dto)).rejects.toThrow();
    });
  });
});
