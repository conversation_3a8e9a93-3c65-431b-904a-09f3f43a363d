import { validate } from 'class-validator';
import { ResetPasswordDto } from '../dto/reset-password.dto';

describe('ResetPasswordDto validation', () => {
  it('should fail if token is empty', async () => {
    const dto = new ResetPasswordDto();
    dto.token = '';
    dto.newPassword = 'NovaSenha123!';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((e) => e.property === 'token' && e.constraints?.isNotEmpty),
    ).toBe(true);
  });

  it('should fail if newPassword is empty', async () => {
    const dto = new ResetPasswordDto();
    dto.token = '123456';
    dto.newPassword = '';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some(
        (e) => e.property === 'newPassword' && e.constraints?.isNotEmpty,
      ),
    ).toBe(true);
  });

  it('should fail if newPassword is less than 8 chars', async () => {
    const dto = new ResetPasswordDto();
    dto.token = '123456';
    dto.newPassword = '1234567';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some(
        (e) => e.property === 'newPassword' && e.constraints?.minLength,
      ),
    ).toBe(true);
  });

  it('should pass with valid data', async () => {
    const dto = new ResetPasswordDto();
    dto.token = '123456';
    dto.newPassword = 'NovaSenha123!';
    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });
});
