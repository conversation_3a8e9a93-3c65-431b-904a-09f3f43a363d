import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../../../users/users.service';
import { LogoutDto } from '../../dto/logout.dto';
import { ConfigModule, ConfigService } from '@nestjs/config';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule],
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            logout: jest.fn(),
          },
        },
        {
          provide: KeycloakService,
          useValue: {
            logout: jest.fn(),
          },
        },
        {
          provide: EventPublisherService,
          useValue: {
            publish: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            decode: jest.fn(),
          },
        },
        {
          provide: UsersService,
          useValue: {},
        },
        {
          provide: 'UserRepository',
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string): string | undefined => {
              const config: Record<string, string> = {
                KEYCLOAK_CLIENT_ID: 'test-client',
                KEYCLOAK_CLIENT_SECRET: 'test-secret',
                KEYCLOAK_BASE_URL: 'http://localhost:8080',
                KEYCLOAK_REALM: 'test-realm',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  describe('logout', () => {
    it('deve retornar 204 ao realizar logout com sucesso', async () => {
      const logoutDto: LogoutDto = {
        refresh_token: 'valid-refresh-token',
      };

      const logoutSpy = jest.spyOn(authService, 'logout').mockResolvedValue();

      await expect(controller.logout(logoutDto)).resolves.toBeUndefined();
      expect(logoutSpy).toHaveBeenCalledWith(logoutDto);
    });

    it('deve lançar BadRequestException quando o token for inválido', async () => {
      const logoutDto: LogoutDto = {
        refresh_token: 'invalid-refresh-token',
      };

      jest
        .spyOn(authService, 'logout')
        .mockRejectedValue(new Error('Token inválido'));

      await expect(controller.logout(logoutDto)).rejects.toThrow(
        'Token inválido',
      );
    });
  });
});
