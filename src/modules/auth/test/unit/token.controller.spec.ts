import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { INestApplication } from '@nestjs/common';
import { UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Server } from 'http';

import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { AuthController } from '../../auth.controller';
import { AuthService } from '../../auth.service';

describe('AuthController - Token', () => {
  let app: INestApplication;
  let authService: AuthService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            token: jest.fn(),
          },
        },
        {
          provide: KeycloakService,
          useValue: {
            token: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                KEYCLOAK_BASE_URL: 'http://keycloak:8080',
                KEYCLOAK_REALM: 'test',
                KEYCLOAK_CLIENT_ID: 'test-client',
                KEYCLOAK_CLIENT_SECRET: 'test-client-secret',
              };
              return config[key] as string;
            }),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    authService = moduleFixture.get<AuthService>(AuthService);

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  // Helper to satisfy supertest's expected type
  function getSupertestServer(app: INestApplication): Server {
    return app.getHttpServer() as Server;
  }

  describe('POST auth/login', () => {
    it('deve retornar novo access_token quando credendciais forem validas', async () => {
      // Setup do mock
      jest.spyOn(authService, 'token').mockResolvedValueOnce({
        access_token: 'new-valid-access-token',
        refresh_token: 'new-valid-refresh-token',
        expires_in: 3600,
      });

      // Teste
      const response = await request(getSupertestServer(app))
        .post('/auth/login')
        .send({
          username: '<EMAIL>',
          password: 'password',
        })
        .expect(200);

      interface TokenResponseBody {
        access_token?: string;
        refresh_token?: string;
        expires_in?: number;
        message?: string;
      }
      const body = response.body as TokenResponseBody;
      expect(body).toHaveProperty('access_token');
      expect(body).toHaveProperty('refresh_token');
      expect(body).toHaveProperty('expires_in');
      expect(body.access_token).toBe('new-valid-access-token');
      expect(body.refresh_token).toBe('new-valid-refresh-token');
      expect(body.expires_in).toBe(3600);
    });

    it('deve retornar 401 quando as credenciais forem inválidas', async () => {
      // Setup do mock
      jest
        .spyOn(authService, 'token')
        .mockRejectedValueOnce(
          new UnauthorizedException('Credenciais inválidas'),
        );

      // Teste
      const response = await request(getSupertestServer(app))
        .post('/auth/login')
        .send({
          username: '<EMAIL>',
          password: 'wrong-password',
        })
        .expect(401);

      interface TokenResponseBody {
        access_token?: string;
        refresh_token?: string;
        expires_in?: number;
        message?: string;
      }
      const body = response.body as TokenResponseBody;
      expect(body).toHaveProperty('message', 'Credenciais inválidas');
    });
  });
});
