import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { INestApplication } from '@nestjs/common';
import { UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Server } from 'http';

import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { AuthController } from '../../auth.controller';
import { AuthService } from '../../auth.service';

describe('AuthController - Refresh Token', () => {
  let app: INestApplication;
  let authService: AuthService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            refreshToken: jest.fn(),
          },
        },
        {
          provide: KeycloakService,
          useValue: {
            refreshToken: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                KEYCLOAK_BASE_URL: 'http://keycloak:8080',
                KEYCLOAK_REALM: 'test',
                KEYCLOAK_CLIENT_ID: 'test-client',
                KEYCLOAK_CLIENT_SECRET: 'test-client-secret',
              };
              return config[key] as string;
            }),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    authService = moduleFixture.get<AuthService>(AuthService);

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /auth/refresh', () => {
    it('deve retornar novo access_token quando refresh_token for válido', async () => {
      // Setup do mock
      jest.spyOn(authService, 'refreshToken').mockResolvedValueOnce({
        access_token: 'new-valid-access-token',
      });

      // Teste
      const response = await request(getSupertestServer(app))
        .post('/auth/refresh')
        .send({ refresh_token: 'valid-refresh-token' })
        .expect(201);

      // Verificações
      interface RefreshResponseBody {
        access_token?: string;
        message?: string;
      }
      const body = response.body as RefreshResponseBody;
      expect(body).toHaveProperty('access_token');
      expect(body.access_token).toBe('new-valid-access-token');
    });

    it('deve retornar 401 quando refresh_token for inválido', async () => {
      // Setup do mock
      jest
        .spyOn(authService, 'refreshToken')
        .mockRejectedValueOnce(
          new UnauthorizedException('Refresh token inválido ou expirado'),
        );

      // Teste
      const response = await request(getSupertestServer(app))
        .post('/auth/refresh')
        .send({ refresh_token: 'invalid-refresh-token' })
        .expect(401);

      // Verificações
      interface RefreshResponseBody {
        access_token?: string;
        message?: string;
      }
      const body = response.body as RefreshResponseBody;
      expect(body).toHaveProperty(
        'message',
        'Refresh token inválido ou expirado',
      );
    });
  });
});

// Helper to satisfy supertest's expected type
function getSupertestServer(app: INestApplication): Server {
  return app.getHttpServer() as Server;
}
