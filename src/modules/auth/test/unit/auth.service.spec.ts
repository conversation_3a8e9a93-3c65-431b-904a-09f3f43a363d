import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from '../../auth.service';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { UsersService } from '../../../users/users.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { UnauthorizedException } from '@nestjs/common';
import { EmployeeService } from '../../../finance/employee/employee.service';
import { CustomerRepository } from '../../../customers/infrastructure/repositories/customer.repository';
import { SupplierAuthService } from '../../supplier-auth/supplier-auth.service';
import { PrismaSupplierRepository } from '../../../../infrastructure/repositories/prisma-supplier.repository';
import { PrismaEmployeeRepository } from '../../../../infrastructure/repositories/prisma-employee.repository';
import { UsersOtpRepository } from '../../interfaces/users-otp.repository.interface';
import { LogoutDto } from '../../dto/logout.dto';
import { BadRequestException } from '@nestjs/common';

describe('AuthService', () => {
  let service: AuthService;
  let mockPublish: jest.Mock;
  let keycloakService: KeycloakService;
  let eventPublisher: EventPublisherService;
  let jwtService: JwtService;

  const mockJwtService = {
    sign: jest.fn(),
    decode: jest.fn(),
  };

  const mockKeycloakService = {
    token: jest.fn(),
    refreshToken: jest.fn(),
    validateToken: jest.fn(),
    getUserInfo: jest.fn(),
    logout: jest.fn(),
  };

  const mockKeycloakIdentityProviderService = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn(),
  };

  const _mockUsersService = {
    findByEmail: jest.fn(),
    findOne: jest.fn(),
  };

  const _mockUserRepository = {
    findByEmail: jest.fn(),
    create: jest.fn(),
    updateUserKeycloakId: jest.fn(),
  };

  const mockPasswordResetTokenRepository = {
    create: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
  };

  const mockEmailService = {
    sendPasswordResetEmail: jest.fn(),
  };

  beforeEach(async () => {
    mockPublish = jest.fn();
    const mockEventPublisher = {
      publish: mockPublish,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: SupplierAuthService,
          useValue: {
            validateSupplier: jest.fn(),
            createSupplier: jest.fn(),
            updateSupplier: jest.fn(),
            deleteSupplier: jest.fn(),
          },
        },
        {
          provide: KeycloakService,
          useValue: mockKeycloakService,
        },
        {
          provide: UsersService,
          useValue: _mockUsersService,
        },
        {
          provide: 'UserRepository',
          useValue: _mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakIdentityProviderService,
        },
        {
          provide: EmployeeService,
          useValue: {},
        },
        {
          provide: CustomerRepository,
          useValue: {},
        },
        {
          provide: PrismaSupplierRepository,
          useValue: {},
        },
        {
          provide: PrismaEmployeeRepository,
          useValue: {},
        },
        {
          provide: 'UsersOtpRepository',
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    keycloakService = module.get<KeycloakService>(KeycloakService);
    eventPublisher = module.get<EventPublisherService>(EventPublisherService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getProfile', () => {
    it('should throw UnauthorizedException when token is invalid', async () => {
      mockKeycloakService.validateToken.mockResolvedValue(false);
      mockKeycloakService.validateToken.mockResolvedValue(false);

      await expect(service.getProfile('invalid-token')).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException when required claims are missing', async () => {
      mockKeycloakService.validateToken.mockResolvedValue(true);
      mockKeycloakService.getUserInfo.mockResolvedValue({
        sub: '123',
        // missing preferred_username and email
      });

      await expect(service.getProfile('valid-token')).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should return user profile with all fields', async () => {
      const mockUserInfo = {
        sub: '123',
        email_verified: true,
        name: 'Test User',
        preferred_username: 'testuser',
        given_name: 'Test',
        family_name: 'User',
        email: '<EMAIL>',
        roles: ['user'],
      };

      mockKeycloakService.validateToken.mockResolvedValue(true);
      mockKeycloakService.getUserInfo.mockResolvedValue(mockUserInfo);

      const result = await service.getProfile('valid-token');

      expect(result).toEqual({
        id: '123',
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        roles: ['user'],
      });
    });

    it('should handle missing optional fields', async () => {
      const mockUserInfo = {
        sub: '123',
        email_verified: true,
        name: 'Test User',
        preferred_username: 'testuser',
        given_name: 'Test',
        family_name: 'User',
        email: '<EMAIL>',
        roles: ['user'],
      };

      mockKeycloakService.validateToken.mockResolvedValue(true);
      mockKeycloakService.getUserInfo.mockResolvedValue(mockUserInfo);

      const result = await service.getProfile('valid-token');

      expect(result).toEqual({
        id: '123',
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        roles: ['user'],
      });
    });

    it('should throw UnauthorizedException when Keycloak is unavailable', async () => {
      mockKeycloakService.validateToken.mockRejectedValue(
        new Error('Keycloak unavailable'),
      );

      await expect(service.getProfile('valid-token')).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('refreshToken', () => {
    const mockAccessToken = 'new-access-token';
    const mockRefreshToken = 'valid-refresh-token';
    const mockUserId = 'user-123';

    it('should refresh token and publish event successfully', async () => {
      // Mock Keycloak refresh token response
      mockKeycloakService.refreshToken.mockResolvedValue({
        access_token: mockAccessToken,
      });

      // Mock JWT decode to return user ID
      mockJwtService.decode.mockReturnValue({
        sub: mockUserId,
      });

      const result = await service.refreshToken({
        refresh_token: mockRefreshToken,
      });

      // Verify response structure
      expect(result).toEqual({
        access_token: mockAccessToken,
      });

      // Verify Keycloak service was called
      expect(mockKeycloakService.refreshToken).toHaveBeenCalledWith(
        mockRefreshToken,
      );

      // Verify JWT decode was called
      expect(mockJwtService.decode).toHaveBeenCalledWith(mockRefreshToken);

      // Verify event was published
      expect(mockPublish).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException when refresh token is invalid', async () => {
      mockKeycloakService.refreshToken.mockRejectedValue(
        new Error('Invalid token'),
      );

      await expect(
        service.refreshToken({ refresh_token: 'invalid-token' }),
      ).rejects.toThrow(UnauthorizedException);

      expect(mockPublish).not.toHaveBeenCalled();
    });

    it('should not publish event when user ID is missing', async () => {
      // Mock Keycloak refresh token response
      mockKeycloakService.refreshToken.mockResolvedValue({
        access_token: mockAccessToken,
      });

      // Mock JWT decode to return no user ID
      mockJwtService.decode.mockReturnValue({});

      const result = await service.refreshToken({
        refresh_token: mockRefreshToken,
      });

      expect(result).toEqual({
        access_token: mockAccessToken,
      });

      expect(mockPublish).not.toHaveBeenCalled();
    });
  });

  describe('logout', () => {
    it('deve revogar o token e publicar evento com sucesso', async () => {
      const logoutDto: LogoutDto = {
        refresh_token: 'valid-refresh-token',
      };

      const decodedToken = {
        sub: 'user-123',
        jti: 'token-123',
      };

      jest.spyOn(jwtService, 'decode').mockReturnValue(decodedToken);
      const logoutSpy = jest
        .spyOn(keycloakService, 'logout')
        .mockResolvedValue(undefined);
      const publishSpy = jest
        .spyOn(eventPublisher, 'publish')
        .mockResolvedValue(undefined);

      await service.logout(logoutDto);

      expect(logoutSpy).toHaveBeenCalledWith(logoutDto.refresh_token);
      expect(publishSpy).toHaveBeenCalled();
    });

    it('deve lançar BadRequestException quando o token for inválido', async () => {
      const logoutDto: LogoutDto = {
        refresh_token: 'invalid-refresh-token',
      };

      jest.spyOn(jwtService, 'decode').mockReturnValue(null);

      await expect(service.logout(logoutDto)).rejects.toThrow(
        BadRequestException,
      );

      const keycloakLogoutSpy = jest.spyOn(keycloakService, 'logout');
      const eventPublishSpy = jest.spyOn(eventPublisher, 'publish');

      expect(keycloakLogoutSpy).not.toHaveBeenCalled();
      expect(eventPublishSpy).not.toHaveBeenCalled();
    });

    it('deve lançar BadRequestException quando o logout falhar', async () => {
      const logoutDto: LogoutDto = {
        refresh_token: 'valid-refresh-token',
      };

      const decodedToken = {
        sub: 'user-123',
        jti: 'token-123',
      };

      jest.spyOn(jwtService, 'decode').mockReturnValue(decodedToken);
      jest
        .spyOn(keycloakService, 'logout')
        .mockRejectedValue(new Error('Falha no logout'));

      await expect(service.logout(logoutDto)).rejects.toThrow(
        BadRequestException,
      );

      const eventPublishSpy = jest.spyOn(eventPublisher, 'publish');
      expect(eventPublishSpy).not.toHaveBeenCalled();
    });
  });
});
