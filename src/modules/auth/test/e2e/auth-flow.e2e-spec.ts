import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../../app.module';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { Express } from 'express';
import { TestContainer } from '@/core/helpers/test-container';

// Set NODE_ENV to test
process.env.NODE_ENV = 'test';

// Load test environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

// Increase timeout for all tests in this file
jest.setTimeout(120000); // 120 seconds

// Definir interfaces para tipagem
interface AuthResponse {
  access_token: string;
  refresh_token?: string;
  user?: {
    id: string;
    email?: string;
    keycloakId?: string;
    [key: string]: unknown;
  };
}

/**
 * IMPORTANTE: Este teste realiza um fluxo completo de autenticação com serviços reais.
 *
 * NOTA SOBRE TOKENS: O Keycloak por padrão configura tokens de acesso com expiração
 * de apenas 60 segundos, o que pode causar falhas nos testes se o logout for executado
 * após esse período. Para resolver esse problema:
 *
 * 1. Este teste combina o login e logout em um único fluxo para garantir que o logout
 *    seja executado antes da expiração do token
 * 2. Em ambiente de produção, você deve:
 *    - Acessar o console de administração do Keycloak (http://localhost:8080/admin)
 *    - Ir para Realm Settings > Tokens
 *    - Aumentar "Access Token Lifespan" para pelo menos 1 hora (3600 segundos)
 *    - Aumentar "Client Session Idle" para pelo menos 8 horas
 *    - Aumentar "Client Session Max" para pelo menos 24 horas
 */
describe('Fluxo de Autenticação (e2e)', () => {
  let app: INestApplication;
  let keycloakService: KeycloakService;

  // Dados de teste - usando timestamp para garantir email único
  const testUser = {
    name: 'Usuário E2E Teste',
    email: `user-${Date.now()}@e2etest.com`,
    password: 'senha@123E2E',
  };

  let accessToken: string;
  let refreshToken: string;

  beforeAll(async () => {
    try {
      // Start test container
      await TestContainer.start();

      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            envFilePath: path.resolve(process.cwd(), '.env.test'),
            isGlobal: true,
          }),
          AppModule,
        ],
      }).compile();

      app = moduleFixture.createNestApplication();
      app.useGlobalPipes(
        new ValidationPipe({
          transform: true,
          whitelist: true,
          skipMissingProperties: false,
        }),
      );
      await app.init();

      // Obter o serviço Keycloak para uso nos testes
      keycloakService = moduleFixture.get<KeycloakService>(KeycloakService);
    } catch (error) {
      console.error('Error in beforeAll:', error);
      throw error;
    }
  });

  afterAll(async () => {
    try {
      // Stop test container
      await TestContainer.stop();

      if (app) {
        await app.close();
      }
    } catch (error) {
      console.error('Error in afterAll:', error);
      throw error;
    }
  });

  describe('Auth Flow', () => {
    it('Deve completar o fluxo completo de autenticação (registro, login, validate, me, refresh e logout)', async () => {
      // 1. REGISTRO
      console.log('\n🚀 TESTE: Registrando novo usuário...');
      console.log('Dados:', JSON.stringify(testUser, null, 2));

      const registerResponse = await request(app.getHttpServer() as Express)
        .post('/auth/register')
        .send(testUser)
        .expect(201);

      expect(registerResponse.body).toHaveProperty('user');
      expect(registerResponse.body).toHaveProperty('access_token');
      const registerBody = registerResponse.body as AuthResponse;
      accessToken = registerBody.access_token;

      console.log('\n✅ REGISTRO BEM-SUCEDIDO');
      console.log('Email:', testUser.email);
      console.log(
        'Access Token:',
        accessToken ? `${accessToken.substring(0, 20)}...` : null,
      );

      // 2. LOGIN
      console.log('\n🚀 TESTE: Fazendo login...');

      const loginResponse = await request(app.getHttpServer() as Express)
        .post('/auth/login')
        .send({
          username: testUser.email,
          password: testUser.password,
        })
        .expect(201);

      expect(loginResponse.body).toHaveProperty('access_token');
      expect(loginResponse.body).toHaveProperty('refresh_token');

      const loginBody = loginResponse.body as AuthResponse;
      accessToken = loginBody.access_token;
      if (loginBody.refresh_token) {
        refreshToken = loginBody.refresh_token;
      } else {
        throw new Error('Refresh token não encontrado na resposta de login');
      }

      console.log('\n✅ LOGIN BEM-SUCEDIDO');
      console.log(
        'Access Token:',
        accessToken ? `${accessToken.substring(0, 20)}...` : null,
      );
      console.log(
        'Refresh Token:',
        refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
      );

      // 3. VALIDATE TOKEN
      console.log('\n🚀 TESTE: Validando token...');

      const validateResponse = await request(app.getHttpServer() as Express)
        .get('/auth/validate')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(validateResponse.body).toHaveProperty('message', 'Token válido');
      expect(validateResponse.body).toHaveProperty('user');

      console.log('\n✅ VALIDAÇÃO DE TOKEN BEM-SUCEDIDA');

      // 4. GET PROFILE
      console.log('\n🚀 TESTE: Obtendo perfil do usuário...');

      const profileResponse = await request(app.getHttpServer() as Express)
        .get('/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(profileResponse.body).toHaveProperty('id');
      expect(profileResponse.body).toHaveProperty('email', testUser.email);
      expect(profileResponse.body).toHaveProperty('username', testUser.email);
      expect(profileResponse.body).toHaveProperty('fullName', testUser.name);
      expect(profileResponse.body).toHaveProperty('roles');

      console.log('\n✅ PERFIL OBTIDO COM SUCESSO');

      // 5. REFRESH TOKEN
      console.log('\n🚀 TESTE: Atualizando token...');

      const refreshResponse = await request(app.getHttpServer() as Express)
        .post('/auth/refresh')
        .send({ refresh_token: refreshToken })
        .expect(201);

      expect(refreshResponse.body).toHaveProperty('access_token');
      const refreshBody = refreshResponse.body as AuthResponse;
      accessToken = refreshBody.access_token;

      console.log('\n✅ TOKEN ATUALIZADO COM SUCESSO');

      // 6. LOGOUT
      console.log('\n🚀 TESTE: Fazendo logout...');

      try {
        await keycloakService.logout(refreshToken);
        console.log('\n✅ LOGOUT BEM-SUCEDIDO via KeycloakService');
      } catch (logoutError) {
        console.error('Erro ao fazer logout via KeycloakService:', logoutError);

        // Tentar via API HTTP como fallback
        try {
          await request(app.getHttpServer() as Express)
            .post('/auth/logout')
            .set('Authorization', `Bearer ${accessToken}`)
            .send({ refresh_token: refreshToken })
            .expect(204);

          console.log('\n✅ LOGOUT BEM-SUCEDIDO via API HTTP');
        } catch (httpError) {
          console.warn('Aviso: Logout via API HTTP também falhou:', httpError);
          console.warn(
            'Motivo: Os tokens de refresh do Keycloak expiram rapidamente em ambientes de desenvolvimento',
          );
        }
      }

      // Verificar se o token não é mais válido tentando acessar um endpoint protegido
      try {
        await request(app.getHttpServer() as Express)
          .get('/auth/me')
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(401); // Deve retornar 401 Unauthorized se o logout foi bem-sucedido

        console.log(
          '\n✅ VERIFICAÇÃO DE LOGOUT BEM-SUCEDIDA: Token inválido como esperado',
        );
      } catch (error) {
        console.warn(
          'Aviso: O token ainda pode ser válido, mas isso é esperado em ambiente de teste:',
          error,
        );
      }
    });
  });
});

// Função de suporte para capturar erros em promessas
function _catchErrorFrom(fn: () => Promise<unknown>): Promise<Error> {
  return fn().then(
    () => {
      throw new Error('Expected an error to be thrown');
    },
    (error: Error) => error,
  );
}
