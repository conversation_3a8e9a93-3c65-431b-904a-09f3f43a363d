import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../../app.module';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import * as dotenv from 'dotenv';

process.env.NODE_ENV = 'test';
dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

// Aumenta o timeout global para evitar falhas por lentidão
jest.setTimeout(120000);

// Definições de tipos para as respostas
interface RegisterResponse {
  user: { id: string; email: string; role: string };
  access_token: string;
}

describe('Auth E2E - Register, Login e /me', () => {
  let app: INestApplication;
  const random = Math.floor(Math.random() * 1000000);
  const testUser = {
    name: '<PERSON><PERSON><PERSON><PERSON> E2E CLI',
    email: `e2euser${random}@test.com`,
    password: '<PERSON>ha@123Teste',
  };
  let accessToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          envFilePath: path.resolve(process.cwd(), '.env.test'),
          isGlobal: true,
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        skipMissingProperties: false,
      }),
    );
    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  it('deve registrar, logar e acessar /me com sucesso', async () => {
    // 1. Register
    const registerResponse = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .post('/auth/register')
      .send(testUser)
      .expect(201);
    expect(registerResponse.status).toBe(201);
    const registerBody = registerResponse.body as RegisterResponse;
    expect(registerBody).toHaveProperty('user');
    expect(registerBody.user).toHaveProperty('role', 'USER');
    expect(registerBody).toHaveProperty('access_token');

    // 2. Login
    const loginResponse = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .post('/auth/login')
      .send({ username: testUser.email, password: testUser.password })
      .expect(201);
    expect(loginResponse.body).toHaveProperty('access_token');
    const loginBody = loginResponse.body as { access_token: string };
    accessToken = loginBody.access_token;

    // 3. /me
    const meResponse = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .get('/auth/me')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);
    expect(meResponse.body).toHaveProperty('email', testUser.email);
    expect(meResponse.body).toHaveProperty('id');
    const meBody = meResponse.body as { roles: string[] };
    expect(Array.isArray(meBody.roles)).toBe(true);
  });
});
