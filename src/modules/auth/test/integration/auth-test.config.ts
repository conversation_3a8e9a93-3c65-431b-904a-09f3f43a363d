export const AUTH_TEST_CONFIG = {
  keycloak: {
    baseUrl: 'http://localhost:8080',
    realm: 'master',
    clientId: 'admin-cli',
    clientSecret: 'test-secret',
  },
  mock: {
    keycloakUserId: 'mock-keycloak-user-id',
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
  },
};

export const mockConfigService = {
  get: jest.fn((key: string) => {
    switch (key) {
      case 'KEYCLOAK_BASE_URL':
        return AUTH_TEST_CONFIG.keycloak.baseUrl;
      case 'KEYCLOAK_REALM':
        return AUTH_TEST_CONFIG.keycloak.realm;
      case 'KEYCLOAK_CLIENT_ID':
        return AUTH_TEST_CONFIG.keycloak.clientId;
      case 'KEYCLOAK_CLIENT_SECRET':
        return AUTH_TEST_CONFIG.keycloak.clientSecret;
      default:
        return undefined;
    }
  }),
};

// Safer implementation with proper typing
export const mockRequestUtilsService = {
  executeWithRetry: jest
    .fn()
    .mockImplementation(<T>(fn: () => Promise<{ data: T }>) => {
      return fn().then((response) => response.data);
    }),
};

// Add test methods to KeycloakService interface for TypeScript
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';

// Extend KeycloakService interface with test methods
declare module '../../../../infrastructure/keycloak/keycloak.service' {
  interface KeycloakService {
    getBaseUrl?: () => string;
    getRealm?: () => string;
  }
}

// Add these methods to KeycloakService when testing
export function extendKeycloakServiceForTesting(
  keycloakService: KeycloakService,
): void {
  type KeycloakWithPrivateFields = {
    baseUrl?: string;
    realm?: string;
  };

  // Define accessor methods to expose private fields for testing
  keycloakService.getBaseUrl = function (this: KeycloakService): string {
    // Use a type assertion with unknown first
    const instance = this as unknown as KeycloakWithPrivateFields;
    return instance.baseUrl || AUTH_TEST_CONFIG.keycloak.baseUrl;
  };

  keycloakService.getRealm = function (this: KeycloakService): string {
    // Use a type assertion with unknown first
    const instance = this as unknown as KeycloakWithPrivateFields;
    return instance.realm || AUTH_TEST_CONFIG.keycloak.realm;
  };
}

export interface TestUser {
  id: string;
  email: string;
  name: string;
  password: string;
  keycloakId: string;
  accessToken: string | null;
  refreshToken: string | null;
}

export function createTestUser(): TestUser {
  const timestamp = Date.now();
  return {
    id: `test-user-id-${timestamp}`,
    email: `test-user-${timestamp}@example.com`,
    password: 'TestPassword123!',
    keycloakId: `test-keycloak-id-${timestamp}`,
    name: 'Test User',
    accessToken: null,
    refreshToken: null,
  };
}

export function resetAllMocks() {
  jest.clearAllMocks();
  jest.resetAllMocks();
}
