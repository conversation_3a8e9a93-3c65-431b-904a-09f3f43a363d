# Centro de Custo - Documentação

## Visão Geral

O módulo de Centro de Custo permite a criação, consulta, atualização e exclusão de centros de custo no sistema. Os centros de custo são utilizados para classificar despesas e receitas por área de responsabilidade.

## Status da Implementação

### Funcionalidades Implementadas

- ✅ Criação de Centro de Custo (POST /v1/finance/cost-centers)
- ⬜ Listagem de Centros de Custo (GET /v1/finance/cost-centers)
- ⬜ Busca de Centro de Custo por ID (GET /v1/finance/cost-centers/{uuid})
- ⬜ Atualização de Centro de Custo (PUT /v1/finance/cost-centers/{uuid})
- ⬜ Exclusão de Centro de Custo (DELETE /v1/finance/cost-centers/{uuid})

### Melhorias Realizadas

- ✅ Implementação da entidade `CostCenter` com validações
- ✅ Implementação do caso de uso `CreateCostCenterUseCase`
- ✅ Implementação do repositório Prisma para Centro de Custo
- ✅ Implementação do controller e serviço
- ✅ Adição de testes unitários, de integração e de contrato
- ✅ Correção de problemas de tipagem e lint
- ✅ Documentação completa da API
- ⬜ Execução da migração do banco de dados

## Documentação da API (Swagger)

![Documentação da API no Swagger]

*Insira aqui o print da documentação do Swagger para o endpoint de Centro de Custo*

## Endpoints

### Criar Centro de Custo

Cria um novo centro de custo no sistema.

- **URL**: `/v1/finance/cost-centers`
- **Método**: `POST`
- **Autenticação**: Requer token JWT válido com role `finance_admin`
- **Corpo da Requisição**:

```json
{
  "description": "Despesas de Marketing",
  "createdBy": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
  "updatedBy": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
}
```

- **Resposta de Sucesso**:
  - **Código**: 201 Created
  - **Conteúdo**:

```json
{
  "uuid": "11111111-**************-************",
  "description": "Despesas de Marketing",
  "createdBy": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
  "updatedBy": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
  "createdAt": "2025-05-12T00:00:00Z",
  "updatedAt": "2025-05-12T00:00:00Z"
}
```

- **Respostas de Erro**:
  - **Código**: 400 Bad Request - Campos ausentes ou inválidos
  - **Código**: 401 Unauthorized - Token JWT ausente ou inválido
  - **Código**: 403 Forbidden - Token válido, mas sem a role exigida

## Modelo de Dados

### Tabela: `finance.cost_centers`

| Coluna       | Tipo         | Descrição                                  |
|--------------|--------------|-------------------------------------------|
| id           | SERIAL       | Identificador único (chave primária)       |
| uuid         | UUID         | Identificador único universal              |
| description  | VARCHAR(255) | Descrição do centro de custo               |
| created_by   | UUID         | UUID do usuário que criou o registro       |
| updated_by   | UUID         | UUID do usuário que atualizou o registro   |
| created_at   | TIMESTAMPTZ  | Data e hora de criação do registro         |
| updated_at   | TIMESTAMPTZ  | Data e hora da última atualização          |

## Regras de Negócio

1. A descrição do centro de custo é obrigatória e deve ter no máximo 255 caracteres.
2. Os campos `createdBy` e `updatedBy` devem ser UUIDs válidos.
3. Apenas usuários com a role `finance_admin` podem criar centros de custo.

## Estrutura do Código

A implementação segue a arquitetura limpa (Clean Architecture) e está organizada da seguinte forma:

- **Entidade**: `CostCenter` em `src/core/domain/cost-center/entities/cost-center.entity.ts`
- **Caso de Uso**: `CreateCostCenterUseCase` em `src/core/application/use-cases/cost-center/create-cost-center.use-case.ts`
- **Repositório**: Interface em `src/core/ports/repositories/cost-center-repository.port.ts` e implementação em `src/infrastructure/repositories/prisma-cost-center.repository.ts`
- **Controller**: `CostCenterController` em `src/modules/finance/cost-center/controllers/cost-center.controller.ts`
- **DTOs**: Em `src/modules/finance/cost-center/dto/`
- **Serviço**: `CostCenterService` em `src/modules/finance/cost-center/services/cost-center.service.ts`

## Testes

A funcionalidade inclui os seguintes testes:

1. **Testes Unitários**: Para o serviço e caso de uso
2. **Testes de Integração**: Para o repositório e controller
3. **Testes de Contrato**: Validação do schema JSON da resposta

## Migração do Banco de Dados

A migração para criar a tabela `finance.cost_centers` está incluída no arquivo de migração do Prisma. Para aplicar a migração, execute:

```bash
npx prisma migrate dev --name add_cost_centers
```

Isso criará a tabela e o índice necessários conforme especificado no DDL:

```sql
-- Ativar extensão pgcrypto
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Criar schema
CREATE SCHEMA IF NOT EXISTS finance;

-- Criar tabela cost_centers
CREATE TABLE finance.cost_centers (
  id SERIAL PRIMARY KEY,
  uuid UUID NOT NULL DEFAULT gen_random_uuid(),
  description VARCHAR(255) NOT NULL,
  created_by UUID NOT NULL,
  updated_by UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Índice para busca por descrição
CREATE INDEX idx_cost_centers_description 
ON finance.cost_centers USING gin (description gin_trgm_ops);
```
