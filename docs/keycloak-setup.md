# Passo a Passo: Setup do Keycloak do Zero

Este guia mostra como preparar o ambiente do Keycloak do zero para desenvolvimento e testes.

## 1. Limpar containers, imagens e volumes antigos

Se você já rodou o ambiente antes, é importante limpar tudo para evitar conflitos:

```sh
docker compose down -v --rmi all --remove-orphans
```
- `-v`: remove volumes
- `--rmi all`: remove imagens
- `--remove-orphans`: remove containers "órfãos"

## 2. Use sempre NPM (não Yarn)

> **Atenção:** O projeto foi testado e funciona melhor com **npm**. Se você usava `yarn`, troque para `npm` para evitar erros de dependência.

## 3. Instale as dependências

```sh
npm install
```

## 4. Suba os containers do ambiente

```sh
docker compose up -d
```

Isso vai subir o banco de dados, Keycloak, RabbitMQ, etc.

## 5. Aplique o schema do banco de dados

```sh
npx prisma db push
```

## 6. D<PERSON> permissão de execução ao script de setup do Keycloak

```sh
chmod +x scripts/keycloak/setup-all.sh
```

## 7. Execute o script de setup do Keycloak

```sh
./scripts/keycloak/setup-all.sh
```

Esse script cria o realm, clients, usuários e roles necessários no Keycloak.

> **Dica:** Se o script falhar, cheque os logs do container do Keycloak:
> ```sh
> docker compose logs -f keycloak
> ```

## 8. (Opcional) Rodar o script de setup manualmente

Se o `setup:all` der erro, execute os comandos acima manualmente, na ordem.

---

**Pronto!** O ambiente estará limpo e pronto para uso.

Se tiver problemas, confira as variáveis de ambiente no `.env` e `.env.test`, e garanta que o Keycloak está acessível em `http://localhost:8080` (ou `http://keycloak:8080` dentro do Docker Compose).

---

> **Observação importante:**
> - Use `http://localhost:8080` para execução dos scripts de setup e para rodar os testes automatizados (`.env.test`).
> - Use `http://keycloak:8080` na variável de ambiente da aplicação quando ela estiver rodando **dentro do Docker Compose** (`.env`).
> - O endereço `localhost` só funciona para o host, enquanto `keycloak` é o nome do serviço Docker acessível entre containers. 