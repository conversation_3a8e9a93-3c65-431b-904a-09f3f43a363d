/*
  Warnings:

  - You are about to drop the column `condicaoPagamento` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `departamentoId` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `preferenciaCobranca` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `responsavel` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - Added the required column `billingPreference` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `departmentId` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `paymentCondition` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `responsible` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "core"."customer_payment_preferences" DROP CONSTRAINT "customer_payment_preferences_departamentoId_fkey";

-- AlterTable
ALTER TABLE "core"."customer_payment_preferences" DROP COLUMN "condicaoPagamento",
DROP COLUMN "departamentoId",
DROP COLUMN "preferenciaCobranca",
DROP COLUMN "responsavel",
ADD COLUMN     "billingPreference" VARCHAR(255) NOT NULL,
ADD COLUMN     "departmentId" INTEGER NOT NULL,
ADD COLUMN     "paymentCondition" VARCHAR(255) NOT NULL,
ADD COLUMN     "responsible" VARCHAR(255) NOT NULL;

-- AddForeignKey
ALTER TABLE "core"."customer_payment_preferences" ADD CONSTRAINT "customer_payment_preferences_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "core"."labels"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
