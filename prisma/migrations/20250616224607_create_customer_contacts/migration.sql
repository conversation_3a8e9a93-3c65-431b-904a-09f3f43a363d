-- CreateTable
CREATE TABLE "core"."customer_contacts" (
    "id" TEXT NOT NULL,
    "customerId" INTEGER NOT NULL,
    "contact" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "area" TEXT NOT NULL,
    "responsible" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "customer_contacts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "customer_contacts_customerId_idx" ON "core"."customer_contacts"("customerId");

-- AddForeignKey
ALTER TABLE "core"."customer_contacts" ADD CONSTRAINT "customer_contacts_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "core"."customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
