-- Create<PERSON><PERSON>
CREATE TYPE "core"."CertificateCategory" AS ENUM ('FORNECEDORES_JOGOS', 'FORNECEDORES_KYC', 'PAGAMENTOS', 'SPORTSBOOK');

-- CreateEnum
CREATE TYPE "core"."CertificateType" AS ENUM ('CERTIFICADO_DE_JOGO', 'CERTIFICADO_RNG', 'CERTIFICADO_RGS', 'CERTIFICADO_DE_PLATAFORMA', 'CERTIFICADO_DE_INTEGRACAO', 'CERTIFICADO_DE_KYC', 'CERTIFICADO_DE_MEIOS_DE_PAGAMENTO', 'CERTIFICADO_DE_SPORTSBOOK');

-- CreateTable
CREATE TABLE "core"."certificates" (
    "id" TEXT NOT NULL,
    "customerId" INTEGER NOT NULL,
    "category" "core"."CertificateCategory" NOT NULL,
    "type" "core"."CertificateType" NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "notes" TEXT,
    "uploadedById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "certificates_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "core"."certificates" ADD CONSTRAINT "certificates_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "core"."customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."certificates" ADD CONSTRAINT "certificates_uploadedById_fkey" FOREIGN KEY ("uploadedById") REFERENCES "core"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
