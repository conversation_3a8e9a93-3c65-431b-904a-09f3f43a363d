/*
  Warnings:

  - You are about to drop the column `contact` on the `suppliers` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[email]` on the table `suppliers` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `email` to the `suppliers` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "core"."suppliers" DROP COLUMN "contact",
ADD COLUMN     "email" VARCHAR(255) NOT NULL;

-- CreateTable
CREATE TABLE "core"."supplier_contacts" (
    "id" TEXT NOT NULL,
    "supplierId" TEXT NOT NULL,
    "contact" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "area" TEXT NOT NULL,
    "responsible" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "supplier_contacts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "supplier_contacts_supplierId_idx" ON "core"."supplier_contacts"("supplierId");

-- CreateIndex
CREATE UNIQUE INDEX "suppliers_email_key" ON "core"."suppliers"("email");

-- AddForeignKey
ALTER TABLE "core"."supplier_contacts" ADD CONSTRAINT "supplier_contacts_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "core"."suppliers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
