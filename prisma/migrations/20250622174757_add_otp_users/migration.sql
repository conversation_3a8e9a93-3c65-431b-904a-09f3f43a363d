-- CreateTable
CREATE TABLE "core"."users_otp" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "hash" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "users_otp_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "users_otp_userId_expiresAt_idx" ON "core"."users_otp"("userId", "expiresAt");

-- AddForeignKey
ALTER TABLE "core"."users_otp" ADD CONSTRAINT "users_otp_userId_fkey" FOREIGN KEY ("userId") REFERENCES "core"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
