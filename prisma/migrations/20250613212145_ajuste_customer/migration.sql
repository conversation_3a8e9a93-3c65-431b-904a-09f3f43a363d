/*
  Warnings:

  - You are about to drop the column `name` on the `companies` table. All the data in the column will be lost.
  - You are about to drop the column `document` on the `customers` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `customers` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[razaoSocial]` on the table `companies` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[cnpj]` on the table `customers` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `razaoSocial` to the `companies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cnpj` to the `customers` table without a default value. This is not possible if the table is not empty.
  - Added the required column `razaoSocial` to the `customers` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "core"."DomainType" AS ENUM ('PRIMARY', 'SECONDARY');

-- DropIndex
DROP INDEX "core"."customers_document_idx";

-- DropIndex
DROP INDEX "core"."customers_document_key";

-- DropIndex
DROP INDEX "core"."customers_name_idx";

-- AlterTable
ALTER TABLE "core"."companies" DROP COLUMN "name",
ADD COLUMN     "razaoSocial" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "core"."customers" DROP COLUMN "document",
DROP COLUMN "name",
ADD COLUMN     "cnpj" VARCHAR(20) NOT NULL,
ADD COLUMN     "image" TEXT,
ADD COLUMN     "razaoSocial" VARCHAR(255) NOT NULL;

-- CreateTable
CREATE TABLE "core"."customer_documents" (
    "id" TEXT NOT NULL,
    "customerUuid" TEXT NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "url" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "customer_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."labels" (
    "id" SERIAL NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "idComponent" VARCHAR(100) NOT NULL,
    "modulo" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "labels_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."customer_payment_preferences" (
    "id" SERIAL NOT NULL,
    "customerUuid" TEXT NOT NULL,
    "departamentoId" INTEGER NOT NULL,
    "responsavel" VARCHAR(255) NOT NULL,
    "metodoPagamentoId" INTEGER NOT NULL,
    "condicaoPagamento" VARCHAR(255) NOT NULL,
    "preferenciaCobranca" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_payment_preferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."domains" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "customer_uuid" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "type" "core"."DomainType" NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,
    "deleted_at" TIMESTAMPTZ,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,

    CONSTRAINT "domains_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "customer_documents_customerUuid_idx" ON "core"."customer_documents"("customerUuid");

-- CreateIndex
CREATE UNIQUE INDEX "domains_uuid_key" ON "core"."domains"("uuid");

-- CreateIndex
CREATE INDEX "domains_customer_uuid_idx" ON "core"."domains"("customer_uuid");

-- CreateIndex
CREATE INDEX "domains_domain_idx" ON "core"."domains"("domain");

-- CreateIndex
CREATE UNIQUE INDEX "domains_customer_uuid_domain_key" ON "core"."domains"("customer_uuid", "domain");

-- CreateIndex
CREATE UNIQUE INDEX "companies_razaoSocial_key" ON "core"."companies"("razaoSocial");

-- CreateIndex
CREATE UNIQUE INDEX "customers_cnpj_key" ON "core"."customers"("cnpj");

-- CreateIndex
CREATE INDEX "customers_razaoSocial_idx" ON "core"."customers"("razaoSocial");

-- CreateIndex
CREATE INDEX "customers_cnpj_idx" ON "core"."customers"("cnpj");

-- AddForeignKey
ALTER TABLE "core"."customer_documents" ADD CONSTRAINT "customer_documents_customerUuid_fkey" FOREIGN KEY ("customerUuid") REFERENCES "core"."customers"("uuid") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."customer_payment_preferences" ADD CONSTRAINT "customer_payment_preferences_customerUuid_fkey" FOREIGN KEY ("customerUuid") REFERENCES "core"."customers"("uuid") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."customer_payment_preferences" ADD CONSTRAINT "customer_payment_preferences_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "core"."labels"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."customer_payment_preferences" ADD CONSTRAINT "customer_payment_preferences_metodoPagamentoId_fkey" FOREIGN KEY ("metodoPagamentoId") REFERENCES "core"."labels"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."domains" ADD CONSTRAINT "domains_customer_uuid_fkey" FOREIGN KEY ("customer_uuid") REFERENCES "core"."customers"("uuid") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."domains" ADD CONSTRAINT "domains_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "core"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."domains" ADD CONSTRAINT "domains_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "core"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
