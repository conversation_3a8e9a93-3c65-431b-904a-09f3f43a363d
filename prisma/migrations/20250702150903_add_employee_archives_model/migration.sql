-- CreateTable
CREATE TABLE "core"."archives" (
    "id" TEXT NOT NULL,
    "employee_uuid" TEXT NOT NULL,
    "uploaded_by" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "file_name" VARCHAR(255),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "archives_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "archives_employee_uuid_idx" ON "core"."archives"("employee_uuid");

-- CreateIndex
CREATE INDEX "archives_uploaded_by_idx" ON "core"."archives"("uploaded_by");

-- CreateIndex
CREATE INDEX "archives_created_at_idx" ON "core"."archives"("created_at");

-- CreateIndex
CREATE INDEX "archives_employee_uuid_deleted_at_idx" ON "core"."archives"("employee_uuid", "deleted_at");

-- AddForeignKey
ALTER TABLE "core"."archives" ADD CONSTRAINT "archives_employee_uuid_fkey" FOREIGN KEY ("employee_uuid") REFERENCES "core"."employees"("uuid") ON DELETE RESTRICT ON UPDATE CASCADE;
