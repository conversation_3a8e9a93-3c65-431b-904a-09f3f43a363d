import * as request from 'supertest';
import type { Application } from 'express';
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { AppModule } from '../../src/app.module';
import { PrismaService } from '../../src/infrastructure/prisma/prisma.service';

describe('CustomersController (e2e) - PATCH /v1/backoffice/customers/:uuid', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let customerUuid: string;
  let token: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }),
    );
    await app.init();
    prisma = app.get(PrismaService);

    // Autentica e obtém o token JWT
    const loginResponse = await request(
      app.getHttpServer() as unknown as Application,
    )
      .post('/auth/login')
      .send({ username: 'admin', password: 'admin' });
    interface LoginResponseBody {
      access_token: string;
    }
    const loginBody = loginResponse.body as LoginResponseBody;
    token = `Bearer ${loginBody.access_token}`;
  });

  beforeEach(async () => {
    const createResponse = await request(
      app.getHttpServer() as unknown as Application,
    )
      .post('/v1/backoffice/customers')
      .set('Authorization', token)
      .send({
        name: 'Old Name',
        document: '99999999999',
        email: '<EMAIL>',
      });
    interface CreateCustomerResponseBody {
      uuid: string;
    }
    const createBody = createResponse.body as CreateCustomerResponseBody;
    customerUuid = createBody.uuid;
  });

  afterEach(async () => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
    await (prisma as any).customer.deleteMany({});
  });

  afterAll(async () => {
    await app.close();
  });

  it.skip('deve atualizar um cliente com sucesso', async () => {
    const patchData = { name: 'Novo Nome', email: '<EMAIL>' };
    const response = await request(
      app.getHttpServer() as unknown as Application,
    )
      .patch(`/v1/backoffice/customers/${customerUuid}`)
      .set('Authorization', token)
      .send(patchData)
      .expect(200);
    interface PatchCustomerResponseBody {
      name: string;
      email: string;
      uuid: string;
    }
    const patchBody = response.body as PatchCustomerResponseBody;
    expect(patchBody.name).toBe('Novo Nome');
    expect(patchBody.email).toBe('<EMAIL>');
    expect(patchBody.uuid).toBe(customerUuid);
  });
});
