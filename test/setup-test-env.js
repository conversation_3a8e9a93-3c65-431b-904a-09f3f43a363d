const dotenv = require('dotenv');
const path = require('path');

// <PERSON><PERSON><PERSON> as variáveis do docker-compose.yml para simular o ambiente de teste
// Você pode criar um .env.test dedicado se precisar de valores diferentes
dotenv.config({ path: path.resolve(__dirname, '../.env.test') });

// Valores do docker-compose.yml
process.env.DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/nest_boilerplate_test?schema=public";
process.env.RABBITMQ_HOST = "localhost";
process.env.RABBITMQ_PORT = "5672";
process.env.JWT_SECRET = "test_secret";
process.env.JWT_EXPIRATION = "3600";
process.env.KEYCLOAK_BASE_URL = "http://localhost:8080";
process.env.KEYCLOAK_REALM = "master";
process.env.KEYCLOAK_CLIENT_ID = "backend-dev-client";
process.env.KEYCLOAK_CLIENT_SECRET = "myclientsecret";
process.env.KEYCLOAK_ADMIN_USERNAME = "admin";
process.env.KEYCLOAK_ADMIN_PASSWORD = "admin"; 